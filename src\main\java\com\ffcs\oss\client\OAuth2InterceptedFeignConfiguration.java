package com.ffcs.oss.client;

//import com.ffcs.oss.pto.dcoos.client.DcoosAllFeignRequestInterceptor;
//import com.ffcs.oss.pto.dcoos.properties.DcoosProperties;
//import com.ffcs.oss.pto.dcoos.service.DcoosAuthentication;
public class OAuth2InterceptedFeignConfiguration {
//
//    private final LoadBalancedResourceDetails loadBalancedResourceDetails;
//
//    private final Environment environment;
//
//    private final DcoosProperties dcoosProperties;
//
//    private final DcoosAuthentication dcoosAuthentication;
//
//    public OAuth2InterceptedFeignConfiguration(LoadBalancedResourceDetails loadBalancedResourceDetails,
//                                               Environment environment,
//                                               DcoosProperties dcoosProperties,
//                                               DcoosAuthentication dcoosAuthentication) {
//        this.loadBalancedResourceDetails = loadBalancedResourceDetails;
//        this.environment = environment;
//        this.dcoosProperties = dcoosProperties;
//        this.dcoosAuthentication = dcoosAuthentication;
//    }
//
//    @Bean(name = "oauth2RequestInterceptor")
//    public RequestInterceptor getOAuth2RequestInterceptor() {
//        return new DcoosAllFeignRequestInterceptor(new DefaultOAuth2ClientContext(),
//            loadBalancedResourceDetails,
//            environment,
//            dcoosProperties,
//            dcoosAuthentication);
//    }
}
