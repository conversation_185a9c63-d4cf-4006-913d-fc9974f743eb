package com.ffcs.oss.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ffcs.oss.domain.AiPermissionConfigD;
import com.ffcs.oss.web.rest.evt.permission.PermissionConfigEvt;
import com.ffcs.oss.web.rest.vm.permission.PermissionConfigVm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限配置Mapper接口
 */
@Mapper
public interface AiPermissionConfigMapper extends BaseMapper<AiPermissionConfigD> {
    
    /**
     * 查询权限配置列表
     *
     * @param evt 查询参数
     * @return 权限配置列表
     */
    List<PermissionConfigVm> getPermissionConfigList(PermissionConfigEvt evt);
    
    /**
     * 查询资源的创建者
     *
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @return 创建者用户名
     */
    String getResourceCreator(@Param("resourceType") Integer resourceType, @Param("resourceId") String resourceId);
    
    /**
     * 检查用户是否有资源的管理权限
     *
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param userName 用户名
     * @return 管理权限数量
     */
    int checkUserAdminPermission(@Param("resourceType") Integer resourceType, 
                                @Param("resourceId") String resourceId, 
                                @Param("userName") String userName,
                                 @Param("groupIdList")List<Long>groupIdList);
    
    /**
     * 检查用户是否有资源的使用权限
     *
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param userName 用户名
     * @return 使用权限数量
     */
    int checkUserUsePermission(@Param("resourceType") Integer resourceType, 
                              @Param("resourceId") String resourceId, 
                              @Param("userName") String userName,
                               @Param("groupIdList")List<Long>groupIdList);
} 