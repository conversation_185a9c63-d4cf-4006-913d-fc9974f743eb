package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ffcs.oss.domain.AiDbConnectionConfigD;
import com.ffcs.oss.mapper.dbconnection.DbConnectionConfigMapper;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.service.SqlExecutionService;
import com.ffcs.oss.utils.db.SqlExecutor;
import com.ffcs.oss.utils.db.SqlExecutorFactory;
import com.ffcs.oss.utils.db.SqlValidator;
import com.ffcs.oss.web.rest.evt.sqlexec.SqlExecutionEvt;
import com.ffcs.oss.web.rest.vm.sqlexec.SqlExecutionResultVm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * SQL执行服务实现类
 */
@Service
public class SqlExecutionServiceImpl implements SqlExecutionService {
    
    private static final Logger logger = LoggerFactory.getLogger(SqlExecutionServiceImpl.class);
    
    /**
     * 连接缓存，避免频繁创建连接
     * key: dbType:dataSourceName
     */
    private final Map<String, SqlExecutor> executorCache = new ConcurrentHashMap<>();
    
    @Resource
    private DbConnectionConfigMapper dbConnectionConfigMapper;
    
    @Resource
    private SqlValidator sqlValidator;
    
    @Override
    public ServiceResp executeSql(SqlExecutionEvt evt) {
        String dbType = evt.getDbType();
        String dataSourceName = evt.getDataSourceName();
        String sqlStatement = evt.getSqlStatement();
        int maxRows = evt.getMaxRows() != null ? evt.getMaxRows() : 1000;
        
        try {
            // 验证SQL是否允许执行
            String validationError = sqlValidator.validate(sqlStatement);
            if (validationError != null) {
                logger.warn("SQL验证未通过: {}, SQL: {}", validationError, sqlStatement);
                return ServiceResp.getInstance().error("SQL验证未通过: " + validationError);
            }
            
            // 获取SQL执行器
            SqlExecutor executor = getSqlExecutor(dbType, dataSourceName);
            if (executor == null) {
                return ServiceResp.getInstance().error("找不到数据源: " + dataSourceName);
            }
            
            // 执行SQL
            SqlExecutionResultVm result = executor.execute(sqlStatement, maxRows);
            
            return ServiceResp.getInstance().success(result, result.getSuccess() ? "SQL执行成功" : "SQL执行失败");
        } catch (Exception e) {
            logger.error("执行SQL语句出错", e);
            return ServiceResp.getInstance().error("执行SQL出错: " + e.getMessage());
        }
    }
    
    /**
     * 获取SQL执行器，优先从缓存中获取
     *
     * @param dbType        数据库类型
     * @param dataSourceName 数据源名称
     * @return SQL执行器
     */
    private SqlExecutor getSqlExecutor(String dbType, String dataSourceName) {
        String cacheKey = dbType + ":" + dataSourceName;
        
        // 尝试从缓存获取
        SqlExecutor executor = executorCache.get(cacheKey);
        if (executor != null) {
            try {
                // 测试连接是否有效
                if (executor.testConnection()) {
                    return executor;
                } else {
                    // 连接无效，从缓存中移除
                    executorCache.remove(cacheKey);
                }
            } catch (Exception e) {
                logger.error("测试连接出错，将重新创建连接", e);
                executorCache.remove(cacheKey);
            }
        }
        
        try {
            // 构建查询条件
            QueryWrapper<AiDbConnectionConfigD> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(AiDbConnectionConfigD::getDbType, dbType)
                    .eq(AiDbConnectionConfigD::getConnectionName, dataSourceName);
            
            // 查询连接配置
            AiDbConnectionConfigD config = dbConnectionConfigMapper.selectOne(queryWrapper);
            if (config == null) {
                logger.error("找不到数据源配置: {}", dataSourceName);
                return null;
            }
            
            // 创建SQL执行器
            executor = SqlExecutorFactory.createSqlExecutor(config);
            if (executor != null) {
                // 添加到缓存
                executorCache.put(cacheKey, executor);
            }
            
            return executor;
        } catch (Exception e) {
            logger.error("创建SQL执行器出错", e);
            return null;
        }
    }
    
    /**
     * 清除指定数据源的连接缓存
     *
     * @param dbType        数据库类型
     * @param dataSourceName 数据源名称
     */
    public void clearCache(String dbType, String dataSourceName) {
        String cacheKey = dbType + ":" + dataSourceName;
        SqlExecutor executor = executorCache.remove(cacheKey);
        if (executor != null) {
            executor.close();
        }
    }
    
    /**
     * 清除所有连接缓存
     */
    public void clearAllCache() {
        for (Map.Entry<String, SqlExecutor> entry : executorCache.entrySet()) {
            try {
                entry.getValue().close();
            } catch (Exception e) {
                logger.error("关闭连接出错: " + e.getMessage(), e);
            }
        }
        executorCache.clear();
    }
} 