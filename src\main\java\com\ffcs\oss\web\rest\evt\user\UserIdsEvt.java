package com.ffcs.oss.web.rest.evt.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2018/12/19  17:07
 */
@ApiModel("用户IDS参数")
public class UserIdsEvt implements Serializable{
    private static final long serialVersionUID = -1813993658093717558L;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty(value = "当前登录用户名",notes = "后台取")
    private String loginName;

    @ApiModelProperty("用户ID组")
    private List<Long> userIds;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

    @Override
    public String toString() {
        return "UserIdsEvt{" +
            "userId=" + userId +
            ", loginName='" + loginName + '\'' +
            '}';
    }
}
