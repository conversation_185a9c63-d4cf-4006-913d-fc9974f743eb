package com.ffcs.oss.utils;

import com.alibaba.fastjson.JSON;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.security.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @description
 * @create 2023/3/3 14:30
 */
public class TenantUtils {
    private static final Logger logger = LoggerFactory.getLogger(TenantUtils.class);
    public static final String ANONYMOUS_USER = "anonymousUser";

    public static boolean isUseTenantCode;
    public static String groupCreatorName;

    public static String getTenantCode() {
        if (isUseTenantCode) {
            logger.info("租户编码:{}-用户名:{}", PtSecurityUtils.getTenantCode(), PtSecurityUtils.getUsername());
            if (StringUtils.isBlank(PtSecurityUtils.getTenantCode())) {
                StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
                //List<StackTraceElement> stackTraceElementsList = Arrays.stream(stackTrace).filter(i -> StringUtils.startsWith(i.getClassName(), "com.ffcs.oss")).collect(Collectors.toList());
                logger.info("无租户编码:{}-用户名:{}-调用信息:{}", PtSecurityUtils.getTenantCode(), PtSecurityUtils.getUsername(), JSON.toJSONString(stackTrace));
            }
            return PtSecurityUtils.getTenantCode();
        } else {
            logger.info("租户未开启");
            return null;
        }

    }
}
