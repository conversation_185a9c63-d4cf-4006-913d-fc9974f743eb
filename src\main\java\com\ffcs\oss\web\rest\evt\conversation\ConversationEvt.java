package com.ffcs.oss.web.rest.evt.conversation;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 对话请求参数
 */
@ApiModel("对话请求参数")
public class ConversationEvt extends QueryPageEvt implements Serializable {

    @ApiModelProperty("会话id")
    private String sessionId;
    
    @ApiModelProperty("问题id")
    private String questionId;
    
    @ApiModelProperty("问题")
    private String question;
    
    @ApiModelProperty("SQL查询")
    private String sql;
    
    @ApiModelProperty("创建人")
    private String createUserName;
    @ApiModelProperty("助理id")
    private Long assistantId;

    public Long getAssistantId() {
        return assistantId;
    }

    public void setAssistantId(Long assistantId) {
        this.assistantId = assistantId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
} 