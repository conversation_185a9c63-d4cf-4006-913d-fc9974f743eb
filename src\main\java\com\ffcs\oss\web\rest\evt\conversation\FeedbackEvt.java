package com.ffcs.oss.web.rest.evt.conversation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 反馈请求参数
 */
@ApiModel("反馈请求参数")
public class FeedbackEvt implements Serializable {

    @ApiModelProperty("回答ID")
    @NotBlank(message = "回答ID不能为空")
    private String answerId;
    
    @ApiModelProperty("反馈类型：1点赞，2点踩")
    @NotBlank(message = "反馈类型不能为空")
    private String feedback;
    
    @ApiModelProperty("用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
} 