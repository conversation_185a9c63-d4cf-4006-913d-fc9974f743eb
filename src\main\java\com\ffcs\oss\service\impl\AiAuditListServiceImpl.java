package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.domain.AiAuditListD;
import com.ffcs.oss.domain.AiTodoListD;
import com.ffcs.oss.mapper.AiAuditListMapper;
import com.ffcs.oss.service.AiAuditListService;
import com.ffcs.oss.service.AiTodoListService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.evt.audit.AuditEvt;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 审核列表Service实现类
 */
@Service
public class AiAuditListServiceImpl extends ServiceImpl<AiAuditListMapper, AiAuditListD> implements AiAuditListService {

    @Autowired
    private AiTodoListService aiTodoListService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addToTodoList(AuditEvt evt) {
        // 查询审核记录
        AiAuditListD auditListD = this.getById(evt.getAuditListId());
        if (auditListD == null) {
            return false;
        }
        
        // 从审核列表中删除
        boolean removed = this.removeById(evt.getAuditListId());
        if (!removed) {
            return false;
        }
        
        // 创建待办记录
        AiTodoListD todoListD = new AiTodoListD();
        BeanUtils.copyProperties(auditListD, todoListD);
        if (StringUtils.isNotBlank(evt.getCorrectSql())){
            todoListD.setCorrectSql(evt.getCorrectSql());
        }
        if (StringUtils.isNotBlank(evt.getProfessionalKnowledge())){
            todoListD.setProfessionalKnowledge(evt.getProfessionalKnowledge());
        }
        todoListD.setTodoListId(null); // 清空ID，让数据库生成
        todoListD.setUpdateTime(LocalDateTime.now());
        
        // 保存到待办列表
        return aiTodoListService.save(todoListD);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeAudit(AiAuditListD auditListD) {
        if (auditListD == null || auditListD.getAuditListId() == null) {
            return false;
        }
        
        // 设置状态为已审核
        auditListD.setAuditStatus("1");
        auditListD.setUpdateTime(LocalDateTime.now());
        
        // 更新审核记录
        return this.updateById(auditListD);
    }
}