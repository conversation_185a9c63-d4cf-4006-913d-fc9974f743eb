# Tomcat

# Spring
spring:
  application:
    # 应用名称
    name: ai-chat-bi-web
  messages:
    #true使用本地包文件，false使用nacos配置
    localfile: true
    basename:  i18n/messages
    encoding: UTF-8
    cacheMillis: 10000
  profiles:
    active: dev
  cloud:
    nacos:
      username: nacos
      password: cloud@ffcsDb4649ffcsAfg!$^
      discovery:
        # 服务注册地址
        server-addr: 192.168.35.213:32447
        #是否启用注册到 nacos,关闭
        enabled: false
      config:
        #未用到配置中心，关闭
        enabled: false
        # 配置中心地址
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        # 国际化配置命名空间(不填默认public) 6bddda45-ab58-4ebf-87a3-cbb4c7e9a3b0
        message-nmespace: 6bddda45-ab58-4ebf-87a3-cbb4c7e9a3b0
        # 国际化配置分组(不填默认DEFAULT_GROUP)
        message-group: DEFAULT_GROUP
        # 配置文件格式
        file-extension: yml
        shared-dataids: ai-chat-bi-web.yml
        #超时
        timeout: 10000

# Eureka配置
eureka:
  client:
    enabled: false
    service-url:
      defaultZone: ******************************************************/eureka/
  instance:
    prefer-ip-address: true
    appname: ${spring.application.name}
    instanceId: ${spring.application.name}:${spring.application.instance-id:${random.value}}

# Apollo配置
app:
  id: chat-bi
apollo:
  meta: http://**************:30001/
  bootstrap:
    enabled: false
    namespaces: application






