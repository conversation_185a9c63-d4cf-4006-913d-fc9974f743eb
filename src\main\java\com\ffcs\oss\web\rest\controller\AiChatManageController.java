package com.ffcs.oss.web.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiChatManageD;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.security.SecurityUtils;import com.ffcs.oss.service.AiChatManageService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.chat.ChatManageEvt;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.ApiModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 聊天管理控制器
 */
@RestController
@RequestMapping(value = "/api/chat")
@ApiModel("聊天管理")
public class AiChatManageController {

    @Autowired
    private AiChatManageService aiChatManageService;

    /**
     * 分页查询聊天会话列表
     */
    @PostMapping("/getChatManagePage")
    public ServiceResp getChatManagePage(@RequestBody ChatManageEvt evt) {
        if (evt.getPageNo() != null && evt.getPageSize() != null && evt.isCountTotal()) {
            com.github.pagehelper.Page page = PageHelper.startPage(evt.getPageNo(), evt.getPageSize(), evt.isCountTotal());
            LambdaQueryWrapper<AiChatManageD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(StringUtils.isNotBlank(evt.getSessionName()), AiChatManageD::getSessionName, evt.getSessionName());
            queryWrapper.eq(evt.getAssistantId() != null, AiChatManageD::getAssistantId, evt.getAssistantId());
            queryWrapper.eq(StringUtils.isNotBlank(evt.getSessionId()), AiChatManageD::getSessionId, evt.getSessionId());
            queryWrapper.eq(StringUtils.isNotBlank(evt.getCreateUserName()), AiChatManageD::getCreateUserName, evt.getCreateUserName());
            queryWrapper.orderByDesc(AiChatManageD::getUpdateTime,AiChatManageD::getCreateTime);
            
            List<AiChatManageD> chatManageList = aiChatManageService.list(queryWrapper);
            return ServiceResp.getInstance().success(QueryPageVm.getInstance(evt, chatManageList, page.getTotal()),
                    LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
        } else {
            LambdaQueryWrapper<AiChatManageD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(evt.getSessionName() != null, AiChatManageD::getSessionName, evt.getSessionName());
            queryWrapper.eq(evt.getAssistantId() != null, AiChatManageD::getAssistantId, evt.getAssistantId());
            queryWrapper.eq(StringUtils.isNotBlank(evt.getSessionId()), AiChatManageD::getSessionId, evt.getSessionId());
            queryWrapper.eq(StringUtils.isNotBlank(evt.getCreateUserName()), AiChatManageD::getCreateUserName, evt.getCreateUserName());
            queryWrapper.orderByDesc(AiChatManageD::getCreateTime);
            
            List<AiChatManageD> chatManageList = aiChatManageService.list(queryWrapper);
            return ServiceResp.getInstance().success(QueryPageVm.getInstance(evt, chatManageList, 0L),
                    LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
        }
    }
    
    /**
     * 获取聊天会话列表
     */
    @PostMapping("/getChatManageList")
    public ServiceResp getChatManageList(@RequestBody ChatManageEvt evt) {
        LambdaQueryWrapper<AiChatManageD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(evt.getAssistantId() != null, AiChatManageD::getAssistantId, evt.getAssistantId());
        queryWrapper.orderByDesc(AiChatManageD::getCreateTime);
        
        List<AiChatManageD> list = aiChatManageService.list(queryWrapper);
        return ServiceResp.getInstance().success(list, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 根据ID获取聊天会话详情
     */
    @PostMapping("/getChatManageById")
    public ServiceResp getChatManageById(@RequestBody ChatManageEvt evt) {
        AiChatManageD chatManage = aiChatManageService.getById(evt.getSessionId());
        return ServiceResp.getInstance().success(chatManage, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 新增或更新聊天会话
     */
    @PostMapping("/addOrUpdateChatManage")
    public ServiceResp addOrUpdateChatManage(@RequestBody ChatManageEvt evt) {
        AiChatManageD chatManage = new AiChatManageD();
        BeanUtils.copyProperties(evt, chatManage);
        
        boolean result;
        if (chatManage.getSessionId() == null || chatManage.getSessionId().isEmpty()) {
            // 新增
            chatManage.setSessionId(UUID.randomUUID().toString().replace("-", ""));
            chatManage.setCreateTime(LocalDateTime.now());
            chatManage.setUpdateTime(LocalDateTime.now());
            chatManage.setCreateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");
            chatManage.setUpdateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");
            result = aiChatManageService.save(chatManage);
        } else {
            // 更新
            chatManage.setUpdateTime(LocalDateTime.now());
            chatManage.setUpdateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");
            result = aiChatManageService.updateById(chatManage);
        }
        
        return result ? ServiceResp.getInstance().success(chatManage, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS)) 
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    /**
     * 删除聊天会话
     */
    @PostMapping("/deleteChatManage")
    public ServiceResp deleteChatManage(@RequestBody ChatManageEvt evt) {
        boolean result = aiChatManageService.removeById(evt.getSessionId());
        return result ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS)) 
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }
} 