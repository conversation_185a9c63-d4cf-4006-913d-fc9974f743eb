package com.ffcs.oss.utils.dfs;

import org.springframework.core.io.ByteArrayResource;

/**
 * 自定义ByteArrayResource
 * 重写getFilename方法，方便在HTTP上传时设置文件名
 */
public class CommonByteArrayResource extends ByteArrayResource {

    private final String filename;

    /**
     * 构造函数
     *
     * @param byteArray 字节数组
     * @param filename  文件名
     */
    public CommonByteArrayResource(byte[] byteArray, String filename) {
        super(byteArray);
        this.filename = filename;
    }

    /**
     * 获取文件名
     *
     * @return 文件名
     */
    @Override
    public String getFilename() {
        return this.filename;
    }

    /**
     * 获取描述
     *
     * @return 资源描述
     */
    @Override
    public String getDescription() {
        return "ByteArray resource [" + this.filename + "]";
    }
} 