package com.ffcs.oss.service.impl;

import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiDataSourceConfigD;
import com.ffcs.oss.mapper.datasourceconfig.DataSourceConfigMapper;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.service.DataSourceConfigService;
import com.ffcs.oss.utils.Asserts;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.controller.DataSourceConfigController;
import com.ffcs.oss.web.rest.evt.datasourceconfig.DataSourceConfigEvt;
import com.ffcs.oss.web.rest.vm.datasourceconfig.DataSourceConfigVm;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2023/8/7 18:16
 */
@Service
public class DataSourceConfigServiceImpl implements DataSourceConfigService {
    private static final Logger logger = LoggerFactory.getLogger(DataSourceConfigController.class);
    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;


    @Override
    public QueryPageVm<DataSourceConfigVm> getDataSourceConfigList(DataSourceConfigEvt evt) {
        if (evt.getPageNo() != null && evt.getPageSize() != null && evt.isCountTotal()) {
            Page page = PageHelper.startPage(evt.getPageNo(), evt.getPageSize(), evt.isCountTotal());
            List<DataSourceConfigVm> dataSourceConfigList = dataSourceConfigMapper.getDataSourceConfigList(evt);
            return QueryPageVm.getInstance(evt, dataSourceConfigList, page.getTotal());
        } else {
            List<DataSourceConfigVm> dataSourceConfigList = dataSourceConfigMapper.getDataSourceConfigList(evt);
            return QueryPageVm.getInstance(evt, dataSourceConfigList, 0L);
        }
    }

    @Override
    public ServiceResp addOrUpdatetDataSourceConfig(DataSourceConfigEvt evt) {
        AiDataSourceConfigD aiDataSourceConfigD = new AiDataSourceConfigD();
        BeanUtils.copyProperties(evt, aiDataSourceConfigD);
        Integer addOrUpdateNum;
        if (evt.getDataSourceConfigId() == null) {
            //新增时数源名称不重复就插入
            Asserts.isTrue(dataSourceConfigMapper.nameWhetherRepeat(evt) == 0, LocalMessageTool.getMessage(LocaleKeyConstant.NAME_CANNOT_REPEATED));
            addOrUpdateNum = dataSourceConfigMapper.insert(aiDataSourceConfigD);
        } else {
            addOrUpdateNum = dataSourceConfigMapper.updateById(aiDataSourceConfigD);
        }
        return addOrUpdateNum > 0 ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    @Override
    public ServiceResp deleteDataSourceConfig(DataSourceConfigEvt evt) {
        return dataSourceConfigMapper.deleteById(evt.getDataSourceConfigId()) > 0 ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    @Override
    public ServiceResp updateDataSourceStatus(DataSourceConfigEvt evt) {
        AiDataSourceConfigD aiDataSourceConfigD = new AiDataSourceConfigD();
        aiDataSourceConfigD.setUpdateTime(new Date());
        aiDataSourceConfigD.setDataSourceConfigId(evt.getDataSourceConfigId());
        aiDataSourceConfigD.setIsOn(evt.getIsOn());
        return dataSourceConfigMapper.updateById(aiDataSourceConfigD) > 0 ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_EXCEPTION));
    }
}
