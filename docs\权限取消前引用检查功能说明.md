# 权限取消前引用检查功能说明

## 功能概述

本功能实现了在创建者分配权限界面取消某些用户的数据模型或数据源权限时，先判断这些数据模型或数据源是否被助理引用。如果被引用则不能取消权限，如果没有被引用才能取消对应的权限。

## 实现原理

### 引用关系说明

1. **助理与数据模型的引用关系**：
   - 助理表（`ai_assistant_d`）中的 `data_model_id` 字段直接引用数据模型

2. **助理与数据源的引用关系**：
   - **直接引用**：助理表（`ai_assistant_d`）中的 `connection_id` 字段直接引用数据源
   - **间接引用**：助理通过数据模型间接引用数据源
     - 助理引用数据模型（`ai_assistant_d.data_model_id`）
     - 数据模型引用数据源（`ai_data_model_d.connection_id`）

### 核心实现

#### 1. 数据模型引用检查

**服务接口**：`AiDataModelService.checkModelReferenceByAssistant(String dataModelId)`

**实现逻辑**：
```java
public boolean checkModelReferenceByAssistant(String dataModelId) {
    LambdaQueryWrapper<AiAssistantD> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(AiAssistantD::getDataModelId, dataModelId);
    return aiAssistantMapper.selectCount(queryWrapper) > 0;
}
```

#### 2. 数据源引用检查

**服务接口**：`DbConnectionService.checkConnectionReferenceByAssistant(String connectionId)`

**实现逻辑**：
```java
public boolean checkConnectionReferenceByAssistant(String connectionId) {
    // 1. 检查助理是否直接引用该数据源
    LambdaQueryWrapper<AiAssistantD> assistantQuery = new LambdaQueryWrapper<>();
    assistantQuery.eq(AiAssistantD::getConnectionId, Long.valueOf(connectionId));
    if (aiAssistantMapper.selectCount(assistantQuery) > 0) {
        return true;
    }
    
    // 2. 检查是否有数据模型引用该数据源，且该数据模型被助理引用
    LambdaQueryWrapper<AiDataModelD> dataModelQuery = new LambdaQueryWrapper<>();
    dataModelQuery.eq(AiDataModelD::getConnectionId, Long.valueOf(connectionId));
    List<AiDataModelD> dataModels = aiDataModelMapper.selectList(dataModelQuery);
    
    for (AiDataModelD dataModel : dataModels) {
        LambdaQueryWrapper<AiAssistantD> assistantByModelQuery = new LambdaQueryWrapper<>();
        assistantByModelQuery.eq(AiAssistantD::getDataModelId, dataModel.getDataModelId());
        if (aiAssistantMapper.selectCount(assistantByModelQuery) > 0) {
            return true;
        }
    }
    
    return false;
}
```

#### 3. 权限删除前检查

**核心方法**：`AiPermissionConfigService.deletePermissionConfigWithCascade(PermissionConfigEvt evt)`

**增强逻辑**：
```java
public ServiceResp deletePermissionConfigWithCascade(PermissionConfigEvt evt) {
    AiPermissionConfigD permission = this.getById(evt.getPermissionId());
    if (permission == null) {
        return ServiceResp.getInstance().error("权限配置不存在");
    }

    // 如果是数据模型权限，检查是否被助理引用
    if (permission.getResourceType().equals(CommonConstant.RESOURCE_TYPE_DATA_MODEL)) {
        boolean isReferenced = aiDataModelService.checkModelReferenceByAssistant(permission.getResourceId());
        if (isReferenced) {
            return ServiceResp.getInstance().error("该数据模型正在被助理引用，无法取消权限");
        }
    }
    
    // 如果是数据源权限，检查是否被助理引用
    if (permission.getResourceType().equals(DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION)) {
        boolean isReferenced = dbConnectionService.checkConnectionReferenceByAssistant(permission.getResourceId());
        if (isReferenced) {
            return ServiceResp.getInstance().error("该数据源正在被助理引用，无法取消权限");
        }
    }

    // 执行原有的权限删除逻辑
    return deletePermissionConfig(evt);
}
```

## 资源类型常量

- **数据模型**：`CommonConstant.RESOURCE_TYPE_DATA_MODEL = 1`
- **助理**：`CommonConstant.RESOURCE_TYPE_ASSISTANT = 2`
- **数据源**：`DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION = 3`

## API接口

### 删除权限配置（带级联处理）

**接口地址**：`POST /api/permission/deletePermissionConfig`

**请求参数**：
```json
{
    "permissionId": 123
}
```

**响应示例**：

成功删除：
```json
{
    "success": true,
    "message": "删除成功",
    "body": null
}
```

数据模型被引用：
```json
{
    "success": false,
    "message": "该数据模型正在被助理引用，无法取消权限",
    "body": null
}
```

数据源被引用：
```json
{
    "success": false,
    "message": "该数据源正在被助理引用，无法取消权限",
    "body": null
}
```

## 使用场景

1. **创建者在权限管理界面取消用户的数据模型权限**：
   - 系统会检查该数据模型是否被任何助理引用
   - 如果被引用，显示错误信息，阻止取消操作
   - 如果未被引用，允许取消权限

2. **创建者在权限管理界面取消用户的数据源权限**：
   - 系统会检查该数据源是否被任何助理直接或间接引用
   - 如果被引用，显示错误信息，阻止取消操作
   - 如果未被引用，允许取消权限

3. **取消助理权限**：
   - 不进行引用检查，直接执行删除操作
   - 删除成功后会自动清理不再需要的关联资源权限

## 注意事项

1. **事务处理**：权限删除操作使用了 `@Transactional` 注解，确保数据一致性
2. **错误处理**：引用检查过程中的异常会被捕获并记录日志，返回 false 以确保系统稳定性
3. **性能考虑**：使用 MyBatis Plus 的 LambdaQueryWrapper 进行高效查询
4. **日志记录**：关键操作都有详细的日志记录，便于问题排查

## 测试验证

项目中包含了完整的单元测试：
- `PermissionReferenceCheckTest`：测试权限删除前的引用检查逻辑
- `DbConnectionReferenceCheckTest`：测试数据源引用检查的各种场景

可以通过运行测试用例来验证功能的正确性。
