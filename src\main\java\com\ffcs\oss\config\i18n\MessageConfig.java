package com.ffcs.oss.config.i18n;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @ClassName MessageConfig
 * @Description 国际化配置
 * <AUTHOR>
 * @Date 2022/8/23 11:28
 */

@RefreshScope
@Component
@ConfigurationProperties(prefix = "spring.messages")
public class MessageConfig {

    /**
     * 是否使用本地配置文件
     */
    private Boolean localfile = Boolean.TRUE;

    /**
     * 国际化文件名称
     */
    private String basename = "i18n/messages";

    /**
     * 国际化编码
     */
    private String encoding = "UTF-8";

    /**
     * 缓存刷新时间
     */
    private long cacheMillis = 10000;

    public Boolean getLocalfile() {
        return localfile;
    }

    public void setLocalfile(Boolean localfile) {
        this.localfile = localfile;
    }

    public String getBasename() {
        return basename;
    }

    public void setBasename(String basename) {
        this.basename = basename;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public long getCacheMillis() {
        return cacheMillis;
    }

    public void setCacheMillis(long cacheMillis) {
        this.cacheMillis = cacheMillis;
    }

    @Override
    public String toString() {
        return "MessageConfig{" +
                "localfile=" + localfile +
                ", basename='" + basename + '\'' +
                ", encoding='" + encoding + '\'' +
                ", cacheMillis=" + cacheMillis +
                '}';
    }
}
