# SQL执行器使用示例

本文档提供了如何使用SQL执行器在不同数据源执行SQL的示例。

## 1. 配置数据源

首先，需要在系统中配置数据源。系统支持以下类型的数据源：

- MySQL
- PostgreSQL
- Elasticsearch
- Hive

### 1.1 配置文件

在`application.yml`中可以配置Hive的Kerberos认证和Elasticsearch的版本：

```yaml
# 数据源配置
datasource:
  # Hive配置
  hive:
    # 是否启用Kerberos认证
    kerberos-enabled: true
    # Kerberos principal
    principal: hive/<EMAIL>
    # Kerberos keytab文件路径
    keytab-path: /etc/kerberos/hive.keytab
  
  # Elasticsearch配置
  elasticsearch:
    # Elasticsearch版本，支持7或8
    version: 7
```

### 1.2 添加数据源

使用以下接口添加数据源：

```
POST /api/dbConnection/addOrUpdateDbConnection
```

请求体示例（添加Elasticsearch数据源）：

```json
{
  "connectionName": "ES集群",
  "dbType": "elasticsearch",
  "host": "*************",
  "port": 9200,
  "username": "elastic",
  "password": "password",
  "creatorName": "admin"
}
```

请求体示例（添加Hive数据源）：

```json
{
  "connectionName": "Hive集群",
  "dbType": "hive",
  "host": "*************",
  "port": 10000,
  "databaseName": "default",
  "username": "hive",
  "password": "password",
  "params": "hive.server2.transport.mode=http",
  "creatorName": "admin"
}
```

## 2. 执行SQL

使用以下接口在指定数据源中执行SQL：

```
POST /api/sql/execute
```

### 2.1 Elasticsearch SQL查询

```json
{
  "dbType": "elasticsearch",
  "dataSourceName": "ES集群",
  "sqlStatement": "SELECT * FROM my_index LIMIT 10",
  "maxRows": 100
}
```

### 2.2 Elasticsearch DSL查询

```json
{
  "dbType": "elasticsearch",
  "dataSourceName": "ES集群",
  "sqlStatement": "{\"query\": {\"match_all\": {}}, \"size\": 10}",
  "maxRows": 100
}
```

带索引名的DSL查询：

```json
{
  "dbType": "elasticsearch",
  "dataSourceName": "ES集群",
  "sqlStatement": "{\"_endpoint\": \"/my_index/_search\", \"query\": {\"match\": {\"field\": \"value\"}}, \"size\": 10}",
  "maxRows": 100
}
```

### 2.3 Hive SQL查询

```json
{
  "dbType": "hive",
  "dataSourceName": "Hive集群",
  "sqlStatement": "SELECT * FROM my_table LIMIT 10",
  "maxRows": 100
}
```

## 3. 测试连接

使用以下接口测试数据源连接：

```
POST /api/dbConnection/testDbConnection
```

请求体示例：

```json
{
  "connectionId": 1,
  "dbType": "elasticsearch",
  "host": "*************",
  "port": 9200,
  "username": "elastic",
  "password": "password"
}
```

## 4. 注意事项

1. **Elasticsearch版本**：系统支持Elasticsearch 7.x和8.x版本，通过配置文件中的`datasource.elasticsearch.version`设置。
   
2. **Elasticsearch DSL查询**：如果SQL语句以`{`开头，则会被识别为DSL查询。可以使用`_endpoint`字段指定查询路径。

3. **Hive Kerberos认证**：如果启用了Kerberos认证，需要在配置文件中设置`datasource.hive.kerberos-enabled=true`，并提供principal和keytab文件路径。

4. **密码安全**：数据库中存储的密码会自动加密，执行SQL时会自动解密。密码不会返回给前端。 