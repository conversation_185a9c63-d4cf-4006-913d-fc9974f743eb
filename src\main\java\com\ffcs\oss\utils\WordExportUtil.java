package com.ffcs.oss.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.io.File;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Word导出工具类
 */
public class WordExportUtil {

    private static final Logger log = LoggerFactory.getLogger(WordExportUtil.class);
    
    // 静态初始化块，在类加载时执行
    static {
        initTempDirectories();
    }
    
    /**
     * 初始化临时目录
     */
    private static void initTempDirectories() {
        try {
            // 检查系统临时目录
            String systemTempDir = System.getProperty("java.io.tmpdir");
            log.info("系统临时目录: {}", systemTempDir);
            
            File systemTempFile = new File(systemTempDir);
            if (!systemTempFile.exists()) {
                boolean created = systemTempFile.mkdirs();
                log.info("创建系统临时目录: {}, 结果: {}", systemTempDir, created);
            }
            
            // 检查自定义临时目录
            String customTempDir = "./temp";
            File customTempFile = new File(customTempDir);
            if (!customTempFile.exists()) {
                boolean created = customTempFile.mkdirs();
                log.info("创建自定义临时目录: {}, 结果: {}", customTempDir, created);
            }
            
            // 检查权限
            log.info("系统临时目录可写: {}", systemTempFile.canWrite());
            log.info("自定义临时目录可写: {}", customTempFile.canWrite());
        } catch (Exception e) {
            log.error("初始化临时目录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建文档
     * @return XWPFDocument对象
     */
    public static XWPFDocument createDocument() {
        return new XWPFDocument();
    }
    
    /**
     * 添加标题
     * @param document XWPFDocument对象
     * @param text 标题文本
     * @param level 标题级别（1-9）
     * @return XWPFParagraph对象
     */
    public static XWPFParagraph addTitle(XWPFDocument document, String text, int level) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setStyle("Heading" + level);
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setBold(true);
        run.setFontSize(20 - (level * 2)); // 根据级别调整字体大小
        return paragraph;
    }
    
    /**
     * 添加段落
     * @param document XWPFDocument对象
     * @param text 段落文本
     * @return XWPFParagraph对象
     */
    public static XWPFParagraph addParagraph(XWPFDocument document, String text) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setFontSize(12);
        return paragraph;
    }
    
    /**
     * 添加代码块
     * @param document XWPFDocument对象
     * @param code 代码文本
     * @return XWPFParagraph对象
     */
    public static XWPFParagraph addCodeBlock(XWPFDocument document, String code) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setIndentationLeft(400); // 缩进
        paragraph.setIndentationRight(400); // 缩进
        
        CTShd shd = paragraph.getCTP().addNewPPr().addNewShd();
        shd.setVal(STShd.CLEAR);
        shd.setColor("auto");
        shd.setFill("F5F5F5"); // 浅灰色背景
        
        XWPFRun run = paragraph.createRun();
        run.setText(code);
        run.setFontFamily("Courier New");
        run.setFontSize(10);
        return paragraph;
    }
    
    /**
     * 添加表格
     * @param document XWPFDocument对象
     * @param data 表格数据（第一行为表头）
     * @return XWPFTable对象
     */
    public static XWPFTable addTable(XWPFDocument document, String[][] data) {
        if (data == null || data.length == 0 || data[0].length == 0) {
            return null;
        }
        
        int rowCount = data.length;
        int colCount = data[0].length;
        
        XWPFTable table = document.createTable(rowCount, colCount);
        
        // 设置表格宽度
        CTTblWidth width = table.getCTTbl().addNewTblPr().addNewTblW();
        width.setType(STTblWidth.DXA);
        width.setW(BigInteger.valueOf(9000)); // 表格宽度
        
        // 填充表格数据
        for (int i = 0; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            for (int j = 0; j < colCount; j++) {
                XWPFTableCell cell = row.getCell(j);
                
                // 设置单元格背景色（表头使用灰色背景）
                if (i == 0) {
                    CTShd shd = cell.getCTTc().addNewTcPr().addNewShd();
                    shd.setVal(STShd.CLEAR);
                    shd.setColor("auto");
                    shd.setFill("DDDDDD"); // 灰色背景
                }
                
                // 添加文本
                XWPFParagraph paragraph = cell.getParagraphArray(0);
                if (paragraph == null) {
                    paragraph = cell.addParagraph();
                }
                XWPFRun run = paragraph.createRun();
                run.setText(data[i][j]);
                
                // 表头文字加粗
                if (i == 0) {
                    run.setBold(true);
                }
            }
        }
        
        return table;
    }
    
    /**
     * 添加图片
     * @param document XWPFDocument对象
     * @param imgData 图片数据
     * @param width 宽度（像素）
     * @param height 高度（像素）
     * @return XWPFParagraph对象
     */
    public static XWPFParagraph addImage(XWPFDocument document, byte[] imgData, int width, int height) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        
        try {
            XWPFRun run = paragraph.createRun();
            InputStream is = new ByteArrayInputStream(imgData);
            run.addPicture(
                    is,
                    Document.PICTURE_TYPE_PNG, // 可以根据实际情况修改图片类型
                    "Image",
                    width * 9525, // 转换为EMU单位
                    height * 9525 // 转换为EMU单位
            );
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return paragraph;
    }
    
    /**
     * 添加Base64编码的图片
     * @param document XWPFDocument对象
     * @param base64Image Base64编码的图片数据
     * @param width 宽度（像素）
     * @param height 高度（像素）
     * @return XWPFParagraph对象
     */
    public static XWPFParagraph addBase64Image(XWPFDocument document, String base64Image, int width, int height) {
        if (base64Image == null || base64Image.isEmpty()) {
            return null;
        }
        
        // 移除可能的前缀，如 "data:image/png;base64,"
        if (base64Image.contains(",")) {
            base64Image = base64Image.split(",")[1];
        }
        
        try {
            byte[] imageData = Base64.getDecoder().decode(base64Image);
            return addImage(document, imageData, width, height);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 将查询结果添加为Excel附件并在Word中创建链接
     * @param document XWPFDocument对象
     * @param queryResult 查询结果数据
     * @param fileName Excel文件名
     * @return 是否成功
     */
    public static boolean addExcelAttachment(XWPFDocument document, List<Map<String, Object>> queryResult, String fileName) {
        if (queryResult == null || queryResult.isEmpty()) {
            return false;
        }
        
        try {
            // 获取临时目录路径
            String tempDir = getWorkingTempDir();
            if (tempDir == null) {
                log.error("无法获取可用的临时目录");
                return false;
            }
            
            String excelFilePath = tempDir + "/" + fileName;
            log.info("Excel文件路径: {}", excelFilePath);
            
            // 检测是否在容器环境中
            boolean isContainer = new File("/.dockerenv").exists() || 
                new File("/proc/1/cgroup").exists() && 
                containsDocker(new File("/proc/1/cgroup")) || 
                System.getenv("DOCKER_CONTAINER") != null;
            
            log.info("当前环境: {}", isContainer ? "容器环境" : "标准环境");
            
            // 统一使用CSV格式，不再区分环境
            boolean success = createMinimalExcelFile(queryResult, excelFilePath);
            if (!success) {
                log.error("创建CSV文件失败");
                return false;
            }
            
            // 在Word中添加文件说明
            XWPFParagraph paragraph = document.createParagraph();
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun run = paragraph.createRun();
            
            // 统一使用CSV文件名
            run.setText("SQL查询结果已导出为CSV文件：" + fileName.replace(".xlsx", ".csv"));
            run.setBold(true);
            run.setFontSize(12);
            
            // 添加说明文本
            XWPFParagraph noteParagraph = document.createParagraph();
            XWPFRun noteRun = noteParagraph.createRun();
            
            noteRun.setText("注：完整数据（" + queryResult.size() + " 条记录）已导出为CSV文件，在ZIP包中与Word文档一起返回。");
            noteRun.setItalic(true);
            noteRun.setFontSize(10);
            
            // 在所有环境中都添加CSV文件使用指导
            XWPFParagraph guideParagraph = document.createParagraph();
            XWPFRun guideRun = guideParagraph.createRun();
            guideRun.setText("CSV文件使用指导：CSV文件可以用Excel、WPS表格或任何文本编辑器打开。如果数据显示不正确，请在Excel中通过数据-自文本功能导入，并选择逗号作为分隔符。");
            guideRun.setItalic(true);
            guideRun.setColor("FF0000"); // 红色
            guideRun.setFontSize(10);
            
            return true;
        } catch (Exception e) {
            log.error("生成Excel附件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取可用的临时目录
     * @return 可用的临时目录路径
     */
    private static String getWorkingTempDir() {
        // 检查系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        log.info("系统临时目录: {}", tempDir);
        
        // 确保临时目录存在
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists()) {
            boolean dirCreated = tempDirFile.mkdirs();
            if (!dirCreated) {
                log.error("无法创建临时目录: {}", tempDir);
                
                // 尝试使用当前目录作为备用
                tempDir = "./temp";
                tempDirFile = new File(tempDir);
                if (!tempDirFile.exists()) {
                    boolean backupDirCreated = tempDirFile.mkdirs();
                    if (!backupDirCreated) {
                        log.error("无法创建备用临时目录: {}", tempDir);
                        return null;
                    }
                }
                log.info("使用备用临时目录: {}", tempDir);
            }
        }
        
        // 检查临时目录是否可写
        if (!tempDirFile.canWrite()) {
            log.error("临时目录不可写: {}", tempDir);
            
            // 尝试使用当前目录作为备用
            tempDir = "./temp";
            tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                boolean dirCreated = tempDirFile.mkdirs();
                if (!dirCreated) {
                    log.error("无法创建备用临时目录: {}", tempDir);
                    return null;
                }
            }
            
            if (!tempDirFile.canWrite()) {
                log.error("备用临时目录也不可写: {}", tempDir);
                return null;
            }
            
            log.info("使用备用临时目录: {}", tempDir);
        }
        
        return tempDir;
    }
    
    /**
     * 创建Excel文件（无AWT依赖）
     * 已废弃，由createMinimalExcelFile方法取代
     * @param queryResult 查询结果
     * @param excelFilePath Excel文件路径
     * @return 是否成功
     * @deprecated 由于统一使用CSV格式，此方法已废弃
     */
    @Deprecated
    private static boolean createExcelFileWithoutAWT(List<Map<String, Object>> queryResult, String excelFilePath) {
        try {
            // 检测是否在容器环境中 - 先尝试检查一些容器特定的文件
            boolean isContainer = new File("/.dockerenv").exists() || 
                new File("/proc/1/cgroup").exists() && 
                containsDocker(new File("/proc/1/cgroup")) || 
                System.getenv("DOCKER_CONTAINER") != null;
            
            // 如果在容器环境中或者使用了Docker环境变量
            if (isContainer) {
                log.info("检测到容器环境，使用简化版Excel生成策略");
                return createMinimalExcelFile(queryResult, excelFilePath);
            }
            
            // 以下是普通环境下的Excel生成逻辑（尽量减少AWT依赖）
            log.info("使用标准Excel生成策略");
            
            // 通过系统属性完全禁用AWT，避免任何可能的AWT调用
            System.setProperty("java.awt.headless", "true");
            System.setProperty("sun.java2d.nofonts", "true");
            
            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("查询结果");
            
            // 获取所有的列名
            Map<String, Object> firstRow = queryResult.get(0);
            String[] columns = firstRow.keySet().toArray(new String[0]);
            
            // 创建标题行（使用简单的CellStyle，避免AWT调用）
            Row headerRow = sheet.createRow(0);
            CellStyle headerStyle = workbook.createCellStyle();
            // 不使用字体设置，避免AWT调用
            // Font headerFont = workbook.createFont();
            // headerFont.setBold(true);
            // headerStyle.setFont(headerFont);
            
            for (int i = 0; i < columns.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(columns[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 填充数据行
            for (int i = 0; i < queryResult.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Map<String, Object> rowData = queryResult.get(i);
                
                for (int j = 0; j < columns.length; j++) {
                    Cell cell = row.createCell(j);
                    Object value = rowData.get(columns[j]);
                    if (value != null) {
                        cell.setCellValue(value.toString());
                    }
                }
            }
            
            // 手动设置固定列宽，不使用任何AWT相关功能
            for (int i = 0; i < columns.length; i++) {
                // 固定列宽为20个字符
                sheet.setColumnWidth(i, 20 * 256);
            }
            
            // 保存Excel文件
            File excelFile = new File(excelFilePath);
            try (FileOutputStream fos = new FileOutputStream(excelFile)) {
                workbook.write(fos);
            }
            workbook.close();
            
            // 验证文件是否创建成功
            if (!excelFile.exists()) {
                log.error("Excel文件未成功创建: {}", excelFilePath);
                return false;
            }
            
            log.info("Excel文件创建成功，大小: {}字节", excelFile.length());
            return true;
        } catch (Exception e) {
            log.error("创建Excel文件失败: {}", e.getMessage(), e);
            
            // 出错时尝试使用最简化的方式创建文件
            log.info("尝试使用备用方法创建简化版Excel文件");
            return createMinimalExcelFile(queryResult, excelFilePath);
        }
    }
    
    /**
     * 创建极简版Excel文件（完全避免AWT依赖）
     * 这个方法只用于在容器环境中，不使用任何可能触发AWT的POI功能
     */
    private static boolean createMinimalExcelFile(List<Map<String, Object>> queryResult, String excelFilePath) {
        FileOutputStream fos = null;
        
        try {
            // 将路径从.xlsx改为.csv
            String csvFilePath = excelFilePath.replace(".xlsx", ".csv");
            
            log.info("在容器环境中创建CSV文件作为Excel替代: {}", csvFilePath);
            
            // 使用最简单的方法创建CSV格式文件作为备选
            if (new File(csvFilePath).exists()) {
                new File(csvFilePath).delete();
            }
            
            fos = new FileOutputStream(csvFilePath);
            
            // 获取所有列名
            if (queryResult == null || queryResult.isEmpty()) {
                return false;
            }
            
            Map<String, Object> firstRow = queryResult.get(0);
            String[] columns = firstRow.keySet().toArray(new String[0]);
            
            // 写入表头
            StringBuilder headerLine = new StringBuilder();
            for (int i = 0; i < columns.length; i++) {
                if (i > 0) {
                    headerLine.append(",");
                }
                headerLine.append(escapeCSV(columns[i]));
            }
            headerLine.append("\r\n");
            fos.write(headerLine.toString().getBytes("UTF-8"));
            
            // 写入数据行
            for (Map<String, Object> row : queryResult) {
                StringBuilder dataLine = new StringBuilder();
                for (int i = 0; i < columns.length; i++) {
                    if (i > 0) {
                        dataLine.append(",");
                    }
                    Object value = row.get(columns[i]);
                    dataLine.append(escapeCSV(value != null ? value.toString() : ""));
                }
                dataLine.append("\r\n");
                fos.write(dataLine.toString().getBytes("UTF-8"));
            }
            
            fos.flush();
            log.info("成功创建CSV文件: {}", csvFilePath);
            
            // 不再创建空的xlsx文件
            // 清理可能存在的旧xlsx文件
            File oldExcelFile = new File(excelFilePath);
            if (oldExcelFile.exists()) {
                boolean deleted = oldExcelFile.delete();
                if (deleted) {
                    log.info("删除了旧的Excel文件: {}", excelFilePath);
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("创建简化版Excel文件失败: {}", e.getMessage(), e);
            return false;
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.error("关闭文件流失败", e);
                }
            }
        }
    }

    /**
     * 转义CSV字段中的特殊字符
     */
    private static String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        // 如果包含逗号、引号或换行，则用引号括起来，并将引号变成两个引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }
    
    /**
     * 检查文件内容是否包含Docker相关信息
     */
    private static boolean containsDocker(File file) {
        try {
            List<String> lines = java.nio.file.Files.readAllLines(file.toPath());
            for (String line : lines) {
                if (line.contains("docker") || line.contains("kubepods")) {
                    return true;
                }
            }
        } catch (IOException e) {
            // 忽略错误
        }
        return false;
    }
    
    /**
     * 保存文档到字节数组
     * @param document XWPFDocument对象
     * @return 字节数组
     */
    public static byte[] saveToByteArray(XWPFDocument document) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            document.write(out);
            return out.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 保存文档到文件
     * @param document XWPFDocument对象
     * @param filePath 文件路径
     * @return 是否成功
     */
    public static boolean saveToFile(XWPFDocument document, String filePath) {
        try (FileOutputStream out = new FileOutputStream(filePath)) {
            document.write(out);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }
} 