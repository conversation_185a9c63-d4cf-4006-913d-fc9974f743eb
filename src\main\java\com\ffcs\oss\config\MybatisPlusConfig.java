package com.ffcs.oss.config;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisPlusConfig {
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        // 设置数据库类型（MySQL、Oracle等）
        paginationInterceptor.setDialectType("postgresql"); // 旧版用字符串，新版用 DbType.MYSQL
        return paginationInterceptor;
    }
}
