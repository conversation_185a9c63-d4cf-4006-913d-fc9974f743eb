package com.ffcs.oss.web.rest.vm.datasourceconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @create 2023/8/7 18:23
 */
@ApiModel("数源返参")
public class DataSourceConfigVm implements Serializable {
    @ApiModelProperty("主键")
    private BigInteger dataSourceConfigId;
    @ApiModelProperty("数源名称")
    @NotBlank
    private String name;
    @ApiModelProperty("数源全称")
    @NotBlank
    private String fullName;
    @ApiModelProperty("数源枚举：“CCTV”，“people”，“BBC”，“CNN”")
    @NotBlank
    private String key;
    @ApiModelProperty("cron表达式")
    @NotBlank
    private String cronExpression;
    @ApiModelProperty("数源类型：1、国际 2、国内")
    @NotNull
    private Integer type;
    @ApiModelProperty("是否开启：1、开启 0、关闭")
    private Integer isOn;
    @ApiModelProperty("正文")
    private String content;
    @ApiModelProperty("创建人姓名")
    private String creatorName;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private String updaterName;
    @ApiModelProperty("更新时间")
    private Date updateTime;

    public BigInteger getDataSourceConfigId() {
        return dataSourceConfigId;
    }

    public void setDataSourceConfigId(BigInteger dataSourceConfigId) {
        this.dataSourceConfigId = dataSourceConfigId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsOn() {
        return isOn;
    }

    public void setIsOn(Integer isOn) {
        this.isOn = isOn;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
