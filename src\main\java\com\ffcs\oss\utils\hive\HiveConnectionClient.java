package com.ffcs.oss.utils.hive;

import com.ffcs.oss.web.rest.constant.DbConnectionConstant;
import com.ffcs.oss.domain.AiDbConnectionConfigD;
import com.ffcs.oss.utils.FileStorageUtil;
import com.ffcs.oss.utils.dfs.CtdfsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Hive连接客户端
 * 用于封装Hive连接的相关操作
 */
@Component
public class HiveConnectionClient {
    
    private static final Logger logger = LoggerFactory.getLogger(HiveConnectionClient.class);
    
    private static CtdfsService ctdfsService;
    
    @Autowired
    public void setCtdfsService(CtdfsService ctdfsService) {
        HiveConnectionClient.ctdfsService = ctdfsService;
    }
    
    /**
     * 安全获取CtdfsService
     * 如果服务未初始化，则记录日志并返回null
     * 
     * @return CtdfsService或null
     */
    private static CtdfsService getCtdfsService() {
        if (ctdfsService == null) {
            logger.warn("小文件系统服务未初始化，将无法访问小文件系统文件");
            return null;
        }
        return ctdfsService;
    }
    
    /**
     * 测试Hive连接
     * 
     * @param jdbcUrl JDBC URL
     * @param username 用户名
     * @param password 密码
     * @param principal Kerberos principal
     * @param keytabFile Kerberos keytab文件
     * @param krb5ConfFile krb5.conf文件
     * @param keytabFilePath keytab文件路径
     * @param krb5ConfFilePath krb5.conf文件路径
     * @return 连接是否成功
     */
    public static boolean testConnection(String jdbcUrl, String username, String password, 
                                        String principal, MultipartFile keytabFile, 
                                        MultipartFile krb5ConfFile, String keytabFilePath, 
                                        String krb5ConfFilePath) {
        HiveConnectionParam connectionParam = new HiveConnectionParam();
        connectionParam.setJdbcUrl(jdbcUrl);
        connectionParam.setUsername(username);
        connectionParam.setPassword(password);
        
        boolean kerberosEnabled = principal != null && !principal.isEmpty();
        connectionParam.setKerberosEnabled(kerberosEnabled);
        
        if (kerberosEnabled) {
            connectionParam.setLoginUserKeytabUsername(principal);
            
            // 处理keytab文件
            if (keytabFile != null && !keytabFile.isEmpty()) {
                try {
                    String tempKeytabPath = FileStorageUtil.createTempFile("keytab", keytabFile.getBytes());
                    connectionParam.setLoginUserKeytabPath(tempKeytabPath);
                } catch (IOException e) {
                    logger.error("处理keytab文件失败", e);
                    return false;
                }
            } else if (keytabFilePath != null && !keytabFilePath.isEmpty()) {
                // 检查是否为小文件系统路径
                if (keytabFilePath.contains("/hive_files/")) {
                    // 从小文件系统下载到临时文件
                    CtdfsService service = getCtdfsService();
                    if (service != null) {
                        try (InputStream inputStream = service.downloadStream(keytabFilePath)) {
                            if (inputStream != null) {
                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                byte[] buffer = new byte[1024];
                                int len;
                                while ((len = inputStream.read(buffer)) != -1) {
                                    baos.write(buffer, 0, len);
                                }
                                String tempKeytabPath = FileStorageUtil.createTempFile("keytab", baos.toByteArray());
                                connectionParam.setLoginUserKeytabPath(tempKeytabPath);
                            } else {
                                logger.error("无法从小文件系统读取keytab文件: {}", keytabFilePath);
                                return false;
                            }
                        } catch (Exception e) {
                            logger.error("处理小文件系统keytab文件失败", e);
                            return false;
                        }
                    } else {
                        logger.error("小文件系统服务未初始化，无法读取keytab文件: {}", keytabFilePath);
                        return false;
                    }
                } else {
                    // 本地文件系统路径
                    connectionParam.setLoginUserKeytabPath(keytabFilePath);
                }
            }
            
            // 处理krb5.conf文件
            if (krb5ConfFile != null && !krb5ConfFile.isEmpty()) {
                try {
                    String tempKrb5ConfPath = FileStorageUtil.createTempFile("krb5.conf", krb5ConfFile.getBytes());
                    connectionParam.setJavaSecurityKrb5Conf(tempKrb5ConfPath);
                } catch (IOException e) {
                    logger.error("处理krb5.conf文件失败", e);
                    return false;
                }
            } else if (krb5ConfFilePath != null && !krb5ConfFilePath.isEmpty()) {
                // 检查是否为小文件系统路径
                if (krb5ConfFilePath.contains("/hive_files/")) {
                    // 从小文件系统下载到临时文件
                    CtdfsService service = getCtdfsService();
                    if (service != null) {
                        try (InputStream inputStream = service.downloadStream(krb5ConfFilePath)) {
                            if (inputStream != null) {
                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                byte[] buffer = new byte[1024];
                                int len;
                                while ((len = inputStream.read(buffer)) != -1) {
                                    baos.write(buffer, 0, len);
                                }
                                String tempKrb5ConfPath = FileStorageUtil.createTempFile("krb5.conf", baos.toByteArray());
                                connectionParam.setJavaSecurityKrb5Conf(tempKrb5ConfPath);
                            } else {
                                logger.error("无法从小文件系统读取krb5.conf文件: {}", krb5ConfFilePath);
                                return false;
                            }
                        } catch (Exception e) {
                            logger.error("处理小文件系统krb5.conf文件失败", e);
                            return false;
                        }
                    } else {
                        logger.error("小文件系统服务未初始化，无法读取krb5.conf文件: {}", krb5ConfFilePath);
                        return false;
                    }
                } else {
                    // 本地文件系统路径
                    connectionParam.setJavaSecurityKrb5Conf(krb5ConfFilePath);
                }
            }
        }
        
        try {
            HiveClient client = new HiveClient(connectionParam);
            boolean connected = client.testConnection();
            client.close();
            return connected;
        } catch (Exception e) {
            logger.error("连接Hive失败", e);
            return false;
        }
    }
    
    /**
     * 根据数据库连接配置创建HiveClient
     * 
     * @param config 数据库连接配置
     * @return HiveClient实例
     */
    public static HiveClient createHiveClient(AiDbConnectionConfigD config) {
        if (config == null) {
            throw new IllegalArgumentException("数据库连接配置不能为空");
        }
        
        HiveConnectionParam connectionParam = new HiveConnectionParam();
        connectionParam.setJdbcUrl(config.getUrl());
        connectionParam.setUsername(config.getUsername());
        connectionParam.setPassword(config.getPassword());
        
        boolean kerberosEnabled = config.getAuthType() != null && 
                config.getAuthType().equals(DbConnectionConstant.AUTH_TYPE_KERBEROS);
        connectionParam.setKerberosEnabled(kerberosEnabled);
        
        if (kerberosEnabled) {
            connectionParam.setLoginUserKeytabUsername(config.getPrincipal());
            
            // 优先使用文件路径
            if (config.getKeytabFilePath() != null && !config.getKeytabFilePath().isEmpty()) {
                // 检查是否为小文件系统路径
                if (config.getKeytabFilePath().contains("/hive_files/")) {
                    // 从小文件系统下载到临时文件
                    CtdfsService service = getCtdfsService();
                    if (service != null) {
                        try (InputStream inputStream = service.downloadStream(config.getKeytabFilePath())) {
                            if (inputStream != null) {
                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                byte[] buffer = new byte[1024];
                                int len;
                                while ((len = inputStream.read(buffer)) != -1) {
                                    baos.write(buffer, 0, len);
                                }
                                String tempKeytabPath = FileStorageUtil.createTempFile(
                                        "keytab", baos.toByteArray(), config.getConnectionId().toString());
                                connectionParam.setLoginUserKeytabPath(tempKeytabPath);
                            } else {
                                logger.error("无法从小文件系统读取keytab文件: {}", config.getKeytabFilePath());
                            }
                        } catch (Exception e) {
                            logger.error("处理小文件系统keytab文件失败", e);
                        }
                    } else {
                        logger.error("小文件系统服务未初始化，无法读取keytab文件: {}", config.getKeytabFilePath());
                    }
                } else if (Files.exists(Paths.get(config.getKeytabFilePath()))) {
                    // 本地文件系统路径
                    connectionParam.setLoginUserKeytabPath(config.getKeytabFilePath());
                } else {
                    logger.warn("keytab文件不存在: {}", config.getKeytabFilePath());
                }
            } 
            // 如果没有文件路径，或者文件不存在，则使用文件内容创建临时文件
            else if (config.getKeytabFile() != null && config.getKeytabFile().length > 0) {
                try {
                    String tempKeytabPath = FileStorageUtil.createTempFile(
                            "keytab", config.getKeytabFile(), config.getConnectionId().toString());
                    connectionParam.setLoginUserKeytabPath(tempKeytabPath);
                } catch (IOException e) {
                    logger.error("处理keytab文件失败", e);
                }
            }
            
            // 处理krb5.conf文件
            if (config.getKrb5ConfFilePath() != null && !config.getKrb5ConfFilePath().isEmpty()) {
                // 检查是否为小文件系统路径
                if (config.getKrb5ConfFilePath().contains("/hive_files/")) {
                    // 从小文件系统下载到临时文件
                    CtdfsService service = getCtdfsService();
                    if (service != null) {
                        try (InputStream inputStream = service.downloadStream(config.getKrb5ConfFilePath())) {
                            if (inputStream != null) {
                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                byte[] buffer = new byte[1024];
                                int len;
                                while ((len = inputStream.read(buffer)) != -1) {
                                    baos.write(buffer, 0, len);
                                }
                                String tempKrb5ConfPath = FileStorageUtil.createTempFile(
                                        "krb5.conf", baos.toByteArray(), config.getConnectionId().toString());
                                connectionParam.setJavaSecurityKrb5Conf(tempKrb5ConfPath);
                            } else {
                                logger.error("无法从小文件系统读取krb5.conf文件: {}", config.getKrb5ConfFilePath());
                            }
                        } catch (Exception e) {
                            logger.error("处理小文件系统krb5.conf文件失败", e);
                        }
                    } else {
                        logger.error("小文件系统服务未初始化，无法读取krb5.conf文件: {}", config.getKrb5ConfFilePath());
                    }
                } else if (Files.exists(Paths.get(config.getKrb5ConfFilePath()))) {
                    // 本地文件系统路径
                    connectionParam.setJavaSecurityKrb5Conf(config.getKrb5ConfFilePath());
                } else {
                    logger.warn("krb5.conf文件不存在: {}", config.getKrb5ConfFilePath());
                }
            } 
            // 如果没有文件路径，或者文件不存在，则使用文件内容创建临时文件
            else if (config.getKrb5ConfFile() != null && config.getKrb5ConfFile().length > 0) {
                try {
                    String tempKrb5ConfPath = FileStorageUtil.createTempFile(
                            "krb5.conf", config.getKrb5ConfFile(), config.getConnectionId().toString());
                    connectionParam.setJavaSecurityKrb5Conf(tempKrb5ConfPath);
                } catch (IOException e) {
                    logger.error("处理krb5.conf文件失败", e);
                }
            }
        }
        
        return new HiveClient(connectionParam);
    }
    
    /**
     * 根据数据库连接ID创建HiveClient
     * 
     * @param connectionId 数据库连接ID
     * @param dbConnectionRepository 数据库连接仓库
     * @return HiveClient实例
     */
    public static HiveClient createHiveClient(BigInteger connectionId, 
                                             com.ffcs.oss.mapper.dbconnection.DbConnectionConfigMapper dbConnectionRepository) {
        AiDbConnectionConfigD config = dbConnectionRepository.selectById(connectionId);
        if (config == null) {
            throw new IllegalArgumentException("找不到指定的数据库连接: " + connectionId);
        }
        
        return createHiveClient(config);
    }
} 