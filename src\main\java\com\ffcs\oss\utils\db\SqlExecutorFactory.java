package com.ffcs.oss.utils.db;

import com.ffcs.oss.config.DataSourceConfig;
import com.ffcs.oss.domain.AiDbConnectionConfigD;
import com.ffcs.oss.service.impl.DbConnectionServiceImpl;
import com.ffcs.oss.utils.JasyptEncryptorUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * SQL执行器工厂类
 */
@Component
public class SqlExecutorFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(SqlExecutorFactory.class);
    
    /**
     * 支持的数据库类型常量
     */
    public static final String DB_TYPE_MYSQL = "mysql";
    public static final String DB_TYPE_POSTGRESQL = "postgresql";
    public static final String DB_TYPE_ELASTICSEARCH = "elasticsearch";
    public static final String DB_TYPE_HIVE = "hive";
    
    private static DataSourceConfig dataSourceConfig;
    
    @Autowired
    public SqlExecutorFactory(DataSourceConfig dataSourceConfig) {
        SqlExecutorFactory.dataSourceConfig = dataSourceConfig;
    }
    
    /**
     * 根据数据库连接配置创建相应的SQL执行器
     *
     * @param config 数据库连接配置
     * @return SQL执行器
     */
    public static SqlExecutor createSqlExecutor(AiDbConnectionConfigD config) {
        String dbType = config.getDbType();
        String dbVersion = config.getDbVersion();
        String authType = config.getAuthType();
        String host = config.getHost();
        Integer port = config.getPort();
        String database = config.getDatabaseName();
        String username = config.getUsername();
        
        // 使用专用的ENC格式解密工具解密密码
        String password = null;
        if (config.getPassword() != null && !config.getPassword().isEmpty()) {
            password = JasyptEncryptorUtils.decrypt(config.getPassword());
        }
        
        String params = config.getParams();
        
        // 处理特殊认证类型
        if (DbConnectionServiceImpl.AUTH_TYPE_KERBEROS.equals(authType) && DB_TYPE_HIVE.equals(dbType)) {
            // Kerberos认证的Hive连接
            return new HiveExecutor(
                    host, port, database, username, password, params,
                    true, config.getPrincipal(), null,
                    config.getKeytabFile(), config.getKrb5ConfFile()
            );
        }
        
        // 其他普通连接
        return createSqlExecutor(dbType, host, port, database, username, password, params, dbVersion);
    }
    
    /**
     * 根据数据库类型和连接信息创建SQL执行器
     *
     * @param dbType   数据库类型
     * @param host     主机地址
     * @param port     端口号
     * @param database 数据库名
     * @param username 用户名
     * @param password 密码
     * @param params   其他参数
     * @return SQL执行器
     */
    public static SqlExecutor createSqlExecutor(String dbType, String host, Integer port, String database, String username, String password, String params) {
        return createSqlExecutor(dbType, host, port, database, username, password, params, null);
    }
    
    /**
     * 根据数据库类型、版本和连接信息创建SQL执行器
     *
     * @param dbType    数据库类型
     * @param host      主机地址
     * @param port      端口号
     * @param database  数据库名
     * @param username  用户名
     * @param password  密码
     * @param params    其他参数
     * @param dbVersion 数据库版本
     * @return SQL执行器
     */
    public static SqlExecutor createSqlExecutor(String dbType, String host, Integer port, String database, String username, String password, String params, String dbVersion) {
        switch (dbType.toLowerCase()) {
            case DB_TYPE_MYSQL:
                return new MySqlExecutor(host, port, database, username, password, buildMySqlParams(params, dbVersion));
                
            case DB_TYPE_POSTGRESQL:
                return new PostgreSqlExecutor(host, port, database, username, password, params);
                
            case DB_TYPE_ELASTICSEARCH:
                // 获取Elasticsearch版本配置
                String esVersion = dataSourceConfig != null ? dataSourceConfig.getElasticsearch().getVersion() : "7";
                return new ElasticsearchExecutor(host, port, database, username, password, params, esVersion);
                
            case DB_TYPE_HIVE:
                // 获取Hive Kerberos配置
                boolean kerberosEnabled = false;
                String kerberosPrincipal = null;
                String kerberosKeytabPath = null;
                
                if (dataSourceConfig != null && dataSourceConfig.getHive().isKerberosEnabled()) {
                    kerberosEnabled = true;
                    kerberosPrincipal = dataSourceConfig.getHive().getPrincipal();
                    kerberosKeytabPath = dataSourceConfig.getHive().getKeytabPath();
                }
                
                return new HiveExecutor(host, port, database, username, password, params, 
                        kerberosEnabled, kerberosPrincipal, kerberosKeytabPath);
                
            default:
                throw new UnsupportedOperationException("不支持的数据库类型: " + dbType);
        }
    }
    
    /**
     * 构建MySQL连接参数，根据版本添加适当的参数
     *
     * @param params    原始参数
     * @param dbVersion 数据库版本
     * @return 处理后的参数
     */
    private static String buildMySqlParams(String params, String dbVersion) {
        StringBuilder paramsBuilder = new StringBuilder();
        
        // 添加基本参数
        if (params != null && !params.isEmpty()) {
            paramsBuilder.append(params);
        }
        
        // 根据版本添加特定参数
        if ("5.7".equals(dbVersion)) {
            // MySQL 5.7特定参数
            if (paramsBuilder.length() > 0 && !paramsBuilder.toString().contains("useSSL=")) {
                paramsBuilder.append(paramsBuilder.length() > 0 && !params.endsWith("&") ? "&" : "").append("useSSL=false");
            }
        } else if ("8.0".equals(dbVersion)) {
            // MySQL 8.0特定参数
            if (paramsBuilder.length() > 0 && !paramsBuilder.toString().contains("allowPublicKeyRetrieval=")) {
                paramsBuilder.append(paramsBuilder.length() > 0 && !params.endsWith("&") ? "&" : "").append("allowPublicKeyRetrieval=true");
            }
        }
        
        // 添加通用参数
        if (paramsBuilder.length() > 0 && !paramsBuilder.toString().contains("serverTimezone=")) {
            paramsBuilder.append(paramsBuilder.length() > 0 && !params.endsWith("&") ? "&" : "").append("serverTimezone=UTC");
        }
        
        return paramsBuilder.toString();
    }
} 