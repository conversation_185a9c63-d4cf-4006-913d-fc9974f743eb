package com.ffcs.oss.utils.db;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * SQL验证工具类
 * 用于验证SQL是否允许执行
 */
@Component
public class SqlValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(SqlValidator.class);
    
    /**
     * SQL类型正则表达式
     */
    private static final Pattern SELECT_PATTERN = Pattern.compile("^\\s*SELECT\\s+.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern INSERT_PATTERN = Pattern.compile("^\\s*INSERT\\s+.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern UPDATE_PATTERN = Pattern.compile("^\\s*UPDATE\\s+.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    private static final Pattern DELETE_PATTERN = Pattern.compile("^\\s*DELETE\\s+.*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    
    /**
     * 是否只允许执行SELECT查询
     */
    @Value("${sql-execution.select-only:true}")
    private boolean selectOnly;
    
    /**
     * 禁止执行的SQL关键字列表
     */
    @Value("${sql-execution.forbidden-keywords:}")
    private List<String> forbiddenKeywords = new ArrayList<>();
    
    /**
     * 禁止执行的SELECT查询列表
     */
    @Value("${sql-execution.forbidden-selects:}")
    private List<String> forbiddenSelects = new ArrayList<>();
    
    /**
     * 禁止关键字的正则表达式列表
     */
    private List<Pattern> forbiddenKeywordPatterns = new ArrayList<>();
    
    /**
     * 禁止SELECT的正则表达式列表
     */
    private List<Pattern> forbiddenSelectPatterns = new ArrayList<>();
    
    /**
     * 初始化方法，将配置的SQL限制转换为正则表达式
     */
    @PostConstruct
    public void init() {
        // 转换禁止关键字为正则表达式
        for (String keyword : forbiddenKeywords) {
            forbiddenKeywordPatterns.add(
                    Pattern.compile(".*" + Pattern.quote(keyword) + ".*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL)
            );
        }
        
        // 转换禁止SELECT为正则表达式
        for (String select : forbiddenSelects) {
            forbiddenSelectPatterns.add(
                    Pattern.compile(".*" + Pattern.quote(select) + ".*", Pattern.CASE_INSENSITIVE | Pattern.DOTALL)
            );
        }
        
        logger.info("SQL验证器初始化完成，selectOnly={}, 禁止关键字数量={}, 禁止SELECT数量={}", 
                selectOnly, forbiddenKeywordPatterns.size(), forbiddenSelectPatterns.size());
    }
    
    /**
     * 验证SQL是否允许执行
     *
     * @param sql SQL语句
     * @return 如果允许执行返回null，否则返回错误消息
     */
    public String validate(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return "SQL语句不能为空";
        }
        
        // 1. 检查是否只允许SELECT查询
        if (selectOnly) {
            if (!isSelectQuery(sql)) {
                return "只允许执行SELECT类型的SQL查询";
            }
        }
        
        // 2. 检查是否包含禁止的关键字
        for (Pattern pattern : forbiddenKeywordPatterns) {
            if (pattern.matcher(sql).matches()) {
                return "SQL包含禁止的关键字";
            }
        }
        
        // 3. 如果是SELECT查询，检查是否包含禁止的SELECT
        if (isSelectQuery(sql)) {
            for (Pattern pattern : forbiddenSelectPatterns) {
                if (pattern.matcher(sql).matches()) {
                    return "包含禁止的SELECT查询";
                }
            }
        }
        
        return null; // 验证通过
    }
    
    /**
     * 判断SQL是否为SELECT查询
     *
     * @param sql SQL语句
     * @return 是否为SELECT查询
     */
    public boolean isSelectQuery(String sql) {
        return SELECT_PATTERN.matcher(sql).matches() && 
                !INSERT_PATTERN.matcher(sql).matches() && 
                !UPDATE_PATTERN.matcher(sql).matches() && 
                !DELETE_PATTERN.matcher(sql).matches();
    }
} 