package com.ffcs.oss.web.rest.evt.datasourceconfig;

import com.ffcs.oss.param.in.QueryPageEvt;
import com.ffcs.oss.web.rest.valid.DataSourceConfigDeleteValid;
import com.ffcs.oss.web.rest.valid.DataSourceConfigValid;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @description
 * @create 2023/8/7 17:08
 */
@ApiModel("数源配置")
public class DataSourceConfigEvt extends QueryPageEvt implements Serializable {
    @ApiModelProperty("主键")
    @NotNull(groups = DataSourceConfigDeleteValid.class,message = "id cannot null")
    private BigInteger dataSourceConfigId;
    @ApiModelProperty("数源名称")
    @NotBlank(groups = {DataSourceConfigValid.class},message = "name cannot null")
    private String name;
    @ApiModelProperty("数源全称")
    @NotBlank(groups = DataSourceConfigValid.class,message = "fullName cannot null")
    private String fullName;
    @ApiModelProperty("数源枚举：“CCTV”，“people”，“BBC”，“CNN”")
    @NotBlank(groups = DataSourceConfigValid.class,message = "key cannot null")
    private String key;
    @ApiModelProperty("cron表达式")
    @NotBlank(groups = DataSourceConfigValid.class,message = "cronExpression cannot null")
    private String cronExpression;
    @ApiModelProperty("数源类型：1、国际 2、国内")
    @NotNull(groups = DataSourceConfigValid.class,message = "type cannot null")
    private Integer type;
    @ApiModelProperty("是否开启：1、开启 0、关闭")
    @NotNull(groups = DataSourceConfigValid.class,message = "isOn cannot null")
    private Integer isOn;
    @ApiModelProperty("正文")
    private String content;
    @ApiModelProperty("模糊搜索")
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public BigInteger getDataSourceConfigId() {
        return dataSourceConfigId;
    }

    public void setDataSourceConfigId(BigInteger dataSourceConfigId) {
        this.dataSourceConfigId = dataSourceConfigId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsOn() {
        return isOn;
    }

    public void setIsOn(Integer isOn) {
        this.isOn = isOn;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
