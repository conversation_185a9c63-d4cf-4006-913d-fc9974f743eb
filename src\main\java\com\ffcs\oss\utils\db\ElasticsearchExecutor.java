package com.ffcs.oss.utils.db;

import com.ffcs.oss.web.rest.vm.sqlexec.SqlExecutionResultVm;
import com.ffcs.oss.web.rest.vm.sqlexec.SqlExecutionResultVm.ColumnInfo;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * Elasticsearch SQL执行器实现
 */
public class ElasticsearchExecutor extends AbstractSqlExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchExecutor.class);
    
    private final String host;
    private final int port;
    private final String username;
    private final String password;
    private final String params;
    private final String esVersion;
    
    private RestClient restClient;
    
    /**
     * Elasticsearch DSL查询正则表达式
     * 以 { 开头的查询被识别为DSL查询
     */
    private static final Pattern DSL_PATTERN = Pattern.compile("^\\s*\\{", Pattern.DOTALL);
    
    public ElasticsearchExecutor(String host, int port, String database, String username, String password, String params) {
        this(host, port, database, username, password, params, "7");
    }
    
    public ElasticsearchExecutor(String host, int port, String database, String username, String password, String params, String esVersion) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        this.params = params;
        this.esVersion = esVersion != null && !esVersion.isEmpty() ? esVersion : "7";
    }
    
    @Override
    protected Connection createConnection() throws SQLException {
        // Elasticsearch不使用JDBC连接，而是使用REST客户端
        // 为了兼容AbstractSqlExecutor，返回null
        return null;
    }
    
    private synchronized RestClient getRestClient() {
        if (restClient == null) {
            try {
                if (username != null && !username.isEmpty() && password != null && !password.isEmpty()) {
                    // 带认证的连接
                    final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    credentialsProvider.setCredentials(AuthScope.ANY,
                            new UsernamePasswordCredentials(username, password));
                    
                    restClient = RestClient.builder(new HttpHost(host, port, "http"))
                            .setHttpClientConfigCallback(httpClientBuilder -> 
                                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider))
                            .build();
                } else {
                    // 不带认证的连接
                    restClient = RestClient.builder(new HttpHost(host, port, "http")).build();
                }
            } catch (Exception e) {
                logger.error("创建Elasticsearch REST客户端失败: " + e.getMessage(), e);
                throw new RuntimeException("创建Elasticsearch连接失败", e);
            }
        }
        return restClient;
    }
    
    @Override
    public SqlExecutionResultVm execute(String sql, int maxRows) {
        SqlExecutionResultVm result = new SqlExecutionResultVm();
        result.setSuccess(false);
        
        long startTime = System.currentTimeMillis();
        
        try {
            RestClient client = getRestClient();
            
            // 判断是SQL查询还是DSL查询
            if (isDSLQuery(sql)) {
                return executeDSL(client, sql, maxRows, result, startTime);
            } else {
                return executeSQL(client, sql, maxRows, result, startTime);
            }
        } catch (Exception e) {
            logger.error("执行Elasticsearch查询出错: " + e.getMessage(), e);
            result.setErrorMessage(e.getMessage());
            result.setExecutionTime(System.currentTimeMillis() - startTime);
            return result;
        }
    }
    
    /**
     * 执行Elasticsearch SQL查询
     */
    private SqlExecutionResultVm executeSQL(RestClient client, String sql, int maxRows, SqlExecutionResultVm result, long startTime) throws Exception {
        // 获取SQL API接口路径（根据版本不同）
        String sqlEndpoint = "/_sql";
        if ("8".equals(esVersion)) {
            sqlEndpoint = "/_sql";
        }
        
        // 创建SQL请求
        Request request = new Request("POST", sqlEndpoint);
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("query", sql);
        jsonBody.put("fetch_size", maxRows);
        
        request.setJsonEntity(jsonBody.toJSONString());
        
        // 执行请求
        Response response = client.performRequest(request);
        
        // 解析响应
        String responseBody = new Scanner(response.getEntity().getContent(), "UTF-8").useDelimiter("\\A").next();
        JSONObject jsonResponse = JSON.parseObject(responseBody);
        
        // 设置列信息
        List<ColumnInfo> columns = new ArrayList<>();
        JSONArray columnsArray = jsonResponse.getJSONArray("columns");
        for (int i = 0; i < columnsArray.size(); i++) {
            JSONObject columnObj = columnsArray.getJSONObject(i);
            columns.add(new ColumnInfo(
                    columnObj.getString("name"),
                    columnObj.getString("type")
            ));
        }
        result.setColumns(columns);
        
        // 设置数据行
        List<Map<String, Object>> rows = new ArrayList<>();
        JSONArray rowsArray = jsonResponse.getJSONArray("rows");
        for (int i = 0; i < rowsArray.size(); i++) {
            JSONArray rowValues = rowsArray.getJSONArray(i);
            Map<String, Object> row = new LinkedHashMap<>();
            for (int j = 0; j < columns.size(); j++) {
                row.put(columns.get(j).getName(), rowValues.get(j));
            }
            rows.add(row);
        }
        result.setRows(rows);
        result.setSuccess(true);
        result.setExecutionTime(System.currentTimeMillis() - startTime);
        
        return result;
    }
    
    /**
     * 执行Elasticsearch DSL查询
     */
    private SqlExecutionResultVm executeDSL(RestClient client, String dsl, int maxRows, SqlExecutionResultVm result, long startTime) throws Exception {
        // 创建DSL请求
        // 默认使用_search接口
        String endpoint = "/_search";
        
        // 处理自定义接口 - 如果DSL中包含了完整路径如"/my_index/_search"，则使用该路径
        if (dsl.trim().startsWith("{")) {
            // 尝试提取JSON中的特定属性以判断请求类型
            JSONObject dslJson = JSON.parseObject(dsl);
            if (dslJson.containsKey("_endpoint")) {
                endpoint = dslJson.getString("_endpoint");
                dslJson.remove("_endpoint");
                dsl = dslJson.toJSONString();
            }
        }
        
        Request request = new Request("POST", endpoint);
        request.setJsonEntity(dsl);
        
        // 执行请求
        Response response = client.performRequest(request);
        
        // 解析响应
        String responseBody = new Scanner(response.getEntity().getContent(), "UTF-8").useDelimiter("\\A").next();
        JSONObject jsonResponse = JSON.parseObject(responseBody);
        
        // 解析hits结果
        JSONObject hits = jsonResponse.getJSONObject("hits");
        if (hits != null) {
            // 获取命中的文档
            JSONArray hitsArray = hits.getJSONArray("hits");
            
            if (hitsArray != null && !hitsArray.isEmpty()) {
                List<Map<String, Object>> rows = new ArrayList<>();
                
                // 从第一个命中文档提取字段信息创建列
                List<ColumnInfo> columns = new ArrayList<>();
                JSONObject firstHit = hitsArray.getJSONObject(0);
                JSONObject source = firstHit.getJSONObject("_source");
                
                if (source != null) {
                    // 添加_id字段
                    columns.add(new ColumnInfo("_id", "keyword"));
                    
                    // 添加source中的字段
                    for (String key : source.keySet()) {
                        // 根据值类型判断列类型
                        String type = determineColumnType(source.get(key));
                        columns.add(new ColumnInfo(key, type));
                    }
                    
                    // 处理所有文档
                    for (int i = 0; i < hitsArray.size() && i < maxRows; i++) {
                        JSONObject hit = hitsArray.getJSONObject(i);
                        Map<String, Object> row = new LinkedHashMap<>();
                        
                        // 添加_id字段
                        row.put("_id", hit.getString("_id"));
                        
                        // 添加source中的字段
                        JSONObject hitSource = hit.getJSONObject("_source");
                        if (hitSource != null) {
                            for (ColumnInfo column : columns) {
                                if (!"_id".equals(column.getName())) {
                                    row.put(column.getName(), hitSource.get(column.getName()));
                                }
                            }
                        }
                        
                        rows.add(row);
                    }
                    
                    result.setColumns(columns);
                    result.setRows(rows);
                    result.setSuccess(true);
                }
            }
        }
        
        // 如果没有hits结构，返回原始的响应体
        if (result.getColumns() == null || result.getColumns().isEmpty()) {
            List<ColumnInfo> columns = new ArrayList<>();
            columns.add(new ColumnInfo("response", "object"));
            
            List<Map<String, Object>> rows = new ArrayList<>();
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("response", responseBody);
            rows.add(row);
            
            result.setColumns(columns);
            result.setRows(rows);
            result.setSuccess(true);
        }
        
        result.setExecutionTime(System.currentTimeMillis() - startTime);
        return result;
    }
    
    /**
     * 判断是否是DSL查询
     */
    private boolean isDSLQuery(String query) {
        if (query == null || query.trim().isEmpty()) {
            return false;
        }
        
        Matcher matcher = DSL_PATTERN.matcher(query.trim());
        return matcher.find();
    }
    
    /**
     * 根据值类型确定列类型
     */
    private String determineColumnType(Object value) {
        if (value == null) {
            return "null";
        } else if (value instanceof Number) {
            if (value instanceof Integer || value instanceof Long) {
                return "long";
            } else {
                return "double";
            }
        } else if (value instanceof Boolean) {
            return "boolean";
        } else if (value instanceof String) {
            return "keyword";
        } else if (value instanceof Map || value instanceof JSONObject) {
            return "object";
        } else if (value instanceof Collection || value instanceof JSONArray) {
            return "array";
        } else {
            return "keyword";
        }
    }
    
    @Override
    public boolean testConnection() {
        try {
            RestClient client = getRestClient();
            Request request = new Request("GET", "/");
            Response response = client.performRequest(request);
            return response.getStatusLine().getStatusCode() == 200;
        } catch (Exception e) {
            logger.error("测试Elasticsearch连接失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public void close() {
        if (restClient != null) {
            try {
                restClient.close();
                restClient = null;
            } catch (Exception e) {
                logger.error("关闭Elasticsearch连接出错: " + e.getMessage(), e);
            }
        }
    }
} 