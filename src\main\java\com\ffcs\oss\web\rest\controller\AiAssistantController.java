package com.ffcs.oss.web.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.domain.AiAssistantD;
import com.ffcs.oss.domain.AiPermissionConfigD;
import com.ffcs.oss.security.SecurityUtils;
import com.ffcs.oss.service.AiAssistantService;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.service.AiPermissionConfigService;
import com.ffcs.oss.service.UserService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.constant.CommonConstant;
import com.ffcs.oss.web.rest.constant.DbConnectionConstant;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.assistant.AssistantEvt;
import com.ffcs.oss.web.rest.vm.dbconnection.DbConnectionConfigVm;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 助理控制器
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/assistant")
@ApiModel("助理管理")
public class AiAssistantController {

    private final AiAssistantService aiAssistantService;
    
    @Autowired
    private AiPermissionConfigService permissionConfigService;
    
    @Autowired
    private UserService userService;

    public AiAssistantController(AiAssistantService aiAssistantService) {
        this.aiAssistantService = aiAssistantService;
    }

    /**
     * 分页查询助理列表
     */
    @PostMapping("/getAssistantPage")
    public ServiceResp getAssistantPage(@RequestBody AssistantEvt evt) {
        if (evt.getPageNo() != null && evt.getPageSize() != null && evt.isCountTotal()) {
            com.github.pagehelper.Page page = PageHelper.startPage(evt.getPageNo(), evt.getPageSize(), evt.isCountTotal());
            LambdaQueryWrapper<AiAssistantD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like( StringUtils.isNotBlank(evt.getAssistantName()), AiAssistantD::getAssistantName, evt.getAssistantName());
            queryWrapper.eq(StringUtils.isNotBlank(evt.getDataModelId()), AiAssistantD::getDataModelId, evt.getDataModelId())
                            .eq(evt.getAssistantId()!=null, AiAssistantD::getAssistantId, evt.getAssistantId())
                            .eq(StringUtils.isNotBlank(evt.getCreateUserName()), AiAssistantD::getCreateUserName, evt.getCreateUserName());
            queryWrapper.orderByDesc(AiAssistantD::getCreateTime);

            List<AiAssistantD> assistantList = aiAssistantService.list(queryWrapper);
            // 获取当前用户
            String currentUserName = userService.getCurrentUserName();
            assistantList.removeIf(aiAssistantD -> !permissionConfigService.hasUsePermission(
                    CommonConstant.RESOURCE_TYPE_ASSISTANT,
                    String.valueOf(aiAssistantD.getAssistantId()),
                    currentUserName
            ));
            return ServiceResp.getInstance().success(QueryPageVm.getInstance(evt, assistantList, page.getTotal()),
                    LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
        } else {
            LambdaQueryWrapper<AiAssistantD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(evt.getAssistantName() != null, AiAssistantD::getAssistantName, evt.getAssistantName());
            queryWrapper.eq(StringUtils.isNotBlank(evt.getDataModelId()), AiAssistantD::getDataModelId, evt.getDataModelId())
                    .eq(evt.getAssistantId()!=null, AiAssistantD::getAssistantId, evt.getAssistantId())
                    .eq(StringUtils.isNotBlank(evt.getCreateUserName()), AiAssistantD::getCreateUserName, evt.getCreateUserName());
            queryWrapper.orderByDesc(AiAssistantD::getCreateTime);

            List<AiAssistantD> assistantList = aiAssistantService.list(queryWrapper);
            // 获取当前用户
            String currentUserName = userService.getCurrentUserName();
            assistantList.removeIf(aiAssistantD -> !permissionConfigService.hasUsePermission(
                    CommonConstant.RESOURCE_TYPE_ASSISTANT,
                    String.valueOf(aiAssistantD.getAssistantId()),
                    currentUserName
            ));
            return ServiceResp.getInstance().success(QueryPageVm.getInstance(evt, assistantList, 0L),
                    LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
        }
    }

    /**
     * 获取助理列表
     */
    @PostMapping("/getAssistantList")
    public ServiceResp getAssistantList() {
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 查询当前用户有权限的助理
        LambdaQueryWrapper<AiAssistantD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(AiAssistantD::getCreateTime);
        
        List<AiAssistantD> list = aiAssistantService.list(queryWrapper);
        list.removeIf(aiAssistantD -> !permissionConfigService.hasUsePermission(
                CommonConstant.RESOURCE_TYPE_ASSISTANT,
                String.valueOf(aiAssistantD.getAssistantId()),
                currentUserName
        ));

        return ServiceResp.getInstance().success(list, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 根据ID获取助理详情
     */
    @PostMapping("/getAssistantById")
    public ServiceResp getAssistantById(@RequestBody AssistantEvt evt) {
        AiAssistantD assistant = aiAssistantService.getById(evt.getAssistantId());
        if (assistant == null) {
            return ServiceResp.getInstance().error("助理不存在");
        }
        
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 检查当前用户是否有权限查看该助理
        if (!permissionConfigService.hasUsePermission(2, String.valueOf(assistant.getAssistantId()), currentUserName)) {
            return ServiceResp.getInstance().error("您没有权限查看该助理");
        }
        
        return ServiceResp.getInstance().success(assistant, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 新增或更新助理
     */
    @PostMapping("/addOrUpdateAssistant")
    public ServiceResp addOrUpdateAssistant(@RequestBody AssistantEvt evt) {
        AiAssistantD assistant = new AiAssistantD();
        BeanUtils.copyProperties(evt, assistant);
        
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 检查助理名称是否已存在
        Long excludeAssistantId = assistant.getAssistantId() == null || assistant.getAssistantId() <= 0 ? null : assistant.getAssistantId();
        if (aiAssistantService.checkAssistantNameExists(assistant.getAssistantName(), excludeAssistantId)) {
            return ServiceResp.getInstance().error("助理名称已存在，请更换名称");
        }
        
        boolean result;
        if (assistant.getAssistantId() == null || assistant.getAssistantId() <= 0) {
            // 新增
            assistant.setCreateTime(LocalDateTime.now());
            assistant.setUpdateTime(LocalDateTime.now());
            assistant.setCreateUserName(currentUserName);
            assistant.setUpdateUserName(currentUserName);
            result = aiAssistantService.save(assistant);
            
            // 为创建者添加管理权限
            if (result) {
                // 添加助理的管理权限
                permissionConfigService.addCreatorAdminPermission(2, String.valueOf(assistant.getAssistantId()), currentUserName);

                // 自动分配关联的数据模型和数据源权限
                List<String> userIdList = Arrays.asList(currentUserName);
                List<String> userNameList = Arrays.asList(currentUserName);
                ServiceResp cascadeResult = permissionConfigService.autoAssignRelatedPermissions(
                    String.valueOf(assistant.getAssistantId()),
                    1, // 管理员权限
                    1, // 用户类型
                    userIdList,
                    userNameList,
                    currentUserName
                );

                // 记录级联权限分配结果
                if (!cascadeResult.isSuccess()) {
                    log.error("助理创建成功，但关联资源权限自动分配失败: {}", cascadeResult.getBody());
                }
            }
        } else {
            // 更新
            // 检查当前用户是否有权限修改该助理
            if (!permissionConfigService.hasAdminPermission(2, String.valueOf(assistant.getAssistantId()), currentUserName)) {
                return ServiceResp.getInstance().error("您没有权限修改该助理");
            }

            // 获取修改前的助理信息，用于权限重新分配
            AiAssistantD oldAssistant = aiAssistantService.getById(assistant.getAssistantId());
            if (oldAssistant == null) {
                return ServiceResp.getInstance().error("助理不存在");
            }

            assistant.setUpdateTime(LocalDateTime.now());
            assistant.setUpdateUserName(currentUserName);
            result = aiAssistantService.updateById(assistant);

            // 如果更新成功，处理权限重新分配
            if (result) {
                ServiceResp permissionResult = permissionConfigService.handleAssistantUpdatePermissions(
                    String.valueOf(assistant.getAssistantId()),
                    oldAssistant,
                    assistant,
                    currentUserName
                );

                // 记录权限重新分配结果
                if (!permissionResult.isSuccess()) {
                    log.warn("助理更新成功，但权限重新分配失败: assistantId={}, error={}",
                            assistant.getAssistantId(), permissionResult.getBody());
                } else {
                    log.info("助理更新成功，权限重新分配完成: assistantId={}, result={}",
                            assistant.getAssistantId(), permissionResult.getBody());
                }
            }
        }
        
        return result ? ServiceResp.getInstance().success(assistant, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS)) 
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    /**
     * 删除助理
     */
    @PostMapping("/deleteAssistant")
    public ServiceResp deleteAssistant(@RequestBody AssistantEvt evt) {
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();

        // 检查当前用户是否有权限删除该助理
        if (!permissionConfigService.hasAdminPermission(2, String.valueOf(evt.getAssistantId()), currentUserName)) {
            return ServiceResp.getInstance().error("您没有权限删除该助理");
        }

        // 在删除助理之前，先获取所有相关的权限配置，以便后续清理关联权限
        LambdaQueryWrapper<AiPermissionConfigD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiPermissionConfigD::getResourceType, CommonConstant.RESOURCE_TYPE_ASSISTANT)
                .eq(AiPermissionConfigD::getResourceId, String.valueOf(evt.getAssistantId()));

        List<AiPermissionConfigD> assistantPermissions = permissionConfigService.list(queryWrapper);

        // 删除助理
        boolean result = aiAssistantService.removeById(evt.getAssistantId());

        if (result) {
            // 助理删除成功后，清理每个用户/用户组的关联权限
            for (AiPermissionConfigD permission : assistantPermissions) {
                ServiceResp cleanupResult = permissionConfigService.cleanupUnusedRelatedPermissions(
                    permission.getResourceType(),
                    permission.getResourceId(),
                    permission.getUserType(),
                    permission.getUserId(),
                    permission.getUserName(),
                    currentUserName
                );

                if (!cleanupResult.isSuccess()) {
                    log.warn("助理删除成功，但用户 {} 的关联权限清理失败: {}",
                            permission.getUserName(), permission.getResourceId());
                }
            }
        }

        return result ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }
} 