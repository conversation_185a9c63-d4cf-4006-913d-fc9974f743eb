-- 添加data_model_id字段到审核列表表
ALTER TABLE ai_audit_list_d ADD COLUMN data_model_id VARCHAR(64);
COMMENT ON COLUMN ai_audit_list_d.data_model_id IS '数据模型ID';

-- 添加data_model_id字段到待办列表表
ALTER TABLE ai_todo_list_d ADD COLUMN data_model_id VARCHAR(64);
COMMENT ON COLUMN ai_todo_list_d.data_model_id IS '数据模型ID';

-- 创建索引以提高查询性能
CREATE INDEX idx_audit_data_model ON ai_audit_list_d (data_model_id);
CREATE INDEX idx_todo_data_model ON ai_todo_list_d (data_model_id); 