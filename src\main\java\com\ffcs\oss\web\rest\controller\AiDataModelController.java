package com.ffcs.oss.web.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiDataModelD;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.security.SecurityUtils;
import com.ffcs.oss.service.AiDataModelService;
import com.ffcs.oss.service.AiPermissionConfigService;
import com.ffcs.oss.service.UserService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.constant.CommonConstant;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.datamodel.DataModelEvt;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.ApiModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 数据模型控制器
 */
@RestController
@RequestMapping(value = "/api/model")
@ApiModel("数据模型")
public class AiDataModelController {

    private final AiDataModelService aiDataModelService;
    
    @Autowired
    private AiPermissionConfigService permissionConfigService;
    
    @Autowired
    private UserService userService;

    public AiDataModelController(AiDataModelService aiDataModelService) {
        this.aiDataModelService = aiDataModelService;
    }

    /**
     * 分页查询数据模型列表
     */
    @PostMapping("/getDataModelPage")
    public ServiceResp getDataModelPage(@RequestBody DataModelEvt evt) {
        if (evt.getPageNo() != null && evt.getPageSize() != null && evt.isCountTotal()) {
            com.github.pagehelper.Page page = PageHelper.startPage(evt.getPageNo(), evt.getPageSize(), evt.isCountTotal());
            LambdaQueryWrapper<AiDataModelD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(evt.getModelName() != null, AiDataModelD::getModelName, evt.getModelName());
            if (StringUtils.isNotBlank(evt.getKeyword())) {
                queryWrapper.and(wrapper -> wrapper
                        .like(AiDataModelD::getModelName, evt.getKeyword())
                        .or()
                        .like(AiDataModelD::getCreateUserName, evt.getKeyword())
                );
            }
            queryWrapper.eq(StringUtils.isNotBlank(evt.getCreateUserName()), AiDataModelD::getCreateUserName, evt.getCreateUserName());
            queryWrapper.orderByDesc(AiDataModelD::getUpdateTime, AiDataModelD::getCreateTime);

            List<AiDataModelD> dataModelList = aiDataModelService.list(queryWrapper);
            // 获取当前用户
            String currentUserName = userService.getCurrentUserName();
            dataModelList.removeIf(aiDataModelD -> !permissionConfigService.hasUsePermission(
                    CommonConstant.RESOURCE_TYPE_DATA_MODEL,
                    String.valueOf(aiDataModelD.getDataModelId()),
                    currentUserName
            ));
            return ServiceResp.getInstance().success(QueryPageVm.getInstance(evt, dataModelList, page.getTotal()),
                    LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
        } else {
            LambdaQueryWrapper<AiDataModelD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(evt.getModelName() != null, AiDataModelD::getModelName, evt.getModelName());
            queryWrapper.eq(StringUtils.isNotBlank(evt.getCreateUserName()), AiDataModelD::getCreateUserName, evt.getCreateUserName());
            if (StringUtils.isNotBlank(evt.getKeyword())) {
                queryWrapper.and(wrapper -> wrapper
                        .like(AiDataModelD::getModelName, evt.getKeyword())
                        .or()
                        .like(AiDataModelD::getCreateUserName, evt.getKeyword())
                );
            }
            queryWrapper.orderByDesc(AiDataModelD::getUpdateTime, AiDataModelD::getCreateTime);

            List<AiDataModelD> dataModelList = aiDataModelService.list(queryWrapper);
            // 获取当前用户
            String currentUserName = userService.getCurrentUserName();
            dataModelList.removeIf(aiDataModelD -> !permissionConfigService.hasUsePermission(
                    CommonConstant.RESOURCE_TYPE_DATA_MODEL,
                    String.valueOf(aiDataModelD.getDataModelId()),
                    currentUserName
            ));
            return ServiceResp.getInstance().success(QueryPageVm.getInstance(evt, dataModelList, 0L),
                    LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
        }
    }

    /**
     * 获取数据模型列表
     */
    @PostMapping("/getDataModelList")
    public ServiceResp getDataModelList() {
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 查询当前用户有权限的数据模型
        LambdaQueryWrapper<AiDataModelD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(AiDataModelD::getCreateTime);
        
        List<AiDataModelD> list = aiDataModelService.list(queryWrapper);
        return ServiceResp.getInstance().success(list, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 根据ID获取数据模型详情
     */
    @PostMapping("/getDataModelById")
    public ServiceResp getDataModelById(@RequestBody DataModelEvt evt) {
        AiDataModelD dataModel = aiDataModelService.getById(evt.getDataModelId());
        if (dataModel == null) {
            return ServiceResp.getInstance().error("数据模型不存在");
        }

        if (StringUtils.isBlank(dataModel.getDataModelId())) {
            return ServiceResp.getInstance().error("数据模型ID不能为空");
        }
        
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 检查当前用户是否有权限查看该数据模型
        if (!permissionConfigService.hasUsePermission(1, dataModel.getDataModelId(), currentUserName)) {
            return ServiceResp.getInstance().error("您没有权限查看该数据模型");
        }

        return ServiceResp.getInstance().success(dataModel, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 新增或更新数据模型
     */
    @PostMapping("/addOrUpdateDataModel")
    public ServiceResp addOrUpdateDataModel(@RequestBody DataModelEvt evt) {
        AiDataModelD dataModel = new AiDataModelD();
        BeanUtils.copyProperties(evt, dataModel);
        
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 检查模型名称是否已存在
        String excludeModelId = StringUtils.isBlank(dataModel.getDataModelId()) ? null : dataModel.getDataModelId();
        if (aiDataModelService.checkModelNameExists(dataModel.getModelName(), excludeModelId)) {
            return ServiceResp.getInstance().error("模型名称已存在，请更换名称");
        }
        
        boolean result;
        if (StringUtils.isBlank(dataModel.getDataModelId())) {
            // 新增
            dataModel.setDataModelId(UUID.randomUUID().toString().replace("-", ""));
            dataModel.setCreateTime(LocalDateTime.now());
            dataModel.setUpdateTime(LocalDateTime.now());
            dataModel.setCreateUserName(currentUserName);
            dataModel.setUpdateUserName(currentUserName);
            result = aiDataModelService.save(dataModel);
            
            // 为创建者添加管理权限
            if (result) {
                permissionConfigService.addCreatorAdminPermission(1, dataModel.getDataModelId(), currentUserName);
            }
        } else {
            // 更新
            // 检查当前用户是否有权限修改该数据模型
            if (!permissionConfigService.hasAdminPermission(1, dataModel.getDataModelId(), currentUserName)) {
                return ServiceResp.getInstance().error("您没有权限修改该数据模型");
            }
            
            dataModel.setUpdateTime(LocalDateTime.now());
            dataModel.setUpdateUserName(currentUserName);
            result = aiDataModelService.updateById(dataModel);
        }

        return result ? ServiceResp.getInstance().success(dataModel, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    /**
     * 删除数据模型
     */
    @PostMapping("/deleteDataModel")
    public ServiceResp deleteDataModel(@RequestBody DataModelEvt evt) {
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 检查当前用户是否有权限删除该数据模型
        if (!permissionConfigService.hasAdminPermission(1, evt.getDataModelId(), currentUserName)) {
            return ServiceResp.getInstance().error("您没有权限删除该数据模型");
        }
        
        // 检查数据模型是否被助理引用
        if (aiDataModelService.checkModelReferenceByAssistant(evt.getDataModelId())) {
            return ServiceResp.getInstance().error("该数据模型已被助理引用，无法删除");
        }
        
        boolean result = aiDataModelService.removeById(evt.getDataModelId());
        return result ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }
} 