-- 添加数据库版本、认证类型和Kerberos相关字段
ALTER TABLE ai_db_connection_config ADD COLUMN db_version VARCHAR(10);
ALTER TABLE ai_db_connection_config ADD COLUMN auth_type VARCHAR(20);
ALTER TABLE ai_db_connection_config ADD COLUMN principal VARCHAR(255);
ALTER TABLE ai_db_connection_config ADD COLUMN keytab_file BYTEA;
ALTER TABLE ai_db_connection_config ADD COLUMN krb5_conf_file BYTEA;
ALTER TABLE ai_db_connection_config ADD COLUMN keytab_filename VARCHAR(255);
ALTER TABLE ai_db_connection_config ADD COLUMN krb5_conf_filename VARCHAR(255);

-- 添加描述字段
ALTER TABLE ai_db_connection_config ADD COLUMN description VARCHAR(500);

-- 添加数据模型和助理关联数据源的字段
ALTER TABLE ai_data_model_d ADD COLUMN connection_id BIGINT;
ALTER TABLE ai_assistant_d ADD COLUMN connection_id BIGINT;

-- 添加外键约束
ALTER TABLE ai_data_model_d ADD CONSTRAINT fk_data_model_connection 
    FOREIGN KEY (connection_id) REFERENCES ai_db_connection_config(connection_id) ON DELETE SET NULL;

ALTER TABLE ai_assistant_d ADD CONSTRAINT fk_assistant_connection 
    FOREIGN KEY (connection_id) REFERENCES ai_db_connection_config(connection_id) ON DELETE SET NULL;

COMMENT ON COLUMN ai_db_connection_config.db_version IS '数据库版本';
COMMENT ON COLUMN ai_db_connection_config.auth_type IS '认证类型，如用户名密码、Kerberos等';
COMMENT ON COLUMN ai_db_connection_config.principal IS 'Kerberos principal';
COMMENT ON COLUMN ai_db_connection_config.keytab_file IS 'Kerberos keytab文件内容';
COMMENT ON COLUMN ai_db_connection_config.krb5_conf_file IS 'krb5.conf文件内容';
COMMENT ON COLUMN ai_db_connection_config.keytab_filename IS 'Kerberos keytab文件名';
COMMENT ON COLUMN ai_db_connection_config.krb5_conf_filename IS 'krb5.conf文件名';
COMMENT ON COLUMN ai_db_connection_config.description IS '数据源描述';
COMMENT ON COLUMN ai_data_model_d.connection_id IS '关联的数据源ID';
COMMENT ON COLUMN ai_assistant_d.connection_id IS '关联的数据源ID'; 