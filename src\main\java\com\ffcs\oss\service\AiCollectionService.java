package com.ffcs.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ffcs.oss.domain.AiCollectionD;
import com.ffcs.oss.domain.AiQuestionD;
import com.ffcs.oss.domain.AiAnswerD;

import java.util.List;
import java.util.Map;

/**
 * 收藏Service接口
 */
public interface AiCollectionService extends IService<AiCollectionD> {
    /**
     * 获取助理的收藏列表
     * @param assistantId 助理ID
     * @return 收藏列表（包含问题和回答）
     */
    List<Map<String, Object>> getCollectionsByAssistantId(Long assistantId);
    
    /**
     * 获取收藏详情
     * @param collectionId 收藏ID
     * @return 收藏详情（包含问题和回答）
     */
    Map<String, Object> getCollectionDetail(String collectionId);
    
    /**
     * 添加收藏
     * @param questionId 问题ID
     * @param answerId 回答ID
     * @param assistantId 助理ID
     * @param username 用户名
     * @return 是否成功
     */
    boolean addCollection(String questionId, String answerId, Long assistantId, String username);
    
    /**
     * 检查是否已收藏
     * @param questionId 问题ID
     * @param answerId 回答ID
     * @return 是否已收藏
     */
    boolean isCollected(String questionId, String answerId);
    
    /**
     * 根据问题ID和回答ID获取收藏
     * @param questionId 问题ID
     * @param answerId 回答ID
     * @return 收藏对象
     */
    AiCollectionD getByQuestionIdAndAnswerId(String questionId, String answerId);
} 