package com.ffcs.oss.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Spring MVC 配置
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 配置文件上传解析器
     */
    @Bean
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        // 设置上传文件总大小限制（10MB）
        resolver.setMaxUploadSize(10 * 1024 * 1024);
        // 设置单个文件大小限制（5MB）
        resolver.setMaxUploadSizePerFile(5 * 1024 * 1024);
        // 设置内存中的最大值
        resolver.setMaxInMemorySize(4096);
        // 设置默认编码
        resolver.setDefaultEncoding("UTF-8");
        return resolver;
    }
} 