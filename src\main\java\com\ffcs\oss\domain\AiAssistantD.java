package com.ffcs.oss.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * 助理表
 */
@Data
@TableName("ai_assistant_d")
public class AiAssistantD {
    
    /**
     * 助理id
     */
    @TableId(value = "assistant_id",type = IdType.AUTO)
    private Long assistantId;
    
    /**
     * 助理名称
     */
    @TableField("assistant_name")
    private String assistantName;
    
    /**
     * 描述
     */
    @TableField("describe")
    private String describe;
    
    /**
     * 数据模型id
     */
    @TableField("data_model_id")
    private String dataModelId;
    
    /**
     * 关联的数据源ID
     */
    @TableField("connection_id")
    private BigInteger connectionId;
    
    /**
     * 知识库检索topn
     */
    @TableField("knowledge_search_top_n")
    private Integer knowledgeSearchTopN;
    
    /**
     * limit大小
     */
    @TableField("limit_size")
    private Integer limitSize;
    
    /**
     * 最大token数
     */
    @TableField("max_token_nums")
    private Integer maxTokenNums;
    
    /**
     * 提示词
     */
    @TableField("cue_word")
    private String cueWord;
    
    /**
     * 创建人
     */
    @TableField("create_user_name")
    private String createUserName;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField("update_user_name")
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 是否启用多轮对话 0-不启用 1-启用
     */
    @TableField("enable_multi_turn")
    private String enableMultiTurn;
} 