package com.ffcs.oss.web.rest.exception;

import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @description 全局校验异常处理
 * @create 2023/8/8 16:39
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(CheckException.class)
    public ServiceResp handCheckException(CheckException e) {
        logger.error("校验发生异常:", e);
        return ServiceResp.getInstance().error(String.format("%s: %s", LocalMessageTool.getMessage(LocaleKeyConstant.CHECK_EXCEPTION), e.getMessage()));
    }

    @ExceptionHandler(Exception.class)
    public ServiceResp handGlobalException(Exception e) {
        logger.error("操作异常:", e);
        return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_EXCEPTION));
    }

}
