package com.ffcs.oss.web.rest.evt.user;

import com.ffcs.oss.param.in.QueryPageEvt;
import com.ffcs.oss.utils.TenantUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * Description: type=1:用户管理查询界面调用,type=2:批量赋权,新增用户调用
 * type=3:用户组管理,关联用户调用,type=4:角色管理调用
 *
 * <AUTHOR>
 * @Date 2018/9/2  12:55
 */
@ApiModel("查询用户接口参数")
public class QueryUserEvt extends QueryPageEvt implements Serializable {

    private static final long serialVersionUID = -4556802031550045411L;
    /*@ApiModelProperty(value = "是否递归查询所有用户组",required = true)
    @NotNull
    private boolean recursive = false;*/
    /*@ApiModelProperty(value = "用户名",notes = "模糊查询条件")
    private String name;*/
    @ApiModelProperty(value = "用户状态", notes = "模糊查询条件")
    private Integer status;
    @ApiModelProperty(value = "用户类型", notes = "模糊查询条件")
    private Integer userType;
    @ApiModelProperty(value = "省", notes = "模糊查询条件")
    private String netTitle;
    @ApiModelProperty(value = "市", notes = "模糊查询条件")
    private String country;
    @ApiModelProperty(value = "用户归属")
    private Integer regionType;
    @ApiModelProperty(value = "关键字", notes = "模糊查询条件-用户名")
    private String keyword;
    @ApiModelProperty(value = "登录用户名")
    private String loginName;
    @ApiModelProperty(value = "登录用户区域ID")
    private String regionId;
    @ApiModelProperty(value = "未登录时间")
    private Integer unLoginDay;
    @ApiModelProperty("组ID")
    private Long orgId;
    @ApiModelProperty("用户区域级别")
    private Integer roleLevel;
    @ApiModelProperty("组ID集合")
    private List<Long> orgIdList;
    @ApiModelProperty("语言")
    private String lang;
    @ApiModelProperty("租户编码")
    private String tenantCode;

    @ApiModelProperty("区域id集合")
    private List<String> regionIdList;

    @ApiModelProperty("用户组id")
    private Long userGroupId;

    public Long getUserGroupId() {
        return userGroupId;
    }

    public void setUserGroupId(Long userGroupId) {
        this.userGroupId = userGroupId;
    }

    public List<String> getRegionIdList() {
        return regionIdList;
    }

    public void setRegionIdList(List<String> regionIdList) {
        this.regionIdList = regionIdList;
    }

    public String getTenantCode() {
        return TenantUtils.getTenantCode();
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    /*@ApiModelProperty(value = "电话号码",notes = "模糊查询条件")
    private String phone;
    @ApiModelProperty("选中的用户组ID")
    private Long userGroupId;
    @ApiModelProperty(value = "编辑用户组ID",notes = "用户组管理-添加关联用户时发")
    private String editGroupId;
    @ApiModelProperty(value = "角色ID",notes = "角色管理-关联用户")
    private String roleId;
    @ApiModelProperty(value = "数组,角色管理已关联的用户ID")
    private List<String> selectUserIds;
    @ApiModelProperty(value = "数组，批量赋权已选中的用户ID",notes = "已有选择的用户又去点增加")
    private List<String> userIds;*/
    /*@ApiModelProperty("用户表排序字段")
    private String orderBy;*/

    /*public boolean isRecursive() {
        return recursive;
    }

    public void setRecursive(boolean recursive) {
        this.recursive = recursive;
    }*/

    /*public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }*/

    @Override
    public String toString() {
        return "QueryUserEvt{" +
                "status=" + status +
                ", userType=" + userType +
                ", netTitle='" + netTitle + '\'' +
                ", country='" + country + '\'' +
                ", regionType=" + regionType +
                ", keyword='" + keyword + '\'' +
                ", loginName='" + loginName + '\'' +
                ", regionId='" + regionId + '\'' +
                ", unLoginDay=" + unLoginDay +
                ", orgId='" + orgId + '\'' +
                ", lang='" + lang + '\'' +
                ", tenantCode=" + tenantCode +
                '}';
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getUnLoginDay() {
        return unLoginDay;
    }

    public void setUnLoginDay(Integer unLoginDay) {
        this.unLoginDay = unLoginDay;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getNetTitle() {
        return netTitle;
    }

    public void setNetTitle(String netTitle) {
        this.netTitle = netTitle;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getRegionType() {
        return regionType;
    }

    public void setRegionType(Integer regionType) {
        this.regionType = regionType;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public Integer getRoleLevel() {
        return roleLevel;
    }

    public void setRoleLevel(Integer roleLevel) {
        this.roleLevel = roleLevel;
    }
/*public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Long getUserGroupId() {
        return userGroupId;
    }

    public void setUserGroupId(Long userGroupId) {
        this.userGroupId = userGroupId;
    }

    public String getEditGroupId() {
        return editGroupId;
    }

    public void setEditGroupId(String editGroupId) {
        this.editGroupId = editGroupId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public List<String> getSelectUserIds() {
        return selectUserIds;
    }

    public void setSelectUserIds(List<String> selectUserIds) {
        this.selectUserIds = selectUserIds;
    }

    public List<String> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<String> userIds) {
        this.userIds = userIds;
    }*/

    /*public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }*/

    public List<Long> getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(List<Long> orgIdList) {
        this.orgIdList = orgIdList;
    }
}
