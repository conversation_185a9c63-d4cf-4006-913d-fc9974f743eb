spring:
  profiles:
    # 环境配置
    active: dev
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 15MB
      file-size-threshold: 0

##=============支持前版本的配置=========begin
##部门id主键配置
oss:
  database:
    id-type: leaf
locale:
  type: zh-cn #en-us

# 与mybatis整合
mybatis-plus:
  mapper-locations: classpath:mapper/*/*.xml,classpath:mapper/*.xml
  type-aliases-package: com.ffcs.oss.domain
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    auto-mapping-behavior: full
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
  global-config:
    db-config:
      id-type: auto

##=============支持前版本的配置=========end
springdoc:
  swagger-ui:
    # 禁止默认路径
    disable-swagger-default-url: true
    path: /springdoc/docs.html
    doc-expansion: none
    display-request-duration: true
    show-extensions: true
    show-common-extensions: true
  api-docs:
    enabled: true
    path: /v3/api-docs
    resolve-schema-properties: true

# 数据源配置
datasource:
  # Hive配置
  hive:
    # 是否启用Kerberos认证
    kerberos-enabled: false
    # Kerberos principal
    principal: hive/<EMAIL>
    # Kerberos keytab文件路径
    keytab-path: /etc/kerberos/hive.keytab
  
  # Elasticsearch配置
  elasticsearch:
    # Elasticsearch版本，支持7或8
    version: 7

# SQL执行限制配置
sql-execution:
  # 是否只允许执行SELECT查询
  select-only: true
  # 禁止执行的SQL关键字列表
  forbidden-keywords:
    - "INSERT INTO"
    - "UPDATE"
    - "DELETE FROM"
    - "DROP TABLE"
    - "DROP DATABASE"
    - "TRUNCATE TABLE"
    - "ALTER TABLE"
    - "CREATE TABLE"
    - "CREATE DATABASE"
  # 禁止执行的SELECT查询列表
  forbidden-selects:
    - "SELECT * FROM information_schema"
    - "SELECT * FROM pg_catalog"
    - "SELECT * FROM mysql.user"
    - "SELECT * FROM sys."