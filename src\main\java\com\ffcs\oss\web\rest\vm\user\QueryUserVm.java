package com.ffcs.oss.web.rest.vm.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2018/9/6  9:25
 */
@Alias("QueryUserVm")
@ApiModel("用户信息")
public class QueryUserVm implements Serializable{
    private static final long serialVersionUID = 870155423715911580L;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("登录名")
    private String loginName;
    @ApiModelProperty("登录名未脱敏")
    private String loginNameBk;
    @ApiModelProperty("姓名")
    private String alias;
    @ApiModelProperty("姓名未脱敏")
    private String aliasBk;
    @ApiModelProperty("用户状态")
    private int status;
    @ApiModelProperty("用户类型")
    private int userType;
    @ApiModelProperty("电话号码")
    private String telephone;
    @ApiModelProperty("电话号码未脱敏")
    private String telephoneBk;
    @ApiModelProperty("邮箱")
    private String eMail;
    @ApiModelProperty("邮箱未脱敏")
    private String eMailBk;
    @ApiModelProperty("办公电话")
    private String officePhone;
    @ApiModelProperty("区域ID")
    private String regionId;
    @ApiModelProperty("用户归属")
    private String regionType;
    @ApiModelProperty("机构ID")
    private String orgId;
    @ApiModelProperty("机构名称")
    private String orgName;
    @ApiModelProperty("用户组id")
    private String userGroupId;
    @ApiModelProperty("用户组名称")
    private String userGroupName;
    @ApiModelProperty("用户性别")
    private int sex;
    @ApiModelProperty("身份证号码")
    private String pid;
    @ApiModelProperty("身份证号码未脱敏")
    private String pidBk;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("密码更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date pwdUpdateTime;
    @ApiModelProperty("全量区域ID")
    private String fullRegion;
    @ApiModelProperty("全量区域名称")
    private String fullRegionName;
    @ApiModelProperty("工号")
    private String jobNumber;
    @ApiModelProperty("最大在线会话数")
    private String maxOnlineSessionNum;
    @ApiModelProperty("账号有效策略")
    private String accountEffective;
    @ApiModelProperty("密码有效策略")
    private String pwdEffective;
    @ApiModelProperty("是否需要修改密码")
    private String isMustUpdPwd;
    @ApiModelProperty("账号锁定策略")
    private String accountLockout;
    @ApiModelProperty("账号停用策略")
    private String accountDisabled;
    @ApiModelProperty("密码有效期提示天数")
    private String pwdPromptDays;
    @ApiModelProperty("系统风格类型")
    private String systemStyleType;
    @ApiModelProperty("用户类型名")
    private String userTypeCn;
    @ApiModelProperty("角色ids")
    private String roleIds;
    @ApiModelProperty("角色名称")
    private String roleName;
    @ApiModelProperty("权限菜单ids")
    private String menuIds;
    @ApiModelProperty("权限菜单名称")
    private String menuName;
    @ApiModelProperty("状态名")
    private String statusCn;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("创建人")
    private String createUserName;
    @ApiModelProperty("最近登录时间")
    private String lastLoginTime;
    @ApiModelProperty("补充字段1 - 单位")
    private String arg1;
    @ApiModelProperty("补充字段2 - 岗位")
    private String arg2;
    @ApiModelProperty("补充字段3")
    private String arg3;
    @ApiModelProperty("补充字段4")
    private String arg4;
    @ApiModelProperty("所属租户编码")
    private String tenantCode;

    public String getUserGroupId() {
        return userGroupId;
    }

    public void setUserGroupId(String userGroupId) {
        this.userGroupId = userGroupId;
    }

    public String getUserGroupName() {
        return userGroupName;
    }

    public void setUserGroupName(String userGroupName) {
        this.userGroupName = userGroupName;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getLoginNameBk() {
        return loginNameBk;
    }

    public void setLoginNameBk(String loginNameBk) {
        this.loginNameBk = loginNameBk;
    }

    public String getAliasBk() {
        return aliasBk;
    }

    public void setAliasBk(String aliasBk) {
        this.aliasBk = aliasBk;
    }

    public String getTelephoneBk() {
        return telephoneBk;
    }

    public void setTelephoneBk(String telephoneBk) {
        this.telephoneBk = telephoneBk;
    }

    public String geteMailBk() {
        return eMailBk;
    }

    public void seteMailBk(String eMailBk) {
        this.eMailBk = eMailBk;
    }

    public String getPidBk() {
        return pidBk;
    }

    public void setPidBk(String pidBk) {
        this.pidBk = pidBk;
    }

    public String getMenuIds() {
        return menuIds;
    }

    public void setMenuIds(String menuIds) {
        this.menuIds = menuIds;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String geteMail() {
        return eMail;
    }

    public void seteMail(String eMail) {
        this.eMail = eMail;
    }

    public String getOfficePhone() {
        return officePhone;
    }

    public void setOfficePhone(String officePhone) {
        this.officePhone = officePhone;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getRegionType() {
        return regionType;
    }

    public void setRegionType(String regionType) {
        this.regionType = regionType;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getPwdUpdateTime() {
        return pwdUpdateTime;
    }

    public void setPwdUpdateTime(Date pwdUpdateTime) {
        this.pwdUpdateTime = pwdUpdateTime;
    }

    public String getFullRegion() {
        return fullRegion;
    }

    public void setFullRegion(String fullRegion) {
        this.fullRegion = fullRegion;
    }

    public String getFullRegionName() {
        return fullRegionName;
    }

    public void setFullRegionName(String fullRegionName) {
        this.fullRegionName = fullRegionName;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getMaxOnlineSessionNum() {
        return maxOnlineSessionNum;
    }

    public void setMaxOnlineSessionNum(String maxOnlineSessionNum) {
        this.maxOnlineSessionNum = maxOnlineSessionNum;
    }

    public String getAccountEffective() {
        return accountEffective;
    }

    public void setAccountEffective(String accountEffective) {
        this.accountEffective = accountEffective;
    }

    public String getPwdEffective() {
        return pwdEffective;
    }

    public void setPwdEffective(String pwdEffective) {
        this.pwdEffective = pwdEffective;
    }

    public String getIsMustUpdPwd() {
        return isMustUpdPwd;
    }

    public void setIsMustUpdPwd(String isMustUpdPwd) {
        this.isMustUpdPwd = isMustUpdPwd;
    }

    public String getAccountLockout() {
        return accountLockout;
    }

    public void setAccountLockout(String accountLockout) {
        this.accountLockout = accountLockout;
    }

    public String getAccountDisabled() {
        return accountDisabled;
    }

    public void setAccountDisabled(String accountDisabled) {
        this.accountDisabled = accountDisabled;
    }

    public String getPwdPromptDays() {
        return pwdPromptDays;
    }

    public void setPwdPromptDays(String pwdPromptDays) {
        this.pwdPromptDays = pwdPromptDays;
    }

    public String getSystemStyleType() {
        return systemStyleType;
    }

    public void setSystemStyleType(String systemStyleType) {
        this.systemStyleType = systemStyleType;
    }

    public String getUserTypeCn() {
        return userTypeCn;
    }

    public void setUserTypeCn(String userTypeCn) {
        this.userTypeCn = userTypeCn;
    }

    public String getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(String roleIds) {
        this.roleIds = roleIds;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getStatusCn() {
        return statusCn;
    }

    public void setStatusCn(String statusCn) {
        this.statusCn = statusCn;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getArg1() {
        return arg1;
    }

    public void setArg1(String arg1) {
        this.arg1 = arg1;
    }

    public String getArg2() {
        return arg2;
    }

    public void setArg2(String arg2) {
        this.arg2 = arg2;
    }

    public String getArg3() {
        return arg3;
    }

    public void setArg3(String arg3) {
        this.arg3 = arg3;
    }

    public String getArg4() {
        return arg4;
    }

    public void setArg4(String arg4) {
        this.arg4 = arg4;
    }

    @Override
    public String toString() {
        return "QueryUserVm{" +
                "userId=" + userId +
                ", loginName='" + loginName + '\'' +
                ", alias='" + alias + '\'' +
                ", status=" + status +
                ", userType=" + userType +
                ", telephone='" + telephone + '\'' +
                ", eMail='" + eMail + '\'' +
                ", officePhone='" + officePhone + '\'' +
                ", regionId='" + regionId + '\'' +
                ", regionType='" + regionType + '\'' +
                ", orgId='" + orgId + '\'' +
                ", orgName='" + orgName + '\'' +
                ", sex=" + sex +
                ", pid='" + pid + '\'' +
                ", description='" + description + '\'' +
                ", pwdUpdateTime=" + pwdUpdateTime +
                ", fullRegion='" + fullRegion + '\'' +
                ", fullRegionName='" + fullRegionName + '\'' +
                ", jobNumber='" + jobNumber + '\'' +
                ", maxOnlineSessionNum='" + maxOnlineSessionNum + '\'' +
                ", accountEffective='" + accountEffective + '\'' +
                ", pwdEffective='" + pwdEffective + '\'' +
                ", isMustUpdPwd='" + isMustUpdPwd + '\'' +
                ", accountLockout='" + accountLockout + '\'' +
                ", accountDisabled='" + accountDisabled + '\'' +
                ", pwdPromptDays='" + pwdPromptDays + '\'' +
                ", systemStyleType='" + systemStyleType + '\'' +
                ", userTypeCn='" + userTypeCn + '\'' +
                ", roleIds='" + roleIds + '\'' +
                ", roleName='" + roleName + '\'' +
                ", statusCn='" + statusCn + '\'' +
                ", createTime='" + createTime + '\'' +
                ", createUserName='" + createUserName + '\'' +
                ", lastLoginTime='" + lastLoginTime + '\'' +
                ", arg1='" + arg1 + '\'' +
                ", arg2='" + arg2 + '\'' +
                ", arg3='" + arg3 + '\'' +
                ", arg4='" + arg4 + '\'' +
                '}';
    }
}
