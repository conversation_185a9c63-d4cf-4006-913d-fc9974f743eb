package com.ffcs.oss.utils.hive;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Closeable;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * Hive客户端类
 * 提供与Hive交互的高级接口
 */
@Slf4j
@Data
public class HiveClient implements Closeable {

    private HiveConnectionParam connectionParam;
    private boolean usePooledConnection;

    /**
     * 使用默认连接参数创建Hive客户端
     */
    public HiveClient() {
        this(HiveUtils.createDefaultConnectionParam(), true);
    }

    /**
     * 使用指定连接参数创建Hive客户端
     * 
     * @param connectionParam Hive连接参数
     */
    public HiveClient(HiveConnectionParam connectionParam) {
        this(connectionParam, true);
    }

    /**
     * 使用指定连接参数和连接模式创建Hive客户端
     * 
     * @param connectionParam Hive连接参数
     * @param usePooledConnection 是否使用池化连接
     */
    public HiveClient(HiveConnectionParam connectionParam, boolean usePooledConnection) {
        this.connectionParam = connectionParam;
        this.usePooledConnection = usePooledConnection;
    }

    /**
     * 获取数据库连接
     * 
     * @return 数据库连接
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     * @throws ExecutionException 执行异常
     */
    public Connection getConnection() throws SQLException, IOException, InterruptedException, ExecutionException {
        if (usePooledConnection) {
            return HiveConnectionManager.getPooledConnection(connectionParam);
        } else {
            return HiveConnectionManager.getAdHocConnection(connectionParam);
        }
    }

    /**
     * 测试连接
     * 
     * @return 是否连接成功
     */
    public boolean testConnection() {
        return HiveUtils.testConnection(connectionParam);
    }

    /**
     * 执行查询操作
     * 
     * @param sql SQL语句
     * @return 查询结果
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     * @throws ExecutionException 执行异常
     */
    public List<Map<String, Object>> executeQuery(String sql) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        return HiveUtils.executeQuery(connectionParam, sql);
    }

    /**
     * 执行更新操作
     * 
     * @param sql SQL语句
     * @return 影响的行数
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     * @throws ExecutionException 执行异常
     */
    public int executeUpdate(String sql) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        return HiveUtils.executeUpdate(connectionParam, sql);
    }

    /**
     * 获取数据库列表
     * 
     * @return 数据库列表
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     * @throws ExecutionException 执行异常
     */
    public List<String> getDatabases() 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        return HiveUtils.getDatabases(connectionParam);
    }

    /**
     * 获取表列表
     * 
     * @param database 数据库名称
     * @return 表列表
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     * @throws ExecutionException 执行异常
     */
    public List<String> getTables(String database) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        return HiveUtils.getTables(connectionParam, database);
    }

    /**
     * 获取表结构
     * 
     * @param database 数据库名称
     * @param table 表名称
     * @return 列信息列表
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     * @throws ExecutionException 执行异常
     */
    public List<Map<String, Object>> getTableSchema(String database, String table) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        return HiveUtils.getTableSchema(connectionParam, database, table);
    }

    /**
     * 关闭客户端资源
     */
    @Override
    public void close() throws IOException {
        if (usePooledConnection) {
            HiveConnectionManager.closeAllConnections();
        }
    }
} 