package com.ffcs.oss.common.exception;

/**
 * 业务异常类
 * 用于表示业务逻辑错误导致的异常
 */
public class BusinessException extends RuntimeException {
    
    /**
     * 无参构造函数
     */
    public BusinessException() {
        super();
    }
    
    /**
     * 带消息的构造函数
     * 
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
    }
    
    /**
     * 带消息和原因的构造函数
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 带原因的构造函数
     * 
     * @param cause 原因异常
     */
    public BusinessException(Throwable cause) {
        super(cause);
    }
} 