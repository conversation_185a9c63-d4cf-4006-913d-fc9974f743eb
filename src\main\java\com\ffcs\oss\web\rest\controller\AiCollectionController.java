package com.ffcs.oss.web.rest.controller;

import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiAnswerD;
import com.ffcs.oss.domain.AiChatManageD;
import com.ffcs.oss.domain.AiCollectionD;
import com.ffcs.oss.domain.AiQuestionD;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.security.SecurityUtils;
import com.ffcs.oss.service.AiAnswerService;
import com.ffcs.oss.service.AiChatManageService;
import com.ffcs.oss.service.AiCollectionService;
import com.ffcs.oss.service.AiQuestionService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.collection.CollectionEvt;
import com.ffcs.oss.web.rest.evt.conversation.ConversationEvt;
import com.ffcs.oss.web.rest.evt.conversation.ConversationWithAnswerEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 收藏控制器
 */
@RestController
@RequestMapping(value = "/api/collection")
@ApiModel("收藏管理")
public class AiCollectionController {

    @Autowired
    private AiCollectionService aiCollectionService;
    
    @Autowired
    private AiQuestionService aiQuestionService;
    
    @Autowired
    private AiAnswerService aiAnswerService;
    
    @Autowired
    private AiChatManageService aiChatManageService;

    /**
     * 添加收藏
     */
    @PostMapping("/addCollection")
    @ApiOperation("添加收藏")
    public ServiceResp addCollection(@RequestBody CollectionEvt evt) {
        if (StringUtils.isBlank(evt.getQuestionId())) {
            return ServiceResp.getInstance().error("问题ID不能为空");
        }
        if (StringUtils.isBlank(evt.getAnswerId())) {
            return ServiceResp.getInstance().error("回答ID不能为空");
        }
        if (evt.getAssistantId() == null) {
            return ServiceResp.getInstance().error("助理ID不能为空");
        }
        
        // 获取当前用户
        String currentUserName = StringUtils.isNotBlank(PtSecurityUtils.getUsername()) ? 
                PtSecurityUtils.getUsername() : 
                StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get()) ? 
                        SecurityUtils.getCurrentUserLogin().get() : "";
        
        // 添加收藏
        boolean result = aiCollectionService.addCollection(evt.getQuestionId(), evt.getAnswerId(), evt.getAssistantId(), currentUserName);
        
        // 检查是否已收藏
        boolean isCollected = aiCollectionService.isCollected(evt.getQuestionId(), evt.getAnswerId());
        
        Map<String, Object> data = new HashMap<>();
        data.put("isCollected", isCollected);
        
        return result ? 
                ServiceResp.getInstance().success(data, "收藏成功") : 
                ServiceResp.getInstance().error("收藏失败");
    }
    
    /**
     * 取消收藏
     */
    @PostMapping("/removeCollection")
    @ApiOperation("取消收藏")
    public ServiceResp removeCollection(@RequestBody CollectionEvt evt) {
        if (StringUtils.isBlank(evt.getQuestionId()) && StringUtils.isBlank(evt.getAnswerId()) && StringUtils.isBlank(evt.getCollectionId())) {
            return ServiceResp.getInstance().error("收藏ID、问题ID或回答ID不能同时为空");
        }
        
        boolean result = false;
        
        // 根据收藏ID删除
        if (StringUtils.isNotBlank(evt.getCollectionId())) {
            result = aiCollectionService.removeById(evt.getCollectionId());
        } 
        // 根据问题ID和回答ID删除
        else if (StringUtils.isNotBlank(evt.getQuestionId()) && StringUtils.isNotBlank(evt.getAnswerId())) {
            AiCollectionD collection = aiCollectionService.getByQuestionIdAndAnswerId(evt.getQuestionId(), evt.getAnswerId());
            if (collection != null) {
                result = aiCollectionService.removeById(collection.getCollectionId());
            }
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("isCollected", false);
        
        return result ? 
                ServiceResp.getInstance().success(data, "取消收藏成功") : 
                ServiceResp.getInstance().error("取消收藏失败");
    }
    
    /**
     * 获取助理的收藏列表
     */
    @PostMapping("/getCollectionsByAssistantId")
    @ApiOperation("获取助理的收藏列表")
    public ServiceResp getCollectionsByAssistantId(@RequestBody CollectionEvt evt) {
        if (evt.getAssistantId() == null) {
            return ServiceResp.getInstance().error("助理ID不能为空");
        }
        
        List<Map<String, Object>> collections = aiCollectionService.getCollectionsByAssistantId(evt.getAssistantId());
        
        return ServiceResp.getInstance().success(collections, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }
    
    /**
     * 获取收藏详情
     */
    @PostMapping("/getCollectionDetail")
    @ApiOperation("获取收藏详情")
    public ServiceResp getCollectionDetail(@RequestBody CollectionEvt evt) {
        if (StringUtils.isBlank(evt.getCollectionId())) {
            return ServiceResp.getInstance().error("收藏ID不能为空");
        }
        
        Map<String, Object> detail = aiCollectionService.getCollectionDetail(evt.getCollectionId());
        
        if (detail.isEmpty()) {
            return ServiceResp.getInstance().error("收藏不存在");
        }
        
        return ServiceResp.getInstance().success(detail, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }
    
    /**
     * 检查是否已收藏
     */
    @PostMapping("/isCollected")
    @ApiOperation("检查是否已收藏")
    public ServiceResp isCollected(@RequestBody CollectionEvt evt) {
        if (StringUtils.isBlank(evt.getQuestionId()) || StringUtils.isBlank(evt.getAnswerId())) {
            return ServiceResp.getInstance().error("问题ID和回答ID不能为空");
        }
        
        boolean isCollected = aiCollectionService.isCollected(evt.getQuestionId(), evt.getAnswerId());
        
        Map<String, Object> data = new HashMap<>();
        data.put("isCollected", isCollected);
        
        return ServiceResp.getInstance().success(data, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }
    
    /**
     * 创建新会话继续对话
     */
    @PostMapping("/continueConversation")
    @ApiOperation("创建新会话继续对话")
    public ServiceResp continueConversation(@RequestBody CollectionEvt evt) {
        if (StringUtils.isBlank(evt.getCollectionId())) {
            return ServiceResp.getInstance().error("收藏ID不能为空");
        }
        
        // 获取收藏详情
        Map<String, Object> detail = aiCollectionService.getCollectionDetail(evt.getCollectionId());
        
        if (detail.isEmpty()) {
            return ServiceResp.getInstance().error("收藏不存在");
        }
        
        AiCollectionD collection = (AiCollectionD) detail.get("collection");
        AiQuestionD question = (AiQuestionD) detail.get("question");
        AiAnswerD answer = (AiAnswerD) detail.get("answer");
        
        if (collection == null || question == null || answer == null) {
            return ServiceResp.getInstance().error("收藏数据不完整");
        }
        
        // 获取当前用户
        String currentUserName = StringUtils.isNotBlank(PtSecurityUtils.getUsername()) ? 
                PtSecurityUtils.getUsername() : 
                StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get()) ? 
                        SecurityUtils.getCurrentUserLogin().get() : "";
        
        // 创建新会话
        AiChatManageD chatManage = new AiChatManageD();
        chatManage.setSessionId(UUID.randomUUID().toString().replace("-", ""));
        chatManage.setSessionName(question.getQuestion());
        chatManage.setAssistantId(collection.getAssistantId());
        chatManage.setCreateUserName(currentUserName);
        chatManage.setUpdateUserName(currentUserName);
        chatManage.setCreateTime(LocalDateTime.now());
        chatManage.setUpdateTime(LocalDateTime.now());
        
        boolean savedChat = aiChatManageService.save(chatManage);
        
        if (!savedChat) {
            return ServiceResp.getInstance().error("创建会话失败");
        }
        
        // 复制问题到新会话
        AiQuestionD newQuestion = new AiQuestionD();
        BeanUtils.copyProperties(question, newQuestion);
        newQuestion.setQuestionId(UUID.randomUUID().toString().replace("-", ""));
        newQuestion.setSessionId(chatManage.getSessionId());
        newQuestion.setCreateUserName(currentUserName);
        newQuestion.setCreateTime(LocalDateTime.now());
        
        boolean savedQuestion = aiQuestionService.save(newQuestion);
        
        if (!savedQuestion) {
            return ServiceResp.getInstance().error("创建问题失败");
        }
        
        // 复制回答到新会话
        AiAnswerD newAnswer = new AiAnswerD();
        BeanUtils.copyProperties(answer, newAnswer);
        newAnswer.setAnswerId(UUID.randomUUID().toString().replace("-", ""));
        newAnswer.setQuestionId(newQuestion.getQuestionId());
        newAnswer.setSessionId(chatManage.getSessionId());
        newAnswer.setCreateUserName(currentUserName);
        newAnswer.setCreateTime(LocalDateTime.now());
        
        boolean savedAnswer = aiAnswerService.save(newAnswer);
        
        if (!savedAnswer) {
            return ServiceResp.getInstance().error("创建回答失败");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("sessionId", chatManage.getSessionId());
        result.put("questionId", newQuestion.getQuestionId());
        result.put("answerId", newAnswer.getAnswerId());
        
        return ServiceResp.getInstance().success(result, "创建会话成功");
    }
} 