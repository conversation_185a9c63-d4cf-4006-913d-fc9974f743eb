package com.ffcs.oss.web.rest.vm.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.util.Date;

@Alias("SimpleUserVm")
@ApiModel
public class SimpleUserVm implements Serializable {
    private static final long serialVersionUID = 8206424901644725237L;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("登录名")
    private String loginName;
    @ApiModelProperty("用户状态")
    private String status;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("手机号码")
    private String telephone;
    @ApiModelProperty("区域ID")
    private Long regionId;
    @ApiModelProperty("组织机构ID")
    private Long orgId;
    @ApiModelProperty("身份证号码")
    private String pid;
    @ApiModelProperty("密码更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pwdUpdateTime;
    @ApiModelProperty("工号")
    private String jobNumber;
    @ApiModelProperty("密码")
    private String password;
    @ApiModelProperty("用户组状态")
    private String groupStatus;

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public void setPwdUpdateTime(Date pwdUpdateTime) {
        this.pwdUpdateTime = pwdUpdateTime;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setGroupStatus(String groupStatus) {
        this.groupStatus = groupStatus;
    }

    public Long getUserId() {
        return userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public String getStatus() {
        return status;
    }

    public String getUserType() {
        return userType;
    }

    public String getTelephone() {
        return telephone;
    }

    public Long getRegionId() {
        return regionId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public String getPid() {
        return pid;
    }

    public Date getPwdUpdateTime() {
        return pwdUpdateTime;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public String getPassword() {
        return password;
    }

    public String getGroupStatus() {
        return groupStatus;
    }
}
