
[libdefaults]
  forwardable = true
  default_realm = OSS.FFCS.CN
  ticket_lifetime = 24h
  dns_lookup_realm = false
  dns_lookup_kdc = false
  default_ccache_name = /tmp/krb5cc_%{uid}
  #default_tgs_enctypes = aes des3-cbc-sha1 rc4 des-cbc-md5
  #default_tkt_enctypes = aes des3-cbc-sha1 rc4 des-cbc-md5

[logging]
  default = FILE:/var/log/krb5kdc.log
  admin_server = FILE:/var/log/kadmind.log
  kdc = FILE:/var/log/krb5kdc.log

[realms]
  OSS.FFCS.CN = {
    admin_server = bigdata1.oss.ffcs.cn
    kdc = bigdata1.oss.ffcs.cn
  }

