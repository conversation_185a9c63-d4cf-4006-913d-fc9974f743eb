package com.ffcs.oss.web.rest.evt.assistant;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigInteger;

/**
 * 助理请求参数
 */
@ApiModel("助理请求参数")
public class AssistantEvt extends QueryPageEvt implements Serializable {
    
    @ApiModelProperty("助理id")
    private Long assistantId;
    
    @ApiModelProperty("助理名称")
    @NotBlank(message = "助理名称不能为空")
    private String assistantName;
    
    @ApiModelProperty("描述")
    private String describe;
    
    @ApiModelProperty("数据模型id")
    private String dataModelId;
    
    @ApiModelProperty("知识库检索topn")
    private Integer knowledgeSearchTopN;
    
    @ApiModelProperty("limit大小")
    private Integer limitSize;
    
    @ApiModelProperty("最大token数")
    private Integer maxTokenNums;
    
    @ApiModelProperty("提示词")
    private String cueWord;
    
    @ApiModelProperty("创建人")
    private String createUserName;
    
    @ApiModelProperty("更新人")
    private String updateUserName;
    
    @ApiModelProperty("是否启用多轮对话 0-不启用 1-启用")
    private String enableMultiTurn;
    @ApiModelProperty("数源id")
    private BigInteger connectionId;

    public BigInteger getConnectionId() {
        return connectionId;
    }

    public void setConnectionId(BigInteger connectionId) {
        this.connectionId = connectionId;
    }

    public Long getAssistantId() {
        return assistantId;
    }

    public void setAssistantId(Long assistantId) {
        this.assistantId = assistantId;
    }

    public String getAssistantName() {
        return assistantName;
    }

    public void setAssistantName(String assistantName) {
        this.assistantName = assistantName;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getDataModelId() {
        return dataModelId;
    }

    public void setDataModelId(String dataModelId) {
        this.dataModelId = dataModelId;
    }

    public Integer getKnowledgeSearchTopN() {
        return knowledgeSearchTopN;
    }

    public void setKnowledgeSearchTopN(Integer knowledgeSearchTopN) {
        this.knowledgeSearchTopN = knowledgeSearchTopN;
    }

    public Integer getLimitSize() {
        return limitSize;
    }

    public void setLimitSize(Integer limitSize) {
        this.limitSize = limitSize;
    }

    public Integer getMaxTokenNums() {
        return maxTokenNums;
    }

    public void setMaxTokenNums(Integer maxTokenNums) {
        this.maxTokenNums = maxTokenNums;
    }

    public String getCueWord() {
        return cueWord;
    }

    public void setCueWord(String cueWord) {
        this.cueWord = cueWord;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }
    
    public String getEnableMultiTurn() {
        return enableMultiTurn;
    }

    public void setEnableMultiTurn(String enableMultiTurn) {
        this.enableMultiTurn = enableMultiTurn;
    }
} 