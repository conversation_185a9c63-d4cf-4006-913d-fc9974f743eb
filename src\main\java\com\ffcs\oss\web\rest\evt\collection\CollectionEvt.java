package com.ffcs.oss.web.rest.evt.collection;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 收藏请求参数
 */
@ApiModel("收藏请求参数")
public class CollectionEvt extends QueryPageEvt implements Serializable {
    
    @ApiModelProperty("收藏id")
    private String collectionId;
    
    @ApiModelProperty("助理id")
    private Long assistantId;
    
    @ApiModelProperty("问题id")
    private String questionId;
    
    @ApiModelProperty("回答id")
    private String answerId;
    
    @ApiModelProperty("创建人")
    private String createUserName;
    
    @ApiModelProperty("标题")
    private String title;
    
    @ApiModelProperty("描述")
    private String description;

    public String getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(String collectionId) {
        this.collectionId = collectionId;
    }

    public Long getAssistantId() {
        return assistantId;
    }

    public void setAssistantId(Long assistantId) {
        this.assistantId = assistantId;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
} 