package com.ffcs.oss.web.rest.evt.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


@ApiModel("查询某个用户参数")
public class QryUserEvt implements Serializable {
    private static final long serialVersionUID = -1980509892391237642L;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty(value = "登录名", notes = "精确匹配")
    private String loginName;

    public QryUserEvt() {
    }

    public QryUserEvt(Long userId) {
        this.userId = userId;
    }

    public QryUserEvt(String loginName) {
        this.loginName = loginName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    @Override
    public String toString() {
        return "QryUserEvt{" +
                "userId=" + userId +
                ", loginName='" + loginName + '\'' +
                '}';
    }
}
