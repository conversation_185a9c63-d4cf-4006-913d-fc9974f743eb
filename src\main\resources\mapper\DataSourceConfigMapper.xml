<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ffcs.oss.mapper.datasourceconfig.DataSourceConfigMapper">
    <select id="getDataSourceConfigList" resultType="com.ffcs.oss.web.rest.vm.datasourceconfig.DataSourceConfigVm">
        select
            data_source_config_id,
            NAME,
            full_name,
            KEY,
            cron_expression,
            TYPE,
            is_on,
            CONTENT,
            creator_name,
            create_time,
            updater_name,
            update_time
        from
            ai_data_source_config_d
        <where>
            <if test="evt.keyword!=null and evt.keyword!=''">
                and (name=#{evt.keyword} or fullName=#{evt.keyword})
            </if>
            <if test="evt.type!=null">
                and type=#{evt.type}
            </if>
        </where>
    </select>
    <select id="nameWhetherRepeat" resultType="java.lang.Integer">
        select count(*) from ai_data_source_config_d where name=#{evt.name}
    </select>
</mapper>
