package com.ffcs.oss.config;

import com.ffcs.oss.utils.TenantUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description
 * @create 2023/5/11 9:55
 */
@RefreshScope
@Configuration
public class TenantCodeConfig {

    @Value("${isUseTenantCode:false}")
    private void setTenantCode(boolean isUseTenantCode){
        TenantUtils.isUseTenantCode=isUseTenantCode;
    }

    @Value("${groupCreatorName:admin}")
    private void setGroupCreatorName(String groupCreatorName){
        TenantUtils.groupCreatorName=groupCreatorName;
    }
}
