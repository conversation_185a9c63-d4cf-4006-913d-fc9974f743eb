-- 创建数据库连接配置表
CREATE TABLE IF NOT EXISTS `ai_db_connection_config` (
    `connection_id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `connection_name` VARCHAR(100) NOT NULL COMMENT '连接名称',
    `db_type` VARCHAR(50) NOT NULL COMMENT '数据库类型：mysql, postgresql, elasticsearch, hive等',
    `host` VARCHAR(255) NOT NULL COMMENT '主机地址',
    `port` INT NOT NULL COMMENT '端口号',
    `database_name` VARCHAR(100) COMMENT '数据库名',
    `username` VARCHAR(100) COMMENT '用户名',
    `password` VARCHAR(255) COMMENT '密码',
    `params` TEXT COMMENT '其他连接参数，JSON格式',
    `creator_name` VARCHAR(50) COMMENT '创建人',
    `create_time` DATETIME COMMENT '创建时间',
    `updater_name` VARCHAR(50) COMMENT '更新人',
    `update_time` DATETIME COMMENT '更新时间',
    UNIQUE KEY `uk_name` (`connection_name`)
) COMMENT='数据库连接配置表';

-- 示例：Elasticsearch 7.x连接配置（密码需要加密后使用）
INSERT INTO `ai_db_connection_config` (`connection_name`, `db_type`, `host`, `port`, `database_name`, `username`, `password`, `creator_name`, `create_time`, `updater_name`, `update_time`)
VALUES ('ES7集群', 'elasticsearch', '*************', 9200, '', 'elastic', 'fGVGSSKQYOeT7HyLnsFG5Q==', 'admin', NOW(), 'admin', NOW());

-- 示例：Elasticsearch 8.x连接配置（密码需要加密后使用）
INSERT INTO `ai_db_connection_config` (`connection_name`, `db_type`, `host`, `port`, `database_name`, `username`, `password`, `params`, `creator_name`, `create_time`, `updater_name`, `update_time`)
VALUES ('ES8集群', 'elasticsearch', '*************', 9200, '', 'elastic', 'fGVGSSKQYOeT7HyLnsFG5Q==', '{"version":"8"}', 'admin', NOW(), 'admin', NOW());

-- 示例：Hive无Kerberos连接配置（密码需要加密后使用）
INSERT INTO `ai_db_connection_config` (`connection_name`, `db_type`, `host`, `port`, `database_name`, `username`, `password`, `creator_name`, `create_time`, `updater_name`, `update_time`)
VALUES ('Hive集群-无Kerberos', 'hive', '*************', 10000, 'default', 'hive', 'fGVGSSKQYOeT7HyLnsFG5Q==', 'admin', NOW(), 'admin', NOW());

-- 示例：Hive带Kerberos连接配置（密码可为空，因为使用Kerberos认证）
INSERT INTO `ai_db_connection_config` (`connection_name`, `db_type`, `host`, `port`, `database_name`, `params`, `creator_name`, `create_time`, `updater_name`, `update_time`)
VALUES ('Hive集群-Kerberos', 'hive', '*************', 10000, 'default', 'hive.server2.authentication=KERBEROS;hive.server2.transport.mode=http', 'admin', NOW(), 'admin', NOW());

-- 注意：以上密码均为加密后的值，明文为"password" 