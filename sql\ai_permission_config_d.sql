CREATE TABLE IF NOT EXISTS ai_permission_config_d (
                                                      permission_id BIGSERIAL PRIMARY KEY,
                                                      resource_type INTEGER NOT NULL,
                                                      resource_id VARCHAR(64) NOT NULL,
    permission_type INTEGER NOT NULL,
    user_type INTEGER NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    user_name VARCHAR(128) NOT NULL,
    create_user_name VARCHAR(128),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_user_name VARCHAR(128),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- 添加唯一约束，确保同一资源对同一用户/用户组只有一种权限
    CONSTRAINT unique_resource_user_permission UNIQUE (resource_type, resource_id, user_type, user_id),
    -- 添加检查约束，确保资源类型在有效范围内
    CONSTRAINT valid_resource_type CHECK (resource_type IN (1, 2, 3)),
    -- 添加检查约束，确保权限类型在有效范围内
    CONSTRAINT valid_permission_type CHECK (permission_type IN (1, 2))
    );

-- 添加索引
CREATE INDEX idx_permission_resource ON ai_permission_config_d (resource_type, resource_id);
CREATE INDEX idx_permission_user ON ai_permission_config_d (user_type, user_id);
CREATE INDEX idx_permission_user_name ON ai_permission_config_d (user_name);


-- 添加注释
COMMENT ON TABLE ai_permission_config_d IS '权限配置表';
COMMENT ON COLUMN ai_permission_config_d.permission_id IS '权限ID';
COMMENT ON COLUMN ai_permission_config_d.resource_type IS '资源类型：1-数据模型，2-助理，3-数据源';
COMMENT ON COLUMN ai_permission_config_d.resource_id IS '资源ID';
COMMENT ON COLUMN ai_permission_config_d.permission_type IS '权限类型：1-管理员，2-普通用户';
COMMENT ON COLUMN ai_permission_config_d.user_type IS '用户/用户组类型：1-用户，2-用户组';
COMMENT ON COLUMN ai_permission_config_d.user_id IS '用户/用户组ID';
COMMENT ON COLUMN ai_permission_config_d.user_name IS '用户/用户组名称';
COMMENT ON COLUMN ai_permission_config_d.create_user_name IS '创建人';
COMMENT ON COLUMN ai_permission_config_d.create_time IS '创建时间';
COMMENT ON COLUMN ai_permission_config_d.update_user_name IS '更新人';
COMMENT ON COLUMN ai_permission_config_d.update_time IS '更新时间';