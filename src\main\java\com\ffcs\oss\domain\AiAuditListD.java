package com.ffcs.oss.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 审核列表
 */
@Data
@TableName("ai_audit_list_d")
public class AiAuditListD {
    
    /**
     * 主键
     */
    @TableId(value = "audit_list_id",type = IdType.AUTO)
    private Long auditListId;
    
    /**
     * 审核状态 0待审核|1已审核
     */
    @TableField("audit_status")
    private String auditStatus;
    
    /**
     * 提问
     */
    @TableField("question")
    private String question;
    
    /**
     * 大模型结果
     */
    @TableField("big_model_result")
    private String bigModelResult;
    
    /**
     * 1点赞|2点踩|0无
     */
    @TableField("like_or_no_like_result")
    private String likeOrNoLikeResult;
    
    /**
     * 反馈用户
     */
    @TableField("feed_back_user")
    private String feedBackUser;
    
    /**
     * 反馈时间
     */
    @TableField("feed_back_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime feedBackTime;
    
    /**
     * 会话id
     */
    @TableField("session_id")
    private String sessionId;
    
    /**
     * 数据模型id
     */
    @TableField("data_model_id")
    private String dataModelId;
    
    /**
     * 创建人
     */
    @TableField("create_user_name")
    private String createUserName;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField("update_user_name")
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 旧的正确sql
     */
    @TableField("old_correct_sql")
    private String oldCorrectSql;
    
    /**
     * 新的正确sql
     */
    @TableField("new_correct_sql")
    private String newCorrectSql;
    
    /**
     * 正确sql
     */
    @TableField("correct_sql")
    private String correctSql;
    
    /**
     * 业务知识
     */
    @TableField("professional_knowledge")
    private String professionalKnowledge;

    /**
     * 答案id
     */
    @TableField("answer_id")
    private String answerId;
} 