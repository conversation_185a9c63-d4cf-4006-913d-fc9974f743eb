package com.ffcs.oss.service;

import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.web.rest.evt.datasourceconfig.DataSourceConfigEvt;
import com.ffcs.oss.web.rest.vm.datasourceconfig.DataSourceConfigVm;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2023/8/7 18:16
 */
public interface DataSourceConfigService {
    /**
     * 获取数源配置列表
     *
     * @param evt
     * @return
     */
    QueryPageVm<DataSourceConfigVm> getDataSourceConfigList(DataSourceConfigEvt evt);

    /**
     * 增加或修改数源配置
     *
     * @param evt
     * @return
     */
    ServiceResp addOrUpdatetDataSourceConfig(DataSourceConfigEvt evt);

    /**
     * 删除数源配置
     *
     * @param evt
     * @return
     */
    ServiceResp deleteDataSourceConfig(DataSourceConfigEvt evt);

    /**
     * 更新数源状态
     * @param evt
     * @return
     */
    ServiceResp updateDataSourceStatus(DataSourceConfigEvt evt);
}
