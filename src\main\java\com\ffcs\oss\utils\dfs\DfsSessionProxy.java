package com.ffcs.oss.utils.dfs;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.InputStream;

/**
 * DFS会话代理类
 */
public class DfsSessionProxy implements DfsSession {
    
    private static final Logger log = LoggerFactory.getLogger(DfsSessionProxy.class);
    
    /**
     * HTTP类型
     */
    public static final String HTTP_TYPE = "http";
    
    /**
     * SFTP类型
     */
    public static final String SFTP_TYPE = "sftp";
    
    /**
     * DFS会话实现
     */
    private final DfsSession dfsSession;
    
    /**
     * 构造函数
     * 
     * @param type 会话类型
     * @param connInfo 连接信息
     */
    public DfsSessionProxy(String type, DfsConnInfo connInfo) {
        log.info("创建DFS会话: type={}", type);
        if (HTTP_TYPE.equalsIgnoreCase(type)) {
            this.dfsSession = new HttpSession(connInfo);
            log.info("使用HTTP协议连接DFS: {}", connInfo.getHost());
        } else if (SFTP_TYPE.equalsIgnoreCase(type)) {
            this.dfsSession = new SftpSession(connInfo);
            log.info("使用SFTP协议连接DFS: {}", connInfo.getHost());
        } else {
            log.warn("不支持的DFS类型: {}，使用默认HTTP类型", type);
            this.dfsSession = new HttpSession(connInfo);
        }
    }
    
    @Override
    public void connect() {
        dfsSession.connect();
    }
    
    @Override
    public void destroy() {
        dfsSession.destroy();
    }
    
    @Override
    public boolean exists(String filename) {
        return dfsSession.exists(filename);
    }
    
    @Override
    public void downloadFile(String remoteFilename, File localFile) {
        dfsSession.downloadFile(remoteFilename, localFile);
    }
    
    @Override
    public InputStream downloadStream(String remoteFilename) {
        return dfsSession.downloadStream(remoteFilename);
    }
    
    @Override
    public void uploadFile(File localFile, String remoteFilename) {
        dfsSession.uploadFile(localFile, remoteFilename);
    }
    
    @Override
    public Boolean uploadFile(String remoteFilename, InputStream inputStream) {
        return dfsSession.uploadFile(remoteFilename, inputStream);
    }
    
    @Override
    public void deleteFile(String filename) {
        dfsSession.deleteFile(filename);
    }
    
    @Override
    public void mkDirs(String dir) {
        dfsSession.mkDirs(dir);
    }
} 