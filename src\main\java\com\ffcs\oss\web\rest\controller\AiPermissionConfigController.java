package com.ffcs.oss.web.rest.controller;

import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.service.AiPermissionConfigService;
import com.ffcs.oss.service.UserService;
import com.ffcs.oss.web.rest.evt.permission.PermissionConfigEvt;
import com.ffcs.oss.web.rest.vm.user.UserGroupVm;
import com.ffcs.oss.web.rest.vm.user.UserVm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限配置控制器
 */
@RestController
@RequestMapping(value = "/api/permission")
@ApiModel("权限配置")
public class AiPermissionConfigController {
    
    @Autowired
    private AiPermissionConfigService permissionConfigService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取权限配置列表
     */
    @PostMapping("/getPermissionConfigList")
    @ApiOperation("获取权限配置列表")
    public ServiceResp getPermissionConfigList(@RequestBody PermissionConfigEvt evt) {
        return ServiceResp.getInstance().success(permissionConfigService.getPermissionConfigList(evt));
    }
    
//    /**
//     * 批量添加权限配置
//     */
//    @PostMapping("/batchAddPermissionConfig")
//    @ApiOperation("批量添加权限配置")
//    public ServiceResp batchAddPermissionConfig(@RequestBody PermissionConfigEvt evt) {
//        return permissionConfigService.batchAddPermissionConfig(evt);
//    }

    /**
     * 批量添加权限配置（带级联分配）
     * 当分配助理权限时，自动分配关联的数据模型和数据源权限
     */
    @PostMapping("/batchAddPermissionConfig")
    @ApiOperation("批量添加权限配置（带级联分配）")
    public ServiceResp batchAddPermissionConfigWithCascade(@RequestBody PermissionConfigEvt evt) {
        return permissionConfigService.batchAddPermissionConfigWithCascade(evt);
    }

//    /**
//     * 删除权限配置
//     */
//    @PostMapping("/deletePermissionConfig")
//    @ApiOperation("删除权限配置")
//    public ServiceResp deletePermissionConfig(@RequestBody PermissionConfigEvt evt) {
//        return permissionConfigService.deletePermissionConfig(evt);
//    }

    /**
     * 删除权限配置（带级联处理）
     * 当删除助理权限时，检查并清理不再需要的数据模型和数据源权限
     */
    @PostMapping("/deletePermissionConfig")
    @ApiOperation("删除权限配置（带级联处理）")
    public ServiceResp deletePermissionConfigWithCascade(@RequestBody PermissionConfigEvt evt) {
        return permissionConfigService.deletePermissionConfigWithCascade(evt);
    }
    
    /**
     * 搜索用户
     */
    @GetMapping("/searchUsers")
    @ApiOperation("搜索用户")
    public ServiceResp searchUsers(@RequestParam(value = "keyword", required = false) String keyword) {
        List<UserVm> userList = userService.searchUsers(keyword);
        return ServiceResp.getInstance().success(userList);
    }
    
    /**
     * 搜索用户组
     */
    @GetMapping("/searchUserGroups")
    @ApiOperation("搜索用户组")
    public ServiceResp searchUserGroups(@RequestParam(value = "keyword", required = false) String keyword) {
        List<UserGroupVm> groupList = userService.searchUserGroups(keyword);
        return ServiceResp.getInstance().success(groupList);
    }
    
    /**
     * 检查用户是否有资源的管理权限
     */
    @GetMapping("/hasAdminPermission")
    @ApiOperation("检查当前登录用户是否有资源的管理权限")
    public ServiceResp hasAdminPermission(
            @RequestParam("resourceType") Integer resourceType,
            @RequestParam("resourceId") String resourceId) {
        String currentUserName = userService.getCurrentUserName();
        boolean hasPermission = permissionConfigService.hasAdminPermission(resourceType, resourceId, currentUserName);
        return ServiceResp.getInstance().success(hasPermission);
    }
    
    /**
     * 检查用户是否有资源的使用权限
     */
    @GetMapping("/hasUsePermission")
    @ApiOperation("检查当前登录用户是否有资源的使用权限")
    public ServiceResp hasUsePermission(
            @RequestParam("resourceType") Integer resourceType,
            @RequestParam("resourceId") String resourceId) {
        String currentUserName = userService.getCurrentUserName();
        boolean hasPermission = permissionConfigService.hasUsePermission(resourceType, resourceId, currentUserName);
        return ServiceResp.getInstance().success(hasPermission);
    }
} 