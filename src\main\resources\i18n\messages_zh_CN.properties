test=\u672C\u57302-\u6D4B\u8BD5--2(CH)
match.words.cannot.repeated= \u8BCD\u6761\u7EC4\u4E0D\u80FD\u91CD\u590D
params.is.not.null=\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
export.serial.number=\u6D4B\u8BD5\u6570\u636E
rule.title.cannot.repeated=\u8BE5\u89C4\u5219\u6807\u9898\u5DF2\u5B58\u5728
#\u679A\u4E3E\u503C
enum.yesno.no=\u5426
enum.yesno.yes=\u662F
#\u679A\u4E3E\u7C7B\u4E2D\u663E\u793A\u5B57\u6BB5
enum.status.enabled=\u542F\u7528
enum.status.disabled=\u7981\u7528
#log info
log.operate.fail=\u64CD\u4F5C\u5F02\u5E38,\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
log.output.param=\u51FD\u6570\u540D\u3010{}\u3011\u5165\u53C2\u4FE1\u606F\u3010{}\u3011
log.output.excep=\u51FD\u6570\u540D\u3010{}\u3011\u5165\u53C2\u4FE1\u606F\u3010{}\u3011\u5F02\u5E38\u4FE1\u606F:
#common
operate.success=\u64CD\u4F5C\u6210\u529F
operate.exception=\u64CD\u4F5C\u5F02\u5E38
operate.fail=\u64CD\u4F5C\u5931\u8D25
query.success=\u67E5\u8BE2\u6210\u529F
query.exception=\u67E5\u8BE2\u5F02\u5E38
common.param.error=\u5165\u53C2\u9519\u8BEF
common.param.empty={0}\u4E0D\u80FD\u4E3A\u7A7A
#cs-sm-qry-srv
menu.name.exist=\u83DC\u5355\u540D\u79F0\u5DF2\u5B58\u5728
menu.code.exist=\u83DC\u5355\u7F16\u7801\u5DF2\u5B58\u5728
menu.permission.code.exist=\u529F\u80FD\u6743\u9650\u7801\u5DF2\u7ECF\u5B58\u5728
menu.check.pass=\u9A8C\u8BC1\u901A\u8FC7
ctgCache.init.fail=CTG-CACH\u5B9E\u4F8B\u83B7\u53D6\u5931\u8D25
user.not.exist=\u7528\u6237\u4E0D\u5B58\u5728
user.userIdAndUsername.not.both.blank=\u7528\u6237id\u548C\u7528\u6237\u540D\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
#portal-service
pwd.oldPwd.empty=\u65E7\u5BC6\u7801\u4E3A\u7A7A
pwd.newPwd.empty=\u65B0\u5BC6\u7801\u4E3A\u7A7A
pwd.oldNewPwd.same=\u65B0\u65E7\u5BC6\u7801\u76F8\u540C
pwd.oldPwd.error=\u65E7\u5BC6\u7801\u9519\u8BEF
pwd.used.repeat={}\u6B21\u5185\u5DF2\u4F7F\u7528\u8BE5\u5BC6\u7801
login.usernameOrPwd.empty=\u7528\u6237\u540D\u6216\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A
login.usernameOrPwdOrCode.empty=\u7528\u6237\u540D,\u5BC6\u7801\u548C\u9A8C\u8BC1\u7801\u90FD\u4E0D\u80FD\u4E3A\u7A7A
login.phoneOrCode.empty=\u624B\u673A\u53F7\u9A8C\u8BC1\u7801\u4E0D\u80FD\u90FD\u4E3A\u7A7A
login.usernameOrPwd.error=\u7528\u6237\u5F03\u7528\u6216\u7528\u6237\u540D\u5BC6\u7801\u9519\u8BEF
login.phone.error=\u624B\u673A\u53F7\u7801\u9519\u8BEF
login.code.empty=\u9A8C\u8BC1\u7801\u4E0D\u80FD\u4E3A\u7A7A
login.code.error=\u9A8C\u8BC1\u7801\u9519\u8BEF2
login.msg.lock=\u7528\u6237\u88AB\u9501\u5B9A
login.msg.loginTimeout=\u7528\u6237\u957F\u65F6\u95F4\u672A\u767B\u9646\u8D26\u53F7\u88AB\u9501\u5B9A
login.msg.pwdTimeout=\u7528\u6237\u957F\u65F6\u95F4\u4FEE\u6539\u5BC6\u7801\u8D26\u53F7\u88AB\u9501\u5B9A
login.msg.accountEffective=\u8D26\u53F7\u6709\u6548\u671F\u5DF2\u8FC7\u8D26\u53F7\u88AB\u9501\u5B9A
login.msg.loginErrorLimi=\u767B\u5F55\u9519\u8BEF\u6B21\u6570\u8FBE\u5230\u9650\u5236\u8D26\u53F7\u88AB\u9501\u5B9A
login.msg.cancel=\u8D26\u53F7\u6CE8\u9500
login.msg.userDisabeld=\u8D26\u53F7\u4E0D\u53EF\u7528
login.msg.userChecked=\u8D26\u53F7\u5F85\u5BA1\u6838
login.msg.updPwd=\u66F4\u65B0\u5BC6\u7801
login.username.empty=\u7528\u6237\u540D\u4E3A\u7A7A
login.get.phone.first=\u8BF7\u5148\u83B7\u53D6\u9A8C\u8BC1\u7801
login.code.valid=\u9A8C\u8BC1\u7801\u8FD8\u5728\u6709\u6548\u671F\u5185
login.code.invalid=\u9A8C\u8BC1\u7801\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u83B7\u53D6\u9A8C\u8BC1\u7801
login.code.sms.send=\u5DF2\u53D1\u9001\u81F3\u60A8\u624B\u673A\uFF0C\u8BF7\u67E5\u6536
login.code.sms.error=\u77ED\u4FE1\u53D1\u9001\u5F02\u5E38\uFF0C\u8BF7\u4F7F\u7528\u8D26\u53F7\u5BC6\u7801\u767B\u5F55
registry.loginName.repeat=\u7528\u6237\u540D\u5DF2\u5B58\u5728
registry.loginName.legal=\u7528\u6237\u540D\u53EA\u80FD\u5305\u542B\u6570\u5B57\u3001\u5B57\u6BCD\u3001\u4E0B\u5212\u7EBF
registry.phone.illegal=\u624B\u673A\u53F7\u4E0D\u6B63\u786E
registry.phone.null=\u624B\u673A\u53F7\u4E3A\u7A7A
registry.phone.exist=\u624B\u673A\u53F7\u5DF2\u5B58\u5728
registry.email.illegal=\u90AE\u7BB1\u4E0D\u6B63\u786E
registry.pid.illegal=\u8EAB\u4EFD\u8BC1\u683C\u5F0F\u4E0D\u6B63\u786E
registry.code.sms.illegal=\u7528\u6237\u540D\u6216\u624B\u673A\u53F7\u7801\u9519\u8BEF
registry.unlock.status.error=\u8BE5\u8D26\u53F7\u672A\u88AB\u9501\u5B9A
registry.forget.pwd.status.error=\u8BE5\u8D26\u53F7\u4E0D\u5B58\u5728\u6216\u4E0D\u53EF\u7528
#oss-setting-server
catalog.delete.to.belong=\u8BF7\u5230\u6240\u5C5E\u76EE\u5F55\u5220\u9664\u6587\u7AE0
log.for.login=\u767B\u9646\u65E5\u5FD7
log.for.system=\u7CFB\u7EDF\u65E5\u5FD7
log.for.operate=\u64CD\u4F5C\u65E5\u5FD7
export.all.icon.info=\u6240\u6709\u56FE\u6807\u6570\u636E
export.select.icon.info=\u9009\u4E2D\u56FE\u6807\u6570\u636E
online.user.info=\u5728\u7EBF\u7528\u6237\u4FE1\u606F
role.delError.bind.userOrGroup=\u5220\u9664\u5931\u8D25,\u8BE5\u89D2\u8272\u6709\u7ED1\u5B9A\u7684\u7528\u6237\u6216\u7528\u6237\u7EC4
role.dirName.exist=\u76EE\u5F55\u540D\u79F0\u5DF2\u5B58\u5728
role.name.exist=\u89D2\u8272\u540D\u79F0\u5DF2\u5B58\u5728
role.roleIdEqualsParentId=\u6240\u5C5E\u76EE\u5F55\u4E0D\u80FD\u9009\u62E9\u5F53\u524D\u7F16\u8F91\u7684\u76EE\u5F55\u672C\u8EAB,\u8BF7\u91CD\u65B0\u9009\u62E9\u76EE\u5F55\u6811
group.name.exist=\u7528\u6237\u7EC4\u540D\u79F0\u5DF2\u5B58\u5728
scene-config-info.not.search=\u573A\u666F\u914D\u7F6E\u4FE1\u606F\u4E3A\u67E5\u5230

#smart-workbench-web
no.shift.leader.shift=\u8BE5\u73ED\u6B21\u6CA1\u6709\u503C\u73ED\u957F\uFF0C\u65E0\u6CD5\u8BC4\u4EF7
more.than.one.shif=\u8BE5\u73ED\u6B21\u6709\u591A\u4E2A\u503C\u73ED\u957F\uFF0C\u65E0\u6CD5\u8BC4\u4EF7
not.the.monitor.not.be.evaluated=\u53EA\u6709\u8BE5\u73ED\u6B21\u503C\u73ED\u957F\u624D\u80FD\u8BC4\u4EF7\uFF01
shift.leader.unable.evaluate.himself=\u503C\u73ED\u957F\u65E0\u6CD5\u5BF9\u81EA\u5DF1\u8BC4\u4EF7
evaluate.success=\u8BC4\u4EF7\u6210\u529F
evaluate.exception=\u8BC4\u4EF7\u5F02\u5E38

primarykey.id.cannot.be.empty=\u4E3B\u952EID\u4E0D\u80FD\u4E3A\u7A7A
information.pushed.cannot.modified=\u4FE1\u606F\u5DF2\u63A8\u9001\u65E0\u6CD5\u4FEE\u6539
information.pushed.all=\u4FE1\u606F\u5DF2\u7ECF\u662F\u5168\u5458\u63A8\u9001
information.pushed.cannot.pushed.further=\u4FE1\u606F\u5DF2\u63A8\u9001\u65E0\u6CD5\u7EE7\u7EED\u63A8\u9001
not.creator.administrator.without.permission=\u4E0D\u662F\u521B\u5EFA\u8005\u548C\u8D85\u7EA7\u7BA1\u7406\u5458\uFF0C\u6CA1\u6709\u6743\u9650
please.upload.the.necessary.parameters=\u8BF7\u4E0A\u4F20\u5FC5\u8981\u53C2\u6570
no.such.plan=\u65E0\u6B64\u9884\u6848
mobile.number.added.cannot.added.repeatedly=\u8BE5\u624B\u673A\u53F7\u5DF2\u6DFB\u52A0\u4E0D\u80FD\u91CD\u590D\u6DFB\u52A0
previously.added=\u4E4B\u524D\u5DF2\u6DFB\u52A0

## cs-pp-config-web
#\u901A\u7528
log.common.save.success=\u4FDD\u5B58\u6210\u529F
log.common.save.error=\u4FDD\u5B58\u5931\u8D25
log.common.del.success=\u5220\u9664\u6210\u529F
log.common.del.error=\u5220\u9664\u5931\u8D25
log.common.search.user.error=\u67E5\u8BE2\u7528\u6237\u5931\u8D25

log.common.opr.success=\u4FDD\u5B58\u6210\u529F
log.common.opr.error=\u4FDD\u5B58\u5931\u8D25

#\u4E1A\u52A1\u5E73\u53F0
log.yw.only.sync=\u8BBE\u5907\u540C\u6B65\u53EA\u80FD\u6709\u4E00\u4E2A
log.yw.only.service=\u5BC6\u7801\u56DE\u8C03\u670D\u52A1\u6807\u8BC6\u4E0D\u80FD\u91CD\u590D

#\u8BBE\u5907\u7BA1\u7406
log.equip.sync.null=\u6CA1\u6709\u9700\u8981\u540C\u6B65\u7684\u6570\u636E
log.equip.sync.all.fail=\u540C\u6B65\u5E94\u7528{}\u5168\u90E8\u5931\u8D25
log.equip.sync.fail=\u5E94\u7528[{}]\u63A5\u53E3\u8C03\u7528\u5931\u8D25
log.equip.sync.no.find=\u6CA1\u6709\u627E\u5230\u5E94\u7528[{}]\u7684\u540C\u6B65\u63A5\u53E3
operate.zero.records=\u64CD\u4F5C0\u6761\u8BB0\u5F55
name.cannot.repeated=\u540D\u79F0\u4E0D\u80FD\u91CD\u590D
check.exception=\u6821\u9A8C\u53D1\u751F\u5F02\u5E38
no.permission=\u65E0\u6743\u9650