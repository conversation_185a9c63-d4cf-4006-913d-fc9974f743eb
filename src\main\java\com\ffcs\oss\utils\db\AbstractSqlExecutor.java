package com.ffcs.oss.utils.db;

import com.ffcs.oss.web.rest.vm.sqlexec.SqlExecutionResultVm;
import com.ffcs.oss.web.rest.vm.sqlexec.SqlExecutionResultVm.ColumnInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.*;
import java.util.*;

/**
 * 抽象SQL执行器实现
 */
public abstract class AbstractSqlExecutor implements SqlExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(AbstractSqlExecutor.class);
    
    protected Connection connection;
    
    @Autowired(required = false)
    private SqlValidator sqlValidator;
    
    /**
     * 执行SQL语句
     *
     * @param sql     SQL语句
     * @param maxRows 最大返回行数
     * @return SQL执行结果
     */
    @Override
    public SqlExecutionResultVm execute(String sql, int maxRows) {
        SqlExecutionResultVm result = new SqlExecutionResultVm();
        result.setSuccess(false);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 再次验证SQL语句类型（双重验证，防止绕过服务层验证）
            if (sqlValidator != null) {
                String validationError = sqlValidator.validate(sql);
                if (validationError != null) {
                    logger.warn("SQL验证未通过: {}, SQL: {}", validationError, sql);
                    result.setErrorMessage(validationError);
                    result.setExecutionTime(System.currentTimeMillis() - startTime);
                    return result;
                }
            }
            
            if (connection == null || connection.isClosed()) {
                connection = createConnection();
            }
            
            try (Statement stmt = connection.createStatement()) {
                stmt.setMaxRows(maxRows);
                
                boolean isQuery = stmt.execute(sql);
                
                if (isQuery) {
                    // 查询操作
                    try (ResultSet rs = stmt.getResultSet()) {
                        ResultSetMetaData metaData = rs.getMetaData();
                        int columnCount = metaData.getColumnCount();
                        
                        // 设置列信息
                        List<ColumnInfo> columns = new ArrayList<>();
                        for (int i = 1; i <= columnCount; i++) {
                            columns.add(new ColumnInfo(
                                    metaData.getColumnLabel(i),
                                    metaData.getColumnTypeName(i)
                            ));
                        }
                        result.setColumns(columns);
                        
                        // 设置数据行
                        List<Map<String, Object>> rows = new ArrayList<>();
                        while (rs.next()) {
                            Map<String, Object> row = new LinkedHashMap<>();
                            for (int i = 1; i <= columnCount; i++) {
                                row.put(metaData.getColumnLabel(i), rs.getObject(i));
                            }
                            rows.add(row);
                        }
                        result.setRows(rows);
                    }
                } else {
                    // 更新操作 - 如果启用了SQL验证，这部分代码不应被执行
                    result.setAffectedRows(stmt.getUpdateCount());
                }
                
                result.setSuccess(true);
            }
        } catch (Exception e) {
            logger.error("执行SQL出错: " + e.getMessage(), e);
            result.setErrorMessage(e.getMessage());
        } finally {
            result.setExecutionTime(System.currentTimeMillis() - startTime);
        }
        
        return result;
    }
    
    /**
     * 测试连接
     *
     * @return 是否连接成功
     */
    @Override
    public boolean testConnection() {
        try {
            Connection conn = createConnection();
            boolean isValid = conn.isValid(5); // 5秒超时
            conn.close();
            return isValid;
        } catch (Exception e) {
            logger.error("测试连接失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 关闭连接
     */
    @Override
    public void close() {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                logger.error("关闭连接出错: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 创建数据库连接
     *
     * @return 数据库连接
     * @throws SQLException 连接异常
     */
    protected abstract Connection createConnection() throws SQLException;
} 