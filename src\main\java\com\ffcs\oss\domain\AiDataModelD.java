package com.ffcs.oss.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * 数据模型表
 */
@Data
@TableName("ai_data_model_d")
public class AiDataModelD {
    
    /**
     * 主键
     */
    @TableId(value = "data_model_id",type = IdType.UUID)
    private String dataModelId;
    
    /**
     * 模型名称
     */
    @TableField("model_name")
    private String modelName;
    
    /**
     * 模型描述
     */
    @TableField("model_desc")
    private String modelDesc;
    
    /**
     * 关联的数据源ID
     */
    @TableField("connection_id")
    private BigInteger connectionId;
    
    /**
     * 创建人
     */
    @TableField("create_user_name")
    private String createUserName;
    
    /**
     * 创建人别名
     */
    @TableField("alias")
    private String alias;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField("update_user_name")
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
} 