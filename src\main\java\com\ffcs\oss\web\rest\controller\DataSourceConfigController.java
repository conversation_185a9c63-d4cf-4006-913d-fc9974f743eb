package com.ffcs.oss.web.rest.controller;

import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.service.DataSourceConfigService;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.datasourceconfig.DataSourceConfigEvt;
import com.ffcs.oss.web.rest.valid.DataSourceConfigDeleteValid;
import com.ffcs.oss.web.rest.valid.DataSourceConfigValid;
import com.univocity.parsers.annotations.Validate;
import io.swagger.annotations.ApiModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2023/8/7 16:38
 */
@RestController
@RequestMapping(value = "/api/dataSourceConfig")
@ApiModel("管理")
public class DataSourceConfigController {
    private DataSourceConfigService dataSourceConfigService;

    public DataSourceConfigController(DataSourceConfigService dataSourceConfigService) {
        this.dataSourceConfigService = dataSourceConfigService;
    }

    @PostMapping("/getDataSourceConfigList")
    public ServiceResp getDataSourceConfigList(@RequestBody DataSourceConfigEvt evt) {
        return ServiceResp.getInstance().success(dataSourceConfigService.getDataSourceConfigList(evt), LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    @PostMapping("/addOrUpdatetDataSourceConfig")
    public ServiceResp addOrUpdatetDataSourceConfig(@RequestBody @Validated(value = DataSourceConfigValid.class) DataSourceConfigEvt evt) {
        return dataSourceConfigService.addOrUpdatetDataSourceConfig(evt);
    }

    @PostMapping("/deleteDataSourceConfig")
    public ServiceResp deleteDataSourceConfig(@RequestBody @Validated(value = DataSourceConfigDeleteValid.class) DataSourceConfigEvt evt) {
        return dataSourceConfigService.deleteDataSourceConfig(evt);
    }

    @PostMapping("/updateDataSourceStatus")
    public ServiceResp updateDataSourceStatus(@RequestBody @Validated(value = DataSourceConfigDeleteValid.class) DataSourceConfigEvt evt) {
        return dataSourceConfigService.updateDataSourceStatus(evt);
    }
}
