package com.ffcs.oss.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * @Description 数据库连接配置实体类
 * <AUTHOR> @Date
 */

@TableName(value = "ai_db_connection_config")
public class AiDbConnectionConfigD implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "connection_id",type= IdType.AUTO)
    private BigInteger connectionId;

    /**
     * 连接名称
     */
    @TableField(value = "connection_name")
    private String connectionName;

    /**
     * 数据库类型
     */
    @TableField(value = "db_type")
    private String dbType;
    
    /**
     * 数据库版本
     */
    @TableField(value = "db_version")
    private String dbVersion;
    
    /**
     * 认证类型
     */
    @TableField(value = "auth_type")
    private String authType;

    /**
     * 主机地址
     */
    @TableField(value = "host")
    private String host;

    /**
     * 端口号
     */
    @TableField(value = "port")
    private Integer port;

    /**
     * 数据库名
     */
    @TableField(value = "database_name")
    private String databaseName;

    /**
     * 用户名
     */
    @TableField(value = "username")
    private String username;

    /**
     * 密码
     */
    @TableField(value = "password")
    private String password;
    
    /**
     * Kerberos principal
     */
    @TableField(value = "principal")
    private String principal;
    
    /**
     * Kerberos keytab文件内容
     */
    @TableField(value = "keytab_file")
    private byte[] keytabFile;
    
    /**
     * krb5.conf文件内容
     */
    @TableField(value = "krb5_conf_file")
    private byte[] krb5ConfFile;
    
    /**
     * Kerberos keytab文件名
     */
    @TableField(value = "keytab_filename")
    private String keytabFilename;
    
    /**
     * krb5.conf文件名
     */
    @TableField(value = "krb5_conf_filename")
    private String krb5ConfFilename;
    
    /**
     * Kerberos keytab文件路径
     */
    @TableField(value = "keytab_file_path")
    private String keytabFilePath;
    
    /**
     * krb5.conf文件路径
     */
    @TableField(value = "krb5_conf_file_path")
    private String krb5ConfFilePath;
    
    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 其他连接参数
     */
    @TableField(value = "params")
    private String params;

    /**
     * 创建人
     */
    @TableField(value = "creator_name")
    private String creatorName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "updater_name")
    private String updaterName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 数据库连接URL
     */
    @TableField(value = "url")
    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public BigInteger getConnectionId() {
        return connectionId;
    }

    public void setConnectionId(BigInteger connectionId) {
        this.connectionId = connectionId;
    }

    public String getConnectionName() {
        return connectionName;
    }

    public void setConnectionName(String connectionName) {
        this.connectionName = connectionName;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }
    
    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }
    
    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public byte[] getKeytabFile() {
        return keytabFile;
    }

    public void setKeytabFile(byte[] keytabFile) {
        this.keytabFile = keytabFile;
    }

    public byte[] getKrb5ConfFile() {
        return krb5ConfFile;
    }

    public void setKrb5ConfFile(byte[] krb5ConfFile) {
        this.krb5ConfFile = krb5ConfFile;
    }

    public String getKeytabFilename() {
        return keytabFilename;
    }

    public void setKeytabFilename(String keytabFilename) {
        this.keytabFilename = keytabFilename;
    }

    public String getKrb5ConfFilename() {
        return krb5ConfFilename;
    }

    public void setKrb5ConfFilename(String krb5ConfFilename) {
        this.krb5ConfFilename = krb5ConfFilename;
    }
    
    public String getKeytabFilePath() {
        return keytabFilePath;
    }

    public void setKeytabFilePath(String keytabFilePath) {
        this.keytabFilePath = keytabFilePath;
    }

    public String getKrb5ConfFilePath() {
        return krb5ConfFilePath;
    }

    public void setKrb5ConfFilePath(String krb5ConfFilePath) {
        this.krb5ConfFilePath = krb5ConfFilePath;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
} 