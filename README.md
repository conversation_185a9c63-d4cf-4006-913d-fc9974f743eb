# ai-chat-bi-web



## Getting started

To make it easy for you to get started with Git<PERSON>ab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin http://192.168.35.81/cp/ai/ai-chat-bi-web.git
git branch -M main
git push -uf origin main
```

## Integrate with your tools

- [ ] [Set up project integrations](http://192.168.35.81/cp/ai/ai-chat-bi-web/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Automatically merge when pipeline succeeds](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing(SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thank you to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README
Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.

# AI聊天BI系统功能说明

## 新增功能

### 1. 收藏功能

用户可以收藏感兴趣的问答，方便后续查看和继续对话。

#### API接口

- **添加收藏**: `/api/collection/addCollection`
  - 请求方式: POST
  - 请求参数: 
    ```json
    {
      "questionId": "问题ID",
      "answerId": "回答ID",
      "assistantId": "助理ID"
    }
    ```
  - 响应结果:
    ```json
    {
      "code": "200",
      "message": "收藏成功",
      "data": {
        "isCollected": true
      }
    }
    ```

- **取消收藏**: `/api/collection/removeCollection`
  - 请求方式: POST
  - 请求参数: 
    ```json
    {
      "questionId": "问题ID",
      "answerId": "回答ID"
    }
    ```
    或
    ```json
    {
      "collectionId": "收藏ID"
    }
    ```
  - 响应结果:
    ```json
    {
      "code": "200",
      "message": "取消收藏成功",
      "data": {
        "isCollected": false
      }
    }
    ```

- **获取助理的收藏列表**: `/api/collection/getCollectionsByAssistantId`
  - 请求方式: POST
  - 请求参数: 
    ```json
    {
      "assistantId": "助理ID"
    }
    ```
  - 响应结果: 收藏列表，包含问题和回答信息

- **获取收藏详情**: `/api/collection/getCollectionDetail`
  - 请求方式: POST
  - 请求参数: 
    ```json
    {
      "collectionId": "收藏ID"
    }
    ```
  - 响应结果: 收藏详情，包含问题和回答信息

- **检查是否已收藏**: `/api/collection/isCollected`
  - 请求方式: POST
  - 请求参数: 
    ```json
    {
      "questionId": "问题ID",
      "answerId": "回答ID"
    }
    ```
  - 响应结果:
    ```json
    {
      "code": "200",
      "message": "操作成功",
      "data": {
        "isCollected": true/false
      }
    }
    ```

- **创建新会话继续对话**: `/api/collection/continueConversation`
  - 请求方式: POST
  - 请求参数: 
    ```json
    {
      "collectionId": "收藏ID"
    }
    ```
  - 响应结果: 新创建的会话信息

### 2. 分享功能

用户可以分享问答内容，生成分享链接供其他人查看。

#### API接口

- **创建分享**: `/api/share/createShare`
  - 请求方式: POST
  - 请求参数: 
    ```json
    {
      "questionId": "问题ID",
      "answerId": "回答ID"
    }
    ```
  - 响应结果:
    ```json
    {
      "code": "200",
      "message": "已复制分享链接",
      "data": {
        "shareLink": "http://example.com/share/abc123"
      }
    }
    ```

- **获取分享详情**: `/api/share/getShareDetail/{shareId}`
  - 请求方式: GET
  - 请求参数: 路径参数 `shareId`
  - 响应结果: 分享详情，包含问题和回答信息

### 3. 导出功能

用户可以将问答内容导出为Word文档。

#### API接口

- **导出Word**: `/api/export/exportWord`
  - 请求方式: GET
  - 请求参数: 
    ```
    questionId=问题ID&answerId=回答ID
    ```
  - 响应结果: Word文档二进制流

### 4. 多轮对话功能

支持在提示词中包含上一轮对话信息，提高对话连贯性。

#### API接口

- **获取上一轮对话信息**: `/api/conversation/getLastConversationInfo`
  - 请求方式: POST
  - 请求参数: 
    ```json
    {
      "sessionId": "会话ID",
      "assistantId": "助理ID"
    }
    ```
  - 响应结果: 
    ```json
    {
      "code": "200",
      "message": "操作成功",
      "data": {
        "enableMultiTurn": true/false,
        "lastConversation": {
          "question": { /* 问题信息 */ },
          "answer": { /* 回答信息 */ }
        }
      }
    }
    ```

## 数据库表结构

### 收藏表 (ai_collection_d)

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| collection_id | VARCHAR(32) | 主键，收藏ID |
| assistant_id | BIGINT | 助理ID |
| question_id | VARCHAR(32) | 问题ID |
| answer_id | VARCHAR(32) | 回答ID |
| create_user_name | VARCHAR(50) | 创建人 |
| create_time | TIMESTAMP | 创建时间 |
| update_user_name | VARCHAR(50) | 更新人 |
| update_time | TIMESTAMP | 更新时间 |
| title | VARCHAR(500) | 标题 |
| description | TEXT | 描述 |

### 分享表 (ai_share_d)

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| share_id | VARCHAR(32) | 主键，分享ID |
| share_link | VARCHAR(500) | 分享链接 |
| question_id | VARCHAR(32) | 问题ID |
| answer_id | VARCHAR(32) | 回答ID |
| create_user_name | VARCHAR(50) | 创建人 |
| create_time | TIMESTAMP | 创建时间 |
| update_user_name | VARCHAR(50) | 更新人 |
| update_time | TIMESTAMP | 更新时间 |
| visit_count | INTEGER | 访问次数 |
| expire_time | TIMESTAMP | 过期时间 |

### 助理表 (ai_assistant_d) 新增字段

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| enable_multi_turn | VARCHAR(2) | 是否启用多轮对话 0-不启用 1-启用 |
