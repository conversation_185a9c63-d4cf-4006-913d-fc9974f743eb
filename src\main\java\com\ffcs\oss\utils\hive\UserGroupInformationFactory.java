package com.ffcs.oss.utils.hive;

import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Hadoop UserGroupInformation工厂类，用于创建和管理Kerberos认证的UGI实例
 */
public class UserGroupInformationFactory {

    private static final Logger log = LoggerFactory.getLogger(UserGroupInformationFactory.class);

    private static final String KERBEROS = "kerberos";
    private static final String JAVA_SECURITY_KRB5_CONF = "java.security.krb5.conf";
    private static final String HADOOP_SECURITY_AUTHENTICATION = "hadoop.security.authentication";
    
    private static final Map<String, Integer> currentLoginTimesMap = new HashMap<>();
    private static final Map<String, UserGroupInformation> userGroupInformationMap = new HashMap<>();

    private static final ScheduledExecutorService kerberosRenewalService =
            Executors.newSingleThreadScheduledExecutor(r -> {
                Thread thread = new Thread(r, "Hive-Kerberos-Renewal-Thread");
                thread.setDaemon(true);
                return thread;
            });

    static {
        // 定期刷新Kerberos票据
        kerberosRenewalService.scheduleWithFixedDelay(() -> {
            if (userGroupInformationMap.isEmpty()) {
                return;
            }
            userGroupInformationMap.forEach((key, ugi) -> {
                try {
                    if (ugi.isFromKeytab()) {
                        ugi.checkTGTAndReloginFromKeytab();
                    }
                    log.info("Relogin from keytab success, user: {}", key);
                } catch (Exception e) {
                    log.error("Relogin from keytab failed, user: {}", key, e);
                }
            });
        }, 0, 5, TimeUnit.MINUTES);
    }

    /**
     * 使用普通用户名登录
     * 
     * @param userName 用户名
     * @return UserGroupInformation实例
     */
    public synchronized static UserGroupInformation login(String userName) {
        return login(null, null, userName, false);
    }

    /**
     * 使用Kerberos认证登录
     * 
     * @param krb5File Kerberos配置文件路径
     * @param keytab 密钥表文件路径
     * @param principal Kerberos principal
     * @return UserGroupInformation实例
     */
    public synchronized static UserGroupInformation login(String krb5File, String keytab, String principal) {
        return login(krb5File, keytab, principal, true);
    }

    /**
     * 登录并获取UserGroupInformation实例
     * 
     * @param krb5File Kerberos配置文件路径
     * @param keytab 密钥表文件路径
     * @param user 用户名/principal
     * @param isKerberosEnabled 是否启用Kerberos认证
     * @return UserGroupInformation实例
     */
    public synchronized static UserGroupInformation login(String krb5File, String keytab, String user,
                                                          boolean isKerberosEnabled) {
        UserGroupInformation userGroupInformation = userGroupInformationMap.get(user);
        if (userGroupInformation == null) {
            if (!isKerberosEnabled) {
                userGroupInformation = createRemoteUser(user);
            } else {
                userGroupInformation = createKerberosUser(krb5File, keytab, user);
            }
            userGroupInformationMap.put(user, userGroupInformation);
        }
        currentLoginTimesMap.compute(user, (k, v) -> v == null ? 1 : v + 1);
        return userGroupInformation;
    }

    /**
     * 用户注销
     * 
     * @param userName 用户名/principal
     */
    public synchronized static void logout(String userName) {
        Integer currentLoginTimes = currentLoginTimesMap.get(userName);
        if (currentLoginTimes == null) {
            return;
        }
        if (currentLoginTimes <= 1) {
            currentLoginTimesMap.remove(userName);
            userGroupInformationMap.remove(userName);
        } else {
            currentLoginTimesMap.put(userName, currentLoginTimes - 1);
        }
    }

    /**
     * 创建远程用户实例
     * 
     * @param userName 用户名
     * @return UserGroupInformation实例
     */
    public static UserGroupInformation createRemoteUser(String userName) {
        return UserGroupInformation.createRemoteUser(userName);
    }

    /**
     * 创建Kerberos认证用户实例
     * 
     * @param krb5File Kerberos配置文件路径
     * @param keytab 密钥表文件路径
     * @param principal Kerberos principal
     * @return UserGroupInformation实例
     */
    public static UserGroupInformation createKerberosUser(String krb5File, String keytab, String principal) {
        if (StringUtils.isNotBlank(krb5File)) {
            System.setProperty(JAVA_SECURITY_KRB5_CONF, krb5File);
        }

        Configuration hadoopConf = new Configuration();
        hadoopConf.set(HADOOP_SECURITY_AUTHENTICATION, KERBEROS);

        try {
            UserGroupInformation.setConfiguration(hadoopConf);
            UserGroupInformation userGroupInformation =
                    UserGroupInformation.loginUserFromKeytabAndReturnUGI(principal.trim(), keytab.trim());
            UserGroupInformation.setLoginUser(userGroupInformation);
            return userGroupInformation;
        } catch (IOException e) {
            throw new RuntimeException("创建UserGroupInformation失败", e);
        }
    }
} 