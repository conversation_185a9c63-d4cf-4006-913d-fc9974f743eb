<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ffcs.oss.mapper.dbconnection.DbConnectionConfigMapper">
    <select id="getDbConnectionList" resultType="com.ffcs.oss.web.rest.vm.dbconnection.DbConnectionConfigVm">
        select
        connection_id,
        connection_name,
        db_type,
        db_version,
        auth_type,
        host,
        port,
        database_name,
        username,
        password,
        principal,
        description,
        params,
        creator_name,
        create_time,
        updater_name,
        update_time,
        case when keytab_file is not null then true else false end as has_keytab_file,
        case when krb5_conf_file is not null then true else false end as has_krb5_conf_file,
        keytab_filename,
        krb5_conf_filename,
        keytab_file_path,
        krb5_conf_file_path,
        url
        from
        ai_db_connection_config
        <where>
            <if test="evt.keyword != null and evt.keyword != ''">
                and (connection_name like concat('%', #{evt.keyword}, '%')
                or db_type like concat('%', #{evt.keyword}, '%')
                or description like concat('%', #{evt.keyword}, '%'))
            </if>
            <if test="evt.dbType != null and evt.dbType != ''">
                and db_type = #{evt.dbType}
            </if>
            <if test="evt.connectionId != null">
                and connection_id = #{evt.connectionId}
            </if>
        </where>
        order by update_time desc,create_time desc
    </select>
    
    <select id="nameWhetherRepeat" resultType="java.lang.Integer">
        select count(*) from ai_db_connection_config where connection_name = #{evt.connectionName}
        <if test="evt.connectionId != null">
            and connection_id != #{evt.connectionId}
        </if>
        and creator_name = #{evt.creatorName}
    </select>
</mapper> 