package com.ffcs.oss.web.rest.controller;

import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.service.SqlExecutionService;
import com.ffcs.oss.web.rest.evt.sqlexec.SqlExecutionEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SQL执行控制器
 */
@RestController
@RequestMapping(value = "/api/sql")
@ApiModel("SQL执行")
public class SqlExecutionController {
    
    private final SqlExecutionService sqlExecutionService;
    
    public SqlExecutionController(SqlExecutionService sqlExecutionService) {
        this.sqlExecutionService = sqlExecutionService;
    }
    
    /**
     * 执行SQL语句
     */
    @PostMapping("/execute")
    @ApiOperation("执行SQL")
    public ServiceResp executeSql(@RequestBody @Validated SqlExecutionEvt evt) {
        return sqlExecutionService.executeSql(evt);
    }
} 