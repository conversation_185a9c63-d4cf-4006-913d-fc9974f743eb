package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import com.ffcs.oss.domain.AiAnswerD;
import com.ffcs.oss.domain.AiQuestionD;
import com.ffcs.oss.mapper.AiQuestionMapper;
import com.ffcs.oss.service.AiAnswerService;
import com.ffcs.oss.service.AiQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 提问Service实现类
 */
@Service
public class AiQuestionServiceImpl extends ServiceImpl<AiQuestionMapper, AiQuestionD> implements AiQuestionService {
    
    @Autowired
    private AiAnswerService aiAnswerService;
    
    /**
     * 获取会话历史记录（包括问题和回答）
     * @param sessionId 会话ID
     * @return 会话历史记录列表
     */
    @Override
    public List<Map<String, Object>> getConversationHistoryWithAnswers(String sessionId) {
        List<Map<String, Object>> historyList = new ArrayList<>();
        
        // 获取会话下的所有问题
        LambdaQueryWrapper<AiQuestionD> questionQueryWrapper = new LambdaQueryWrapper<>();
        questionQueryWrapper.eq(AiQuestionD::getSessionId, sessionId);
        questionQueryWrapper.orderByAsc(AiQuestionD::getCreateTime); // 按创建时间升序排序
        List<AiQuestionD> questions = this.list(questionQueryWrapper);
        
        if (questions == null || questions.isEmpty()) {
            return historyList;
        }
        
        // 获取所有问题ID
        List<String> questionIds = questions.stream()
                .map(AiQuestionD::getQuestionId)
                .collect(Collectors.toList());
        
        // 获取这些问题对应的所有回答
        LambdaQueryWrapper<AiAnswerD> answerQueryWrapper = new LambdaQueryWrapper<>();
        answerQueryWrapper.in(AiAnswerD::getQuestionId, questionIds);
        List<AiAnswerD> answers = aiAnswerService.list(answerQueryWrapper);
        
        // 将回答按问题ID分组
        Map<String, List<AiAnswerD>> answerMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(answers)) {
            answerMap = answers.stream()
                    .collect(Collectors.groupingBy(AiAnswerD::getQuestionId));
        }
        
        // 组装结果
        for (AiQuestionD question : questions) {
            // 添加问题
            Map<String, Object> questionMap = new HashMap<>();
            questionMap.put("question", question);

            historyList.add(questionMap);

            // 添加该问题的回答
            List<AiAnswerD> questionAnswers = answerMap.get(question.getQuestionId());
            if (questionAnswers != null && !questionAnswers.isEmpty()) {
                for (AiAnswerD answer : questionAnswers) {
                    Map<String, Object> oldAnswerMap = new HashMap<>();
                    oldAnswerMap.put("answer", answer);
                    historyList.add(oldAnswerMap);
                }
            }
        }
        
        return historyList;
    }
} 