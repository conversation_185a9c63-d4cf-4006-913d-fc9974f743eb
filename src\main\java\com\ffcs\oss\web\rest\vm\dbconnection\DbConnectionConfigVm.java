package com.ffcs.oss.web.rest.vm.dbconnection;

import java.math.BigInteger;
import java.util.Date;

/**
 * 数据库连接配置ViewModel
 */
public class DbConnectionConfigVm {
    
    /**
     * 连接ID
     */
    private BigInteger connectionId;
    
    /**
     * 连接名称
     */
    private String connectionName;
    
    /**
     * 数据库类型
     */
    private String dbType;
    
    /**
     * 数据库版本
     */
    private String dbVersion;
    
    /**
     * 认证类型
     */
    private String authType;
    
    /**
     * 主机地址
     */
    private String host;
    
    /**
     * 端口号
     */
    private Integer port;
    
    /**
     * 数据库名
     */
    private String databaseName;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * Kerberos principal
     */
    private String principal;
    
    /**
     * 是否有Kerberos keytab文件
     */
    private Boolean hasKeytabFile;
    
    /**
     * 是否有krb5.conf文件
     */
    private Boolean hasKrb5ConfFile;
    
    /**
     * Kerberos keytab文件名
     */
    private String keytabFilename;
    
    /**
     * krb5.conf文件名
     */
    private String krb5ConfFilename;
    
    /**
     * Kerberos keytab文件路径
     */
    private String keytabFilePath;
    
    /**
     * krb5.conf文件路径
     */
    private String krb5ConfFilePath;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 其他连接参数
     */
    private String params;
    
    /**
     * 创建人
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updaterName;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否有管理权限
     */
    private Boolean isAdmin;
    private String url;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public BigInteger getConnectionId() {
        return connectionId;
    }

    public void setConnectionId(BigInteger connectionId) {
        this.connectionId = connectionId;
    }

    public String getConnectionName() {
        return connectionName;
    }

    public void setConnectionName(String connectionName) {
        this.connectionName = connectionName;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }
    
    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }
    
    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }
    
    public Boolean getHasKeytabFile() {
        return hasKeytabFile;
    }

    public void setHasKeytabFile(Boolean hasKeytabFile) {
        this.hasKeytabFile = hasKeytabFile;
    }

    public Boolean getHasKrb5ConfFile() {
        return hasKrb5ConfFile;
    }

    public void setHasKrb5ConfFile(Boolean hasKrb5ConfFile) {
        this.hasKrb5ConfFile = hasKrb5ConfFile;
    }
    
    public String getKeytabFilename() {
        return keytabFilename;
    }

    public void setKeytabFilename(String keytabFilename) {
        this.keytabFilename = keytabFilename;
    }

    public String getKrb5ConfFilename() {
        return krb5ConfFilename;
    }

    public void setKrb5ConfFilename(String krb5ConfFilename) {
        this.krb5ConfFilename = krb5ConfFilename;
    }
    
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public Boolean getIsAdmin() {
        return isAdmin;
    }
    
    public void setIsAdmin(Boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

    public String getKeytabFilePath() {
        return keytabFilePath;
    }

    public void setKeytabFilePath(String keytabFilePath) {
        this.keytabFilePath = keytabFilePath;
    }

    public String getKrb5ConfFilePath() {
        return krb5ConfFilePath;
    }

    public void setKrb5ConfFilePath(String krb5ConfFilePath) {
        this.krb5ConfFilePath = krb5ConfFilePath;
    }
} 