package com.ffcs.oss.web.rest.evt.chat;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 聊天会话请求参数
 */
@ApiModel("聊天会话请求参数")
public class ChatManageEvt extends QueryPageEvt implements Serializable {
    
    @ApiModelProperty("会话id")
    private String sessionId;
    
    @ApiModelProperty("会话名称")
    private String sessionName;
    
    @ApiModelProperty("助理id")
    private Long assistantId;
    
    @ApiModelProperty("创建人")
    private String createUserName;
    
    @ApiModelProperty("更新人")
    private String updateUserName;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSessionName() {
        return sessionName;
    }

    public void setSessionName(String sessionName) {
        this.sessionName = sessionName;
    }

    public Long getAssistantId() {
        return assistantId;
    }

    public void setAssistantId(Long assistantId) {
        this.assistantId = assistantId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }
} 