package com.ffcs.oss.utils.dfs;

import org.springframework.util.StringUtils;

/**
 * HTTP URL 工具类
 */
public class HttpUrlUtils {
    
    /**
     * 获取文件URL
     * 
     * @param host 主机地址
     * @param port 端口
     * @param scheme 协议
     * @param point 节点
     * @return 完整URL
     */
    public static String getFileUrl(String host, Integer port, String scheme, String point) {
        StringBuilder sb = new StringBuilder();
        
        if (StringUtils.hasText(scheme) && !"http".equalsIgnoreCase(scheme) && !"https".equalsIgnoreCase(scheme)) {
            sb.append(scheme).append("://");
        } else {
            sb.append("http://");
        }
        
        sb.append(host).append(":").append(port);
        
        if (StringUtils.hasText(point)) {
            sb.append("/").append(point);
        }
        
        return sb.toString();
    }
} 