spring:
  application:
    # 应用名称
    name: ai-chat-bi-web
  devtools:
    restart:
      # 解决jar包中entity 表名无法识别问题
      enabled: false
  main:
    ##解决spring boot 2.6后，因循环引用导致启动报错
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: *******************************************************************************
    username: user_common
    #    password: FfcsPg#1107 FfcsPg#0112
    password: ENC(eGmAqNkWuQ/LEDbN40Y97WabbYZKqnUX)

#密码加密串
jasypt:
  encryptor:
    password: ffcsoss

locale:
  #en-us zh-cn
  type: zh-cn

# 与mybatis整合
mybatis-plus:
  mapper-locations: classpath:mapper/*/*.xml,classpath:mapper/*.xml
  type-aliases-package: com.ffcs.oss.domain
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    auto-mapping-behavior: full
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
  global-config:
    db-config:
      id-type: auto
##部门id主键配置
oss:
  database:
    id-type: leaf
server:
  port: 9095
#http://localhost:9095/swagger-ui/index.html sweagger地址

# 小文件系统配置
ctdfs:
  # 是否启用小文件系统
  enabled: true
  # 小文件系统主机地址
  host: **************
  # 端口
  port: 22
  # 用户名
  userName: ffcs_zstp
  # 密码
  password: JBDw7@xP4Nvs
  # 基础URL - 使用确定可写的路径
  baseUrl: /ftp/ai-chat-bi-web/file/hive_files
  # REST端口
  restPort: 8995
  # REST密码
  restPassword: JBDw7@xP4Nvs
  # 时间差
  timeDiff: 2000
  # 方案
  scheme: http
  # 点
  point: aaa
  # 类型 (http或sftp)
  type: sftp
  # 加密方式
  encryption: MD5
  # Minio配置
  endpoint: http://**************:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: ai-chat-bi-web

jhipster:
  cache: # Cache configuration
    hazelcast: # Hazelcast distributed cache
      time-to-live-seconds: 3600
      backup-count: 1
      management-center: # Full reference is available at: http://docs.hazelcast.org/docs/management-center/3.9/manual/html/Deploying_and_Starting.html
        enabled: false
        update-interval: 3
        url: http://localhost:8180/mancenter
  # CORS is disabled by default on microservices, as you should access them through a gateway.
  # If you want to enable it, please uncomment the configuration below.
  # cors:
  #     allowed-origins: "*"
  #     allowed-methods: "*"
  #     allowed-headers: "*"
  #     exposed-headers: "Authorization,Link,X-Total-Count"
  #     allow-credentials: true
  #     max-age: 1800
  security:
    client-authorization:
      access-token-uri: http://uaa/oauth/token
      token-service-id: uaa
      client-id: internal
      client-secret: internal
  mail: # specific JHipster mail property, for standard properties see MailProperties
    base-url: http://127.0.0.1:8081
  metrics:
    logs: # Reports metrics in the logs
      enabled: false
      report-frequency: 60 # in seconds
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      queue-size: 512
  audit-events:
    retention-period: 30 # Number of days before audit events are deleted.
oauth2:
  signature-verification:
    public-key-endpoint-uri: http://uaa/oauth/token_key
    #ttl for public keys to verify JWT tokens (in ms)
    ttl: 3600000
    #max. rate at which public keys will be fetched (in ms)
    public-key-refresh-rate-limit: 10000
  web-client-configuration:
    #keep in sync with UAA configuration
    client-id: web_app
    secret: changeit