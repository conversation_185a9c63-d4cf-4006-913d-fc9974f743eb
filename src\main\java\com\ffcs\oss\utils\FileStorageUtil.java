package com.ffcs.oss.utils;

import com.ffcs.oss.config.CtdfsConfig;
import com.ffcs.oss.utils.dfs.CtdfsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * 文件存储工具类
 * 用于处理文件的存储和管理
 */
@Component
public class FileStorageUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(FileStorageUtil.class);
    
    /**
     * 默认文件存储根目录
     */
    private static final String DEFAULT_STORAGE_PATH = "kerberos_files";
    
    /**
     * keytab文件存储子目录
     */
    private static final String KEYTAB_SUBDIR = "keytab";
    
    /**
     * krb5.conf文件存储子目录
     */
    private static final String KRB5_CONF_SUBDIR = "krb5conf";
    
    /**
     * 临时文件目录
     */
    private static final String TEMP_DIR = "temp";

    /**
     * 小文件系统根目录
     */
    private static final String CTDFS_ROOT_PATH = "";

    /**
     * keytab文件在小文件系统中的子目录
     */
    private static final String CTDFS_KEYTAB_SUBDIR = "";

    /**
     * krb5.conf文件在小文件系统中的子目录
     */
    private static final String CTDFS_KRB5_CONF_SUBDIR = "";
    
    /**
     * 小文件系统配置
     */
    private static CtdfsConfig ctdfsConfig;
    
    /**
     * 小文件系统服务
     */
    private static CtdfsService ctdfsService;
    
    @Autowired
    public void setCtdfsConfig(CtdfsConfig ctdfsConfig) {
        FileStorageUtil.ctdfsConfig = ctdfsConfig;
    }
    
    @Autowired
    public void setCtdfsService(CtdfsService ctdfsService) {
        FileStorageUtil.ctdfsService = ctdfsService;
    }
    
    /**
     * 创建必要的目录结构
     */
    static {
        try {
            createDirectoryIfNotExists(DEFAULT_STORAGE_PATH);
            createDirectoryIfNotExists(DEFAULT_STORAGE_PATH + File.separator + KEYTAB_SUBDIR);
            createDirectoryIfNotExists(DEFAULT_STORAGE_PATH + File.separator + KRB5_CONF_SUBDIR);
            createDirectoryIfNotExists(DEFAULT_STORAGE_PATH + File.separator + TEMP_DIR);
        } catch (IOException e) {
            logger.error("初始化文件存储目录结构失败", e);
        }
    }
    
    /**
     * 保存keytab文件到磁盘或小文件系统
     * 
     * @param keytabFile keytab文件
     * @param connectionId 连接ID
     * @return 文件存储路径
     * @throws IOException IO异常
     */
    public static String saveKeytabFile(MultipartFile keytabFile, String connectionId) throws IOException {
        if (keytabFile == null || keytabFile.isEmpty()) {
            return null;
        }
        
        String fileName = generateFileName(keytabFile.getOriginalFilename(), connectionId);
        
        // 判断是否启用小文件系统
        if (isCtdfsEnabled()) {
            // 使用baseUrl作为存储位置
            String remoteFilePath = ctdfsConfig.getBaseUrl() + "/" + fileName;
            CtdfsService service = getCtdfsService();
            if (service != null) {
                try {
                    String filePath = service.uploadFile(remoteFilePath, fileName, keytabFile.getInputStream());
                    if (filePath != null) {
                        logger.info("文件已上传到小文件系统: {}", filePath);
                        return filePath;
                    } else {
                        logger.warn("小文件系统返回的路径为空，降级到本地文件系统");
                    }
                } catch (Exception e) {
                    logger.warn("上传文件到小文件系统失败，降级到本地文件系统: {}", e.getMessage());
                }
            } else {
                // 降级到本地文件系统
                logger.warn("小文件系统服务不可用，降级到本地文件系统");
            }
        }
        
        // 使用本地文件系统
        String filePath = DEFAULT_STORAGE_PATH + File.separator + KEYTAB_SUBDIR + File.separator + fileName;
        
        try {
            saveFile(keytabFile.getBytes(), filePath);
            logger.info("文件已保存到本地: {}", filePath);
            return filePath;
        } catch (Exception e) {
            logger.error("保存文件到本地失败: {}", e.getMessage(), e);
            throw new IOException("保存文件失败", e);
        }
    }
    
    /**
     * 保存keytab文件到磁盘或小文件系统
     * 
     * @param fileContent 文件内容
     * @param originalFilename 原始文件名
     * @param connectionId 连接ID
     * @return 文件存储路径
     * @throws IOException IO异常
     */
    public static String saveKeytabFile(byte[] fileContent, String originalFilename, String connectionId) throws IOException {
        if (fileContent == null || fileContent.length == 0) {
            return null;
        }
        
        String fileName = generateFileName(originalFilename, connectionId);
        
        // 判断是否启用小文件系统
        if (isCtdfsEnabled()) {
            // 使用baseUrl作为存储位置
            String remoteFilePath = ctdfsConfig.getBaseUrl() + "/" + fileName;
            
            // 先保存为临时文件
            File tempFile = File.createTempFile("keytab_", ".tmp");
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(fileContent);
            }
            
            CtdfsService service = getCtdfsService();
            if (service != null) {
                try {
                    String filePath = service.uploadFile(remoteFilePath, fileName, tempFile.getAbsolutePath());
                    // 删除临时文件
                    tempFile.delete();
                    
                    if (filePath != null) {
                        logger.info("文件已上传到小文件系统: {}", filePath);
                        return filePath;
                    } else {
                        logger.warn("小文件系统返回的路径为空，降级到本地文件系统");
                    }
                } catch (Exception e) {
                    // 删除临时文件
                    tempFile.delete();
                    logger.warn("上传文件到小文件系统失败，降级到本地文件系统: {}", e.getMessage());
                }
            } else {
                // 删除临时文件
                tempFile.delete();
                // 降级到本地文件系统
                logger.warn("小文件系统服务不可用，降级到本地文件系统");
            }
        }
        
        // 使用本地文件系统
        String filePath = DEFAULT_STORAGE_PATH + File.separator + KEYTAB_SUBDIR + File.separator + fileName;
        
        try {
            saveFile(fileContent, filePath);
            logger.info("文件已保存到本地: {}", filePath);
            return filePath;
        } catch (Exception e) {
            logger.error("保存文件到本地失败: {}", e.getMessage(), e);
            throw new IOException("保存文件失败", e);
        }
    }
    
    /**
     * 保存krb5.conf文件到磁盘或小文件系统
     * 
     * @param krb5ConfFile krb5.conf文件
     * @param connectionId 连接ID
     * @return 文件存储路径
     * @throws IOException IO异常
     */
    public static String saveKrb5ConfFile(MultipartFile krb5ConfFile, String connectionId) throws IOException {
        if (krb5ConfFile == null || krb5ConfFile.isEmpty()) {
            return null;
        }
        
        String fileName = generateFileName(krb5ConfFile.getOriginalFilename(), connectionId);
        
        // 判断是否启用小文件系统
        if (isCtdfsEnabled()) {
            // 使用baseUrl作为存储位置
            String remoteFilePath = ctdfsConfig.getBaseUrl() + "/" + fileName;
            
            CtdfsService service = getCtdfsService();
            if (service != null) {
                try {
                    String filePath = service.uploadFile(remoteFilePath, fileName, krb5ConfFile.getInputStream());
                    if (filePath != null) {
                        logger.info("文件已上传到小文件系统: {}", filePath);
                        return filePath;
                    } else {
                        logger.warn("小文件系统返回的路径为空，降级到本地文件系统");
                    }
                } catch (Exception e) {
                    logger.warn("上传文件到小文件系统失败，降级到本地文件系统: {}", e.getMessage());
                }
            } else {
                // 降级到本地文件系统
                logger.warn("小文件系统服务不可用，降级到本地文件系统");
            }
        }
        
        // 使用本地文件系统
        String filePath = DEFAULT_STORAGE_PATH + File.separator + KRB5_CONF_SUBDIR + File.separator + fileName;
        
        try {
            saveFile(krb5ConfFile.getBytes(), filePath);
            logger.info("文件已保存到本地: {}", filePath);
            return filePath;
        } catch (Exception e) {
            logger.error("保存文件到本地失败: {}", e.getMessage(), e);
            throw new IOException("保存文件失败", e);
        }
    }
    
    /**
     * 保存krb5.conf文件到磁盘或小文件系统
     * 
     * @param fileContent 文件内容
     * @param originalFilename 原始文件名
     * @param connectionId 连接ID
     * @return 文件存储路径
     * @throws IOException IO异常
     */
    public static String saveKrb5ConfFile(byte[] fileContent, String originalFilename, String connectionId) throws IOException {
        if (fileContent == null || fileContent.length == 0) {
            return null;
        }
        
        String fileName = generateFileName(originalFilename, connectionId);
        
        // 判断是否启用小文件系统
        if (isCtdfsEnabled()) {
            // 使用baseUrl作为存储位置
            String remoteFilePath = ctdfsConfig.getBaseUrl() + "/" + fileName;
            
            // 先保存为临时文件
            File tempFile = File.createTempFile("krb5conf_", ".tmp");
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(fileContent);
            }
            
            CtdfsService service = getCtdfsService();
            if (service != null) {
                try {
                    String filePath = service.uploadFile(remoteFilePath, fileName, tempFile.getAbsolutePath());
                    // 删除临时文件
                    tempFile.delete();
                    
                    if (filePath != null) {
                        logger.info("文件已上传到小文件系统: {}", filePath);
                        return filePath;
                    } else {
                        logger.warn("小文件系统返回的路径为空，降级到本地文件系统");
                    }
                } catch (Exception e) {
                    // 删除临时文件
                    tempFile.delete();
                    logger.warn("上传文件到小文件系统失败，降级到本地文件系统: {}", e.getMessage());
                }
            } else {
                // 删除临时文件
                tempFile.delete();
                // 降级到本地文件系统
                logger.warn("小文件系统服务不可用，降级到本地文件系统");
            }
        }
        
        // 使用本地文件系统
        String filePath = DEFAULT_STORAGE_PATH + File.separator + KRB5_CONF_SUBDIR + File.separator + fileName;
        
        try {
            saveFile(fileContent, filePath);
            logger.info("文件已保存到本地: {}", filePath);
            return filePath;
        } catch (Exception e) {
            logger.error("保存文件到本地失败: {}", e.getMessage(), e);
            throw new IOException("保存文件失败", e);
        }
    }
    
    /**
     * 创建临时文件
     * 
     * @param prefix 文件前缀
     * @param content 文件内容
     * @return 临时文件路径
     * @throws IOException IO异常
     */
    public static String createTempFile(String prefix, byte[] content) throws IOException {
        return createTempFile(prefix, content, UUID.randomUUID().toString());
    }
    
    /**
     * 创建临时文件
     * 
     * @param prefix 文件前缀
     * @param content 文件内容
     * @param identifier 标识符
     * @return 临时文件路径
     * @throws IOException IO异常
     */
    public static String createTempFile(String prefix, byte[] content, String identifier) throws IOException {
        // 创建临时文件目录
        Path tempDir = Paths.get(DEFAULT_STORAGE_PATH, TEMP_DIR);
        Files.createDirectories(tempDir);
        
        // 创建临时文件
        String fileName = prefix + "-" + identifier;
        Path tempFile = tempDir.resolve(fileName);
        Files.write(tempFile, content);
        
        return tempFile.toAbsolutePath().toString();
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public static boolean deleteFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否是小文件系统路径
        if (isCtdfsPath(filePath)) {
            CtdfsService service = getCtdfsService();
            if (service != null) {
                service.deleteFile(filePath);
                logger.info("已从小文件系统删除文件: {}", filePath);
                return true;
            } else {
                logger.error("小文件系统服务未初始化，无法删除文件: {}", filePath);
                return false;
            }
        } else {
            // 本地文件系统
            try {
                Path path = Paths.get(filePath);
                return Files.deleteIfExists(path);
            } catch (IOException e) {
                logger.error("删除文件失败: {}", filePath, e);
                return false;
            }
        }
    }
    
    /**
     * 保存文件到磁盘
     * 
     * @param fileContent 文件内容
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    private static void saveFile(byte[] fileContent, String filePath) throws IOException {
        // 创建父目录
        Path path = Paths.get(filePath);
        Files.createDirectories(path.getParent());
        
        // 写入文件内容
        Files.write(path, fileContent);
    }
    
    /**
     * 创建目录（如果不存在）
     * 
     * @param directoryPath 目录路径
     * @throws IOException IO异常
     */
    private static void createDirectoryIfNotExists(String directoryPath) throws IOException {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
        }
    }
    
    /**
     * 生成文件名
     * 
     * @param originalFilename 原始文件名
     * @param connectionId 连接ID
     * @return 生成的文件名
     */
    private static String generateFileName(String originalFilename, String connectionId) {
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf('.'));
        }
        
        return UUID.randomUUID().toString() + "_" + connectionId + extension;
    }

    /**
     * 检查是否启用小文件系统
     * 
     * @return 是否启用小文件系统
     */
    private static boolean isCtdfsEnabled() {
        return ctdfsConfig != null && ctdfsService != null && ctdfsConfig.isEnabled();
    }
    
    /**
     * 安全获取CtdfsService
     * 如果服务未初始化，则记录日志并返回null
     * 
     * @return CtdfsService或null
     */
    private static CtdfsService getCtdfsService() {
        if (ctdfsService == null) {
            logger.warn("小文件系统服务未初始化，将使用本地文件系统");
            return null;
        }
        return ctdfsService;
    }
    
    /**
     * 检查路径是否为小文件系统路径
     * 
     * @param path 路径
     * @return 是否为小文件系统路径
     */
    private static boolean isCtdfsPath(String path) {
        return path != null && path.contains(CTDFS_ROOT_PATH);
    }
} 