package com.ffcs.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ffcs.oss.domain.AiTodoListD;
import com.ffcs.oss.web.rest.evt.audit.AuditEvt;

/**
 * 待办列表Service接口
 */
public interface AiTodoListService extends IService<AiTodoListD> {
    
    /**
     * 完成审核，将待办移动到审核列表
     * @param todoListId 待办ID
     * @return 是否成功
     */
    boolean completeAuditFromTodo(Long todoListId);
    
    /**
     * 完成待办审核
     * @param evt 审核事件
     * @return 是否成功
     */
    boolean completeTodoAudit(AuditEvt evt);
}