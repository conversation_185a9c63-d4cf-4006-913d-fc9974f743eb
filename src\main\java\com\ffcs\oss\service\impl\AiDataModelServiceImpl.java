package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.domain.AiAssistantD;
import com.ffcs.oss.domain.AiDataModelD;
import com.ffcs.oss.mapper.AiAssistantMapper;
import com.ffcs.oss.mapper.AiDataModelMapper;
import com.ffcs.oss.service.AiDataModelService;
import com.ffcs.oss.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据模型Service实现类
 */
@Service
public class AiDataModelServiceImpl extends ServiceImpl<AiDataModelMapper, AiDataModelD> implements AiDataModelService {
    
    @Autowired
    private AiAssistantMapper aiAssistantMapper;
    
    @Autowired
    private UserService userService;
    
    /**
     * 检查数据模型是否被助理引用
     *
     * @param dataModelId 数据模型ID
     * @return 如果被引用返回true，否则返回false
     */
    @Override
    public boolean checkModelReferenceByAssistant(String dataModelId) {
        LambdaQueryWrapper<AiAssistantD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiAssistantD::getDataModelId, dataModelId);
        
        // 查询引用该数据模型的助理数量
        return aiAssistantMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 检查数据模型名称是否已存在
     *
     * @param modelName 模型名称
     * @param excludeModelId 排除的模型ID（用于更新时排除自身）
     * @return 如果名称已存在返回true，否则返回false
     */
    @Override
    public boolean checkModelNameExists(String modelName, String excludeModelId) {
        LambdaQueryWrapper<AiDataModelD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiDataModelD::getModelName, modelName);
        
        // 只检查当前用户创建的数据模型名称是否重复
        String currentUserName = userService.getCurrentUserName();
        queryWrapper.eq(AiDataModelD::getCreateUserName, currentUserName);
        
        // 如果是更新操作，需要排除自身
        if (excludeModelId != null) {
            queryWrapper.ne(AiDataModelD::getDataModelId, excludeModelId);
        }
        
        return baseMapper.selectCount(queryWrapper) > 0;
    }
} 