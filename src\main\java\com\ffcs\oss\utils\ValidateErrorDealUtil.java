package com.ffcs.oss.utils;

import com.ffcs.oss.param.out.ServiceResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindingResult;

/**
 * <AUTHOR>
 * @description
 * @create 2023/7/21 10:51
 */
public class ValidateErrorDealUtil {
    private Logger logger = LoggerFactory.getLogger(ValidateErrorDealUtil.class);

    public static ServiceResp paramError(BindingResult result){
        StringBuffer message = new StringBuffer();
        if (result.hasErrors()) {
            message.append(result.getFieldError().getDefaultMessage()) ;
        }
        ServiceResp serviceResp=ServiceResp.getInstance();
        serviceResp.error(StringUtils.isBlank(message.toString()) ? "入参有误" : message.toString());
        return serviceResp;
    }
}
