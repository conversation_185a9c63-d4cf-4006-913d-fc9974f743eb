package com.ffcs.oss.utils.db;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 支持ZooKeeper HA模式的Hive执行器
 */
public class ZookeeperHiveExecutor extends AbstractSqlExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ZookeeperHiveExecutor.class);
    
    private final String jdbcUrl;
    private final String principal;
    private final boolean kerberosEnabled;
    private final byte[] keytabFileContent;
    private final byte[] krb5ConfFileContent;
    
    // 临时文件路径
    private String tempKeytabPath;
    private String tempKrb5ConfPath;
    
    /**
     * 构造函数
     * 
     * @param jdbcUrl 完整的JDBC URL
     * @param principal Kerberos principal
     * @param kerberosEnabled 是否启用Kerberos认证
     * @param keytabFileContent keytab文件内容
     * @param krb5ConfFileContent krb5.conf文件内容
     */
    public ZookeeperHiveExecutor(String jdbcUrl, String principal, boolean kerberosEnabled,
                                byte[] keytabFileContent, byte[] krb5ConfFileContent) {
        this.jdbcUrl = jdbcUrl;
        this.principal = principal;
        this.kerberosEnabled = kerberosEnabled;
        this.keytabFileContent = keytabFileContent;
        this.krb5ConfFileContent = krb5ConfFileContent;
        
        logger.debug("创建ZooKeeper Hive执行器，URL: {}", jdbcUrl);
    }
    
    @Override
    protected Connection createConnection() throws SQLException {
        try {
            // 加载Hive驱动
            Class.forName("org.apache.hive.jdbc.HiveDriver");
            
            // 设置Kerberos认证
            if (kerberosEnabled) {
                try {
                    setupKerberosAuthentication();
                } catch (IOException e) {
                    throw new SQLException("Kerberos认证设置失败: " + e.getMessage(), e);
                }
            }
            
            // 设置系统属性，避免ZooKeeper依赖问题
            System.setProperty("zookeeper.sasl.client", "false");
            
            // 设置额外的Kerberos相关系统属性
            System.setProperty("javax.security.auth.useSubjectCredsOnly", "false");
            
            // 确保关闭调试日志，避免输出敏感信息
            System.setProperty("sun.security.krb5.debug", "false");
            
            // 添加自动处理域名解析的配置
            if (kerberosEnabled && principal != null && principal.contains("@")) {
                String realm = principal.substring(principal.lastIndexOf('@') + 1);
                String hostname = null;
                
                // 从principal中提取服务器主机名
                if (principal.contains("/") && principal.indexOf("/") < principal.indexOf("@")) {
                    hostname = principal.substring(principal.indexOf("/") + 1, principal.indexOf("@"));
                    logger.debug("从principal提取主机名: {}", hostname);
                }
                
                // 尝试手动创建域名映射
                try {
                    // 如果提供了hostname，尝试解析其IP地址
                    if (hostname != null && !hostname.isEmpty()) {
                        handleDnsResolution(hostname, realm);
                    }
                } catch (Exception e) {
                    logger.error("处理域名映射时出错: {}", e);
                    // 继续执行，不中断连接流程
                }
            }
            
            // 设置参数解决ZooKeeper版本冲突问题
            Properties connectionProps = new Properties();
            
            // 添加额外参数以处理ZooKeeper HA
            if (kerberosEnabled) {
                connectionProps.setProperty("hadoop.security.authentication", "kerberos");
                connectionProps.setProperty("hive.server2.authentication", "KERBEROS");
                connectionProps.setProperty("hive.server2.authentication.kerberos.principal", principal);
                
                // 设置Sasl QOP属性
                connectionProps.setProperty("sasl.qop", "auth");
                connectionProps.setProperty("sasl.server.qop", "auth");
                
                // 解决GSS认证问题
                connectionProps.setProperty("sasl.client.callback.handler.class", 
                        "org.apache.hive.service.auth.PlainSaslHelper$PlainCallbackHandler");
                
                // 如果URL中没有包含principal参数，则添加
                if (!jdbcUrl.contains("principal=")) {
                    // 从jdbcUrl中获取基本URL部分（不含参数）
                    String baseUrl = jdbcUrl;
                    String params = "";
                    
                    if (jdbcUrl.contains(";")) {
                        baseUrl = jdbcUrl.substring(0, jdbcUrl.indexOf(";"));
                        params = jdbcUrl.substring(jdbcUrl.indexOf(";"));
                    }
                    
                    // 构建新的URL，添加principal参数
                    return DriverManager.getConnection(
                            baseUrl + ";principal=" + principal + ";auth=kerberos" + params, 
                            connectionProps);
                }
            }
            
            // 添加参数以绕过ZooKeeper版本兼容性问题
            connectionProps.setProperty("hive.server2.zookeeper.use.builtin", "true");
            connectionProps.setProperty("zookeeper.recovery.retry", "3");
            connectionProps.setProperty("zookeeper.recovery.retry.intervalmill", "2000");
            connectionProps.setProperty("hive.zookeeper.client.port", "2181");
            connectionProps.setProperty("hive.zookeeper.property.clientPort", "2181");
            
            // Kerberos SASL配置
            connectionProps.setProperty("sasl.qop", "auth");
            
            // 尝试建立连接
            logger.debug("正在尝试建立Hive连接...");
            
            // 修改ZooKeeper HA模式下的URL处理
            String finalUrl = jdbcUrl;
            
            // 尝试解析并修改URL
            if (jdbcUrl.contains("serviceDiscoveryMode=zooKeeper")) {
                try {
                    // 解析URL中的ZooKeeper服务器
                    String zookeeperServers = "";
                    if (jdbcUrl.contains("//") && jdbcUrl.indexOf("/", jdbcUrl.indexOf("//") + 2) > 0) {
                        zookeeperServers = jdbcUrl.substring(
                                jdbcUrl.indexOf("//") + 2, 
                                jdbcUrl.indexOf("/", jdbcUrl.indexOf("//") + 2));
                    }
                    
                    // 确保URL包含必要的参数
                    if (!jdbcUrl.contains("zooKeeperNamespace=")) {
                        finalUrl = finalUrl + ";zooKeeperNamespace=hiveserver2";
                    }
                    
                    // 添加额外的Kerberos认证参数
                    if (kerberosEnabled && !finalUrl.contains("auth=")) {
                        finalUrl = finalUrl + ";auth=kerberos";
                    }
                    
                    // 确保principal参数正确格式化
                    if (kerberosEnabled && principal != null && !principal.isEmpty()) {
                        // 检查URL中是否已包含principal参数
                        if (!finalUrl.contains("principal=")) {
                            finalUrl = finalUrl + ";principal=" + principal;
                        } else {
                            // 如果URL中的principal参数格式不正确，替换它
                            String[] parts = finalUrl.split(";");
                            StringBuilder newUrl = new StringBuilder();
                            for (String part : parts) {
                                if (part.startsWith("principal=") && !part.equals("principal=" + principal)) {
                                    newUrl.append("principal=").append(principal);
                                } else {
                                    newUrl.append(part);
                                }
                                newUrl.append(";");
                            }
                            // 移除最后一个分号
                            if (newUrl.charAt(newUrl.length() - 1) == ';') {
                                newUrl.deleteCharAt(newUrl.length() - 1);
                            }
                            finalUrl = newUrl.toString();
                        }
                    }
                    
                    logger.debug("修正后的JDBC URL: {}", finalUrl);
                    
                } catch (Exception e) {
                    logger.warn("解析和修正URL时出错: {}", e.getMessage());
                }
            }
            
            // 添加重试机制
            int maxRetries = 3;
            SQLException lastException = null;
            
            for (int i = 0; i < maxRetries; i++) {
                try {
                    logger.info("第{}次尝试连接Hive (URL: {})", i+1, finalUrl);
                    
                    // 尝试建立连接
                    Connection conn = DriverManager.getConnection(finalUrl, connectionProps);
                    logger.info("成功连接Hive服务器");
                    return conn;
                } catch (SQLException e) {
                    lastException = e;
                    logger.warn("连接尝试 #{} 失败: {}", i+1, e.getMessage());
                    
                    // 如果是GSS认证失败，尝试提供更具体的错误信息
                    if (e.getMessage() != null && e.getMessage().contains("GSS initiate failed")) {
                        logger.error("GSS认证失败，可能的原因: " +
                                "1) principal格式不正确（应为user/host@REALM） " +
                                "2) keytab文件与principal不匹配 " +
                                "3) krb5.conf配置不正确 " +
                                "4) 时间同步问题（客户端与服务器时间差不能超过5分钟）");
                    }
                    
                    // 重试前等待一段时间
                    if (i < maxRetries - 1) {
                        try {
                            Thread.sleep(2000 * (i + 1));
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            }
            
            // 如果所有重试都失败
            if (lastException != null) {
                throw lastException;
            }
            
            // 这一行理论上不会执行，因为上面的循环要么返回连接，要么抛出异常
            return DriverManager.getConnection(finalUrl, connectionProps);
        } catch (ClassNotFoundException e) {
            throw new SQLException("Hive JDBC驱动未找到", e);
        } catch (SQLException e) {
            logger.error("连接Hive服务器失败: ", e);
            throw new SQLException("连接Hive服务器失败: " + e.getMessage() + 
                "\n如果是ZooKeeper HA模式下的GSS认证失败，请检查principal、keytab和krb5.conf是否正确配置", e);
        }
    }
    
    /**
     * 设置Kerberos认证
     */
    private void setupKerberosAuthentication() throws IOException {
        // 如果提供了文件内容，则创建临时文件
        if (keytabFileContent != null && keytabFileContent.length > 0) {
            tempKeytabPath = createTempFile("keytab", keytabFileContent);
            logger.debug("已创建临时keytab文件: {}", tempKeytabPath);
        }
        
        if (krb5ConfFileContent != null && krb5ConfFileContent.length > 0) {
            tempKrb5ConfPath = createTempFile("krb5.conf", krb5ConfFileContent);
            System.setProperty("java.security.krb5.conf", tempKrb5ConfPath);
            logger.debug("已设置krb5.conf路径: {}, 文件大小: {}", tempKrb5ConfPath, krb5ConfFileContent.length);
            
            // 验证krb5.conf文件内容
            try {
                String confContent = new String(Files.readAllBytes(Paths.get(tempKrb5ConfPath)), "UTF-8");
                logger.debug("krb5.conf内容前100字符: {}", 
                        confContent.length() > 100 ? confContent.substring(0, 100) + "..." : confContent);
                
                // 检查必要的配置段
                boolean hasLibDefaults = confContent.contains("[libdefaults]");
                boolean hasRealms = confContent.contains("[realms]");
                boolean hasDefaultRealm = confContent.contains("default_realm =");
                
                // 如果krb5.conf中没有设置default_realm，但是从principal可以获取realm信息，则自动添加
                if (!hasDefaultRealm && principal != null && principal.contains("@")) {
                    String realmFromPrincipal = principal.substring(principal.lastIndexOf('@') + 1);
                    logger.info("krb5.conf中未设置default_realm，将自动配置为: {}", realmFromPrincipal);
                    
                    // 在[libdefaults]部分添加default_realm设置
                    String updatedContent;
                    if (hasLibDefaults) {
                        updatedContent = confContent.replace("[libdefaults]", 
                            "[libdefaults]\n  default_realm = " + realmFromPrincipal);
                    } else {
                        // 如果没有[libdefaults]部分，则添加整个部分
                        updatedContent = "[libdefaults]\n  default_realm = " + realmFromPrincipal + "\n\n" + confContent;
                    }
                    
                    // 创建新的配置文件
                    tempKrb5ConfPath = createTempFile("krb5.conf", updatedContent.getBytes());
                    System.setProperty("java.security.krb5.conf", tempKrb5ConfPath);
                    logger.info("已更新krb5.conf文件，添加default_realm设置: {}", tempKrb5ConfPath);
                    
                    // 更新配置内容以供后续检查
                    confContent = updatedContent;
                    hasDefaultRealm = true;
                }
                
                if (!hasLibDefaults || !hasRealms) {
                    StringBuilder missingParts = new StringBuilder("krb5.conf文件格式不正确，缺少必要的配置段: ");
                    if (!hasLibDefaults) missingParts.append("[libdefaults] ");
                    if (!hasRealms) missingParts.append("[realms] ");
                    
                    logger.error(missingParts.toString());
                    logger.error("请确保krb5.conf文件包含完整的Kerberos配置");
                    
                    // 提供一个示例配置供参考
                    logger.info("参考的krb5.conf文件格式：\n" +
                            "[libdefaults]\n" +
                            "  default_realm = EXAMPLE.COM\n" +
                            "  dns_lookup_realm = false\n" +
                            "  dns_lookup_kdc = false\n" +
                            "\n" +
                            "[realms]\n" +
                            "  EXAMPLE.COM = {\n" +
                            "    kdc = kdc.example.com\n" +
                            "    admin_server = kdc.example.com\n" +
                            "  }");
                    
                    // 记录文件内容以帮助调试
                    logger.debug("上传的krb5.conf文件内容:\n{}", confContent);
                    
                    // 尝试自动修复配置文件
                    if (principal != null && principal.contains("@")) {
                        String realmFromPrincipal = principal.substring(principal.lastIndexOf('@') + 1);
                        String kdcHost = realmFromPrincipal.split("\\.")[0];
                        
                        logger.info("尝试自动生成krb5.conf文件...");
                        String autoGenConfig = generateKrb5Conf(realmFromPrincipal, kdcHost);
                        
                        // 创建新的配置文件
                        tempKrb5ConfPath = createTempFile("krb5.conf", autoGenConfig.getBytes());
                        System.setProperty("java.security.krb5.conf", tempKrb5ConfPath);
                        logger.info("已生成新的krb5.conf文件: {}", tempKrb5ConfPath);
                    }
                }
                
                // 添加解析并检查realm配置
                if (principal != null && principal.contains("@")) {
                    String realmFromPrincipal = principal.substring(principal.lastIndexOf('@') + 1);
                    boolean realmInConfig = confContent.contains(realmFromPrincipal);
                    
                    if (!realmInConfig) {
                        logger.error("krb5.conf文件中缺少针对realm [{}] 的配置", realmFromPrincipal);
                        
                        // 生成一个建议的配置
                        StringBuilder suggestedConfig = new StringBuilder();
                        suggestedConfig.append("请在krb5.conf的[realms]部分添加以下配置：\n");
                        suggestedConfig.append("  ").append(realmFromPrincipal).append(" = {\n");
                        suggestedConfig.append("    kdc = ").append(realmFromPrincipal.split("\\.")[0]).append("\n");
                        suggestedConfig.append("    admin_server = ").append(realmFromPrincipal.split("\\.")[0]).append("\n");
                        suggestedConfig.append("  }");
                        
                        logger.info(suggestedConfig.toString());
                    } else {
                        logger.debug("在krb5.conf中找到realm [{}] 的配置", realmFromPrincipal);
                        
                        // 尝试解析KDC地址
                        int realmPos = confContent.indexOf(realmFromPrincipal + " = {");
                        if (realmPos < 0) realmPos = confContent.indexOf(realmFromPrincipal + "={");
                        
                        if (realmPos > 0) {
                            int blockEnd = confContent.indexOf("}", realmPos);
                            if (blockEnd > realmPos) {
                                String realmBlock = confContent.substring(realmPos, blockEnd);
                                logger.debug("Realm配置块: {}", realmBlock);
                                
                                // 提取KDC地址
                                if (realmBlock.contains("kdc =")) {
                                    String kdcLine = realmBlock.substring(realmBlock.indexOf("kdc ="));
                                    int endOfLine = kdcLine.indexOf('\n');
                                    if (endOfLine > 0) {
                                        String kdc = kdcLine.substring(5, endOfLine).trim();
                                        logger.debug("从krb5.conf提取的KDC地址: {}", kdc);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.warn("无法读取krb5.conf内容: {}", e.getMessage());
            }
        } else {
            // 使用默认的krb5.conf路径
            System.setProperty("java.security.krb5.conf", "/etc/krb5.conf");
            logger.debug("使用系统默认krb5.conf路径: /etc/krb5.conf");
        }
        
        // 验证Kerberos参数
        if (principal == null || principal.isEmpty() || tempKeytabPath == null || tempKeytabPath.isEmpty()) {
            throw new IllegalArgumentException("Kerberos认证参数不完整: principal或keytab缺失");
        }
        
        // 从principal中提取realm和KDC信息
        String realm = "";
        String kdcHost = "";
        
        if (principal.contains("@")) {
            realm = principal.substring(principal.lastIndexOf('@') + 1);
            logger.debug("从principal提取realm: {}", realm);
            
            if (realm.contains(".")) {
                kdcHost = realm.split("\\.")[0];
                logger.debug("从realm提取KDC host: {}", kdcHost);
            }
        }
        
        // 设置Kerberos相关的系统属性
        // 清除这些属性，让系统从krb5.conf文件中读取
        System.clearProperty("java.security.krb5.realm");
        System.clearProperty("java.security.krb5.kdc");
        
        // 设置其他重要的系统属性
        System.setProperty("javax.security.auth.useSubjectCredsOnly", "false");
        System.clearProperty("java.security.krb5.conf.backup"); // 清除可能的备份配置
        
        // 添加DNS相关系统属性，改善域名解析
        System.setProperty("sun.net.spi.nameservice.provider.1", "dns,sun");
        System.setProperty("networkaddress.cache.ttl", "0");
        System.setProperty("networkaddress.cache.negative.ttl", "0");
        
        // 添加JAAS配置，增强Kerberos认证
        String jaasConf = "com.sun.security.auth.module.Krb5LoginModule required " +
                "useKeyTab=true " +
                "keyTab=\"" + tempKeytabPath + "\" " +
                "principal=\"" + principal + "\" " +
                "useTicketCache=false " +
                "storeKey=true " +
                "doNotPrompt=true;";
        
        // 设置JAAS配置
        try {
            // 创建临时JAAS配置文件
            String jaasPath = createTempFile("jaas.conf", jaasConf.getBytes());
            System.setProperty("java.security.auth.login.config", jaasPath);
            logger.debug("已设置JAAS配置: {}", jaasPath);
            
            // 设置GSS相关参数
            System.setProperty("javax.security.sasl.qop", "auth");
            System.setProperty("javax.security.sasl.server.qop", "auth");
            System.setProperty("javax.security.sasl.policy.forward", "true");
            
            // 添加Kerberos调试信息（仅在调试环境使用）
            System.setProperty("sun.security.krb5.debug", "true");
            System.setProperty("java.security.debug", "gssloginconfig,configfile,configparser,logincontext");
            
        } catch (Exception e) {
            logger.warn("设置JAAS配置时出错: {}", e.getMessage());
            // 继续执行，不中断流程
        }
        
        // 尝试预先解析realm域名，以防止在连接时出现解析问题
        if (principal != null && principal.contains("@")) {
            String realmName = principal.substring(principal.lastIndexOf('@') + 1);
            logger.debug("预解析realm域名: {}", realmName);
            
            // 添加realm域名到DNS解析配置
            try {
                java.net.InetAddress.getByName(realmName);
                logger.debug("Realm域名 {} 解析成功", realmName);
            } catch (Exception e) {
                logger.warn("Realm域名 {} 解析失败: {}", realmName, e.getMessage());
                
                // 尝试添加域名到hosts文件的建议
                logger.info("建议: 如果认证失败，请考虑在hosts文件中添加 '服务器IP {}' 以解决域名解析问题", realmName);
            }
            
            // 从principal中提取服务器主机名
            String hostname = null;
            if (principal.contains("/") && principal.indexOf("/") < principal.indexOf("@")) {
                hostname = principal.substring(principal.indexOf("/") + 1, principal.indexOf("@"));
                logger.debug("从principal提取主机名: {}", hostname);
                
                // 使用handleDnsResolution处理域名解析
                if (hostname != null && !hostname.isEmpty()) {
                    handleDnsResolution(hostname, realmName);
                }
            }
            
            // 检查principal格式是否正确
            if (!principal.contains("/")) {
                logger.warn("Principal格式可能不正确。Kerberos服务主体应为: username/hostname@REALM");
                // 尝试自动修正
                String principalUser = principal.substring(0, principal.indexOf("@"));
                String correctedPrincipal = principalUser + "/" + hostname + "@" + realmName;
                logger.info("建议使用正确格式的principal: {}", correctedPrincipal);
            }
        }
        
        // 创建Hadoop配置
        Configuration conf = new Configuration();
        conf.set("hadoop.security.authentication", "kerberos");
        conf.set("hadoop.security.authorization", "true");
        
        // ZooKeeper相关配置
        conf.set("hive.server2.authentication", "kerberos");
        conf.set("hive.server2.authentication.kerberos.principal", principal);
        conf.set("hive.server2.authentication.kerberos.keytab", tempKeytabPath);
        
        // 设置配置
        UserGroupInformation.setConfiguration(conf);
        
        // 尝试清理之前的认证
        try {
            UserGroupInformation currentUser = UserGroupInformation.getCurrentUser();
            if (currentUser != null && currentUser.hasKerberosCredentials()) {
                logger.debug("尝试注销当前用户: {}", currentUser.getUserName());
                currentUser.logoutUserFromKeytab();
            }
        } catch (Exception e) {
            logger.debug("尝试注销之前的Kerberos凭据时出现异常: {}", e.getMessage());
        }
        
        try {
            // 先尝试注销之前的凭据
            try {
                UserGroupInformation currentUser = UserGroupInformation.getCurrentUser();
                if (currentUser != null && currentUser.hasKerberosCredentials()) {
                    logger.debug("尝试注销当前用户: {}", currentUser.getUserName());
                    currentUser.logoutUserFromKeytab();
                }
            } catch (Exception e) {
                logger.debug("尝试注销之前的Kerberos凭据时出现异常: {}", e.getMessage());
            }
            
            // 使用keytab认证
            logger.debug("尝试使用principal={}, keytab={}进行Kerberos认证", principal, tempKeytabPath);
            
            // 添加重试机制
            int maxRetries = 3;
            Exception lastException = null;
            
            for (int i = 0; i < maxRetries; i++) {
                try {
                    // 检查hosts文件和域名解析
                    try {
                        if (principal.contains("@")) {
                            String realmName = principal.substring(principal.lastIndexOf('@') + 1);
                            logger.debug("尝试解析realm域名: {}", realmName);
                            java.net.InetAddress.getByName(realmName);
                            logger.debug("域名{}解析成功", realmName);
                        }
                    } catch (java.net.UnknownHostException e) {
                        logger.warn("域名解析失败，这可能会导致Kerberos认证问题: {}", e.getMessage());
                        // 不抛出异常，继续尝试认证
                    }
                    
                    // 尝试登录
                    UserGroupInformation.loginUserFromKeytab(principal, tempKeytabPath);
                    
                    // 如果成功，跳出循环
                    logger.info("Kerberos认证成功，principal={}", principal);
                    
                    // 验证认证结果
                    UserGroupInformation currentUser = UserGroupInformation.getLoginUser();
                    logger.debug("当前登录用户: {}, 是否有Kerberos凭据: {}", 
                             currentUser.getUserName(), 
                             currentUser.hasKerberosCredentials());
                    
                    return; // 认证成功，直接返回
                } catch (Exception e) {
                    lastException = e;
                    logger.warn("Kerberos认证尝试 #{} 失败: {}", i+1, e.getMessage());
                    
                    // 如果是域名解析错误，提供更具体的提示
                    if (e.getMessage() != null && e.getMessage().contains("nodename nor servname provided")) {
                        logger.error("域名解析错误，请检查krb5.conf中的realm和kdc配置是否正确");
                    }
                    
                    // 重试前等待一段时间
                    if (i < maxRetries - 1) {
                        try {
                            Thread.sleep(1000 * (i + 1));
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            }
            
            // 如果所有重试都失败
            if (lastException != null) {
                logger.error("Kerberos认证失败，已重试{}次: {}", maxRetries, lastException.getMessage());
                throw new IOException("Kerberos认证失败: " + lastException.getMessage() + 
                    "。请检查以下可能的问题: 1) principal格式是否正确 2) keytab文件是否匹配principal " +
                    "3) krb5.conf配置是否包含正确的realm和kdc设置", lastException);
            }
        } catch (IOException e) {
            logger.error("Kerberos认证失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 创建临时文件并写入内容
     * 
     * @param prefix 文件前缀
     * @param content 文件内容
     * @return 临时文件路径
     * @throws IOException IO异常
     */
    private String createTempFile(String prefix, byte[] content) throws IOException {
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("hive-kerberos-");
        
        // 创建临时文件
        String fileName = prefix + "-" + UUID.randomUUID().toString();
        File tempFile = new File(tempDir.toFile(), fileName);
        
        // 写入文件内容
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(content);
        }
        
        // 设置文件权限
        tempFile.setReadable(true, true);
        tempFile.setWritable(true, true);
        tempFile.setExecutable(false, false);
        
        // 返回文件路径
        return tempFile.getAbsolutePath();
    }
    
    /**
     * 生成基本的krb5.conf配置文件
     * 
     * @param realm Kerberos realm
     * @param kdcHost KDC主机名
     * @return 配置文件内容
     */
    private String generateKrb5Conf(String realm, String kdcHost) {
        StringBuilder conf = new StringBuilder();
        
        conf.append("[libdefaults]\n");
        conf.append("  default_realm = ").append(realm).append("\n");
        conf.append("  dns_lookup_realm = false\n");
        conf.append("  dns_lookup_kdc = false\n");
        conf.append("  ticket_lifetime = 24h\n");
        conf.append("  renew_lifetime = 7d\n");
        conf.append("  forwardable = true\n");
        conf.append("\n");
        
        conf.append("[realms]\n");
        conf.append("  ").append(realm).append(" = {\n");
        conf.append("    kdc = ").append(kdcHost).append("\n");
        conf.append("    admin_server = ").append(kdcHost).append("\n");
        conf.append("  }\n");
        
        logger.debug("自动生成的krb5.conf内容:\n{}", conf.toString());
        return conf.toString();
    }
    
    /**
     * 处理DNS域名解析问题
     * 
     * @param hostname 主机名
     * @param realm Kerberos realm
     */
    private void handleDnsResolution(String hostname, String realm) {
        try {
            // 尝试解析主机名
            java.net.InetAddress addr = java.net.InetAddress.getByName(hostname);
            String ipAddress = addr.getHostAddress();
            logger.debug("主机名 {} 解析到IP: {}", hostname, ipAddress);
            
            // 如果解析成功，确保realm域名也能解析
            if (ipAddress != null && !ipAddress.isEmpty()) {
                try {
                    // 尝试使用反射获取nameCache字段
                    // 注意：此字段在不同Java版本中可能不存在
                    java.lang.reflect.Field field = java.net.InetAddress.class.getDeclaredField("nameCache");
                    field.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, java.net.InetAddress[]> cache = 
                        (java.util.Map<String, java.net.InetAddress[]>) field.get(null);
                    
                    // 为realm域名创建缓存
                    cache.put(realm.toLowerCase(),
                        new java.net.InetAddress[]{addr});
                    
                    logger.info("已创建域名映射: {} -> {}", realm, ipAddress);
                } catch (NoSuchFieldException e) {
                    // 处理nameCache字段不存在的情况
                    logger.info("当前Java版本不支持nameCache字段，将使用hosts文件或DNS解析域名");
                    
                    // 尝试通过添加系统属性的方式解决
                    System.setProperty("sun.net.spi.nameservice.provider.1", "dns,sun");
                    System.setProperty("networkaddress.cache.ttl", "0");
                    
                    // 尝试使用Java 9+中的InetAddress.CachePolicy
                    try {
                        // 获取InetAddress$CachePolicy类
                        Class<?> cachePolicyClass = Class.forName("java.net.InetAddress$CachePolicy");
                        // 获取get方法
                        java.lang.reflect.Method get = cachePolicyClass.getDeclaredMethod("get");
                        get.setAccessible(true);
                        Object cachePolicy = get.invoke(null);
                        
                        // 获取put方法
                        java.lang.reflect.Method put = cachePolicyClass.getDeclaredMethod(
                            "put", String.class, java.net.InetAddress[].class);
                        put.setAccessible(true);
                        
                        // 尝试添加映射
                        put.invoke(cachePolicy, realm.toLowerCase(), new java.net.InetAddress[]{addr});
                        logger.info("使用CachePolicy创建域名映射: {} -> {}", realm, ipAddress);
                    } catch (Exception ex) {
                        // 忽略异常，回退到DNS解析
                        logger.debug("无法使用CachePolicy，将依赖DNS解析: {}", ex.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("无法为realm创建域名映射: {}", e);
            // 即使域名映射失败，也允许继续执行
            // 添加系统属性，尝试改善DNS解析
            System.setProperty("sun.net.spi.nameservice.provider.1", "dns,sun");
            System.setProperty("networkaddress.cache.ttl", "0");
            
            // 输出建议，帮助用户解决潜在的域名解析问题
            if (realm != null && !realm.isEmpty()) {
                logger.info("建议: 如果遇到域名解析问题，请确保hosts文件中包含realm [{}] 的IP映射", realm);
            }
        }
    }
    
    @Override
    public void close() {
        super.close();
        
        // 清理临时文件
        cleanupTempFiles();
    }
    
    /**
     * 清理临时文件
     */
    private void cleanupTempFiles() {
        // 清理keytab临时文件
        if (tempKeytabPath != null) {
            try {
                Files.deleteIfExists(Paths.get(tempKeytabPath));
                logger.debug("已删除临时keytab文件: {}", tempKeytabPath);
            } catch (IOException e) {
                logger.warn("删除临时keytab文件失败: {}", e.getMessage());
            }
        }
        
        // 清理krb5.conf临时文件
        if (tempKrb5ConfPath != null) {
            try {
                Files.deleteIfExists(Paths.get(tempKrb5ConfPath));
                logger.debug("已删除临时krb5.conf文件: {}", tempKrb5ConfPath);
            } catch (IOException e) {
                logger.warn("删除临时krb5.conf文件失败: {}", e.getMessage());
            }
        }
    }
} 