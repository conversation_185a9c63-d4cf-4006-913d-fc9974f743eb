package com.ffcs.oss.web.rest.evt.audit;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审核请求参数
 */
@ApiModel("审核请求参数")
public class AuditEvt extends QueryPageEvt implements Serializable {
    
    @ApiModelProperty("审核ID")
    private Long auditListId;
    
    @ApiModelProperty("待办ID")
    private Long todoListId;
    
    @ApiModelProperty("审核状态 0待审核|1已审核")
    private String auditStatus;
    
    @ApiModelProperty("提问")
    private String question;
    
    @ApiModelProperty("大模型结果")
    private String bigModelResult;
    
    @ApiModelProperty("1点赞|2点踩|0无")
    private String likeOrNoLikeResult;
    
    @ApiModelProperty("反馈用户")
    private String feedBackUser;
    
    @ApiModelProperty("会话id")
    private String sessionId;
    
    @ApiModelProperty("创建人")
    private String createUserName;
    
    @ApiModelProperty("更新人")
    private String updateUserName;
    
    @ApiModelProperty("旧的正确sql")
    private String oldCorrectSql;
    
    @ApiModelProperty("新的正确sql")
    private String newCorrectSql;
    
    @ApiModelProperty("正确sql")
    private String correctSql;
    
    @ApiModelProperty("业务知识")
    private String professionalKnowledge;

    public Long getAuditListId() {
        return auditListId;
    }

    public void setAuditListId(Long auditListId) {
        this.auditListId = auditListId;
    }

    public Long getTodoListId() {
        return todoListId;
    }

    public void setTodoListId(Long todoListId) {
        this.todoListId = todoListId;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getBigModelResult() {
        return bigModelResult;
    }

    public void setBigModelResult(String bigModelResult) {
        this.bigModelResult = bigModelResult;
    }

    public String getLikeOrNoLikeResult() {
        return likeOrNoLikeResult;
    }

    public void setLikeOrNoLikeResult(String likeOrNoLikeResult) {
        this.likeOrNoLikeResult = likeOrNoLikeResult;
    }

    public String getFeedBackUser() {
        return feedBackUser;
    }

    public void setFeedBackUser(String feedBackUser) {
        this.feedBackUser = feedBackUser;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public String getOldCorrectSql() {
        return oldCorrectSql;
    }

    public void setOldCorrectSql(String oldCorrectSql) {
        this.oldCorrectSql = oldCorrectSql;
    }

    public String getNewCorrectSql() {
        return newCorrectSql;
    }

    public void setNewCorrectSql(String newCorrectSql) {
        this.newCorrectSql = newCorrectSql;
    }

    public String getCorrectSql() {
        return correctSql;
    }

    public void setCorrectSql(String correctSql) {
        this.correctSql = correctSql;
    }

    public String getProfessionalKnowledge() {
        return professionalKnowledge;
    }

    public void setProfessionalKnowledge(String professionalKnowledge) {
        this.professionalKnowledge = professionalKnowledge;
    }
} 