package com.ffcs.oss.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName MyConfigProperties
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/8/8 15:48
 */
@Configuration
@ConfigurationProperties(prefix = "myconfig", ignoreUnknownFields = false)
public class MyConfigProperties {
    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
