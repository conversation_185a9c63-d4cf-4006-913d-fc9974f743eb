package com.ffcs.oss.mapper.datasourceconfig;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ffcs.oss.domain.AiDataSourceConfigD;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.web.rest.evt.datasourceconfig.DataSourceConfigEvt;
import com.ffcs.oss.web.rest.vm.datasourceconfig.DataSourceConfigVm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2023/8/7 18:14
 */

public interface DataSourceConfigMapper extends BaseMapper<AiDataSourceConfigD> {
    /**
     * 获取数源配置列表
     *
     * @param evt
     * @return
     */
    List<DataSourceConfigVm> getDataSourceConfigList(@Param("evt") DataSourceConfigEvt evt);

    /**
     * 数源名称是否重复
     *
     * @param evt
     * @return
     */
    Integer nameWhetherRepeat(@Param("evt") DataSourceConfigEvt evt);
}
