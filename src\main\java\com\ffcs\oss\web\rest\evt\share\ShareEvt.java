package com.ffcs.oss.web.rest.evt.share;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 分享请求参数
 */
@ApiModel("分享请求参数")
public class ShareEvt extends QueryPageEvt implements Serializable {
    
    @ApiModelProperty("分享id")
    private String shareId;
    
    @ApiModelProperty("分享链接")
    private String shareLink;
    
    @ApiModelProperty("问题id")
    private String questionId;
    
    @ApiModelProperty("回答id")
    private String answerId;
    
    @ApiModelProperty("创建人")
    private String createUserName;

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }

    public String getShareLink() {
        return shareLink;
    }

    public void setShareLink(String shareLink) {
        this.shareLink = shareLink;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getAnswerId() {
        return answerId;
    }

    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
} 