package com.ffcs.oss.utils;

import org.jasypt.encryption.StringEncryptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Jasypt ENC格式加解密工具类
 * 专门处理带ENC()格式的加密数据
 */
@Component
public class JasyptEncryptorUtils {

    private static final Logger logger = LoggerFactory.getLogger(JasyptEncryptorUtils.class);
    
    // 前缀标识
    private static final String ENC_PREFIX = "ENC(";
    private static final String ENC_SUFFIX = ")";
    
    private static StringEncryptor encryptor;
    
    @Autowired
    public JasyptEncryptorUtils(StringEncryptor jasyptStringEncryptor) {
        JasyptEncryptorUtils.encryptor = jasyptStringEncryptor;
    }
    
    /**
     * 加密后的值添加ENC()前缀
     *
     * @param value 要加密的值
     * @return 加密后的值，格式为ENC(加密内容)
     */
    public static String encrypt(String value) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }
        
        // 如果已经是ENC格式，直接返回
        if (isEncrypted(value)) {
            return value;
        }
        
        try {
            if (encryptor == null) {
                logger.error("StringEncryptor未注入");
                // 回退到原始加密方式
                return PasswordEncryptionUtil.encrypt(value);
            }
            
            String encrypted = encryptor.encrypt(value);
            return ENC_PREFIX + encrypted + ENC_SUFFIX;
        } catch (Exception e) {
            logger.error("加密失败", e);
            // 加密失败时使用原始加密方法
            return PasswordEncryptionUtil.encrypt(value);
        }
    }
    
    /**
     * 解密ENC()格式的值
     *
     * @param value 要解密的值，格式为ENC(加密内容)
     * @return 解密后的值
     */
    public static String decrypt(String value) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }
        
        // 如果不是ENC格式，先尝试原始解密
        if (!isEncrypted(value)) {
            try {
                return PasswordEncryptionUtil.decrypt(value);
            } catch (Exception e) {
                logger.error("原始解密失败", e);
                return value;
            }
        }
        
        // 提取ENC()中的内容
        String encryptedValue = value.substring(ENC_PREFIX.length(), value.length() - ENC_SUFFIX.length());
        
        try {
            if (encryptor == null) {
                logger.error("StringEncryptor未注入");
                // 回退到原始解密方式
                return PasswordEncryptionUtil.decrypt(encryptedValue);
            }
            
            return encryptor.decrypt(encryptedValue);
        } catch (Exception e) {
            logger.warn("Jasypt解密失败，尝试原始解密方式", e);
            try {
                // 尝试使用原始解密方法
                return PasswordEncryptionUtil.decrypt(encryptedValue);
            } catch (Exception ex) {
                logger.error("所有解密方式均失败", ex);
                // 如果解密失败，返回原始加密内容（不含ENC前缀）
                return encryptedValue;
            }
        }
    }
    
    /**
     * 判断字符串是否为ENC()格式
     *
     * @param value 要检查的字符串
     * @return 是否为ENC()格式
     */
    public static boolean isEncrypted(String value) {
        return value != null && value.startsWith(ENC_PREFIX) && value.endsWith(ENC_SUFFIX);
    }
    
    /**
     * 用于测试连接的临时方法
     * 直接返回原值，不进行解密（用于测试连接时的明文密码）
     */
    public static String noDecrypt(String value) {
        return value;
    }
} 