package com.ffcs.oss.utils.dfs;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate工具类
 */
public class RestTemplateUtil {
    
    private static final RestTemplate restTemplate = new RestTemplate();
    
    /**
     * 发送GET请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 响应类型
     * @param <T> 响应类型
     * @return 响应实体
     */
    public static <T> ResponseEntity<T> get(String url, HttpHeaders headers, Class<T> responseType) {
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }
    
    /**
     * 发送POST请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param body 请求体
     * @param responseType 响应类型
     * @param <T> 响应类型
     * @return 响应实体
     */
    public static <T> ResponseEntity<T> post(String url, HttpHeaders headers, Object body, Class<T> responseType) {
        HttpEntity<Object> requestEntity = new HttpEntity<>(body, headers);
        return restTemplate.exchange(url, HttpMethod.POST, requestEntity, responseType);
    }
    
    /**
     * 发送DELETE请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 响应类型
     * @param <T> 响应类型
     * @return 响应实体
     */
    public static <T> ResponseEntity<T> delete(String url, HttpHeaders headers, Class<T> responseType) {
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        return restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, responseType);
    }
} 