# 点赞点踩取消功能使用说明

## 功能概述

本功能扩展了原有的点赞点踩功能，支持用户取消自己的点赞或点踩操作。取消操作有以下限制：

1. **只能取消自己的反馈**：用户只能取消自己提交的点赞或点踩
2. **审核状态限制**：
   - 审核通过的反馈不支持取消
   - 审核不通过或审核之前的反馈可以取消

## API接口

### 点赞/点踩/取消接口

**接口地址：** `POST /api/conversation/feedback`

**请求参数：**

```json
{
    "answerId": "回答ID",
    "feedback": "反馈类型",
    "userId": "用户ID",
    "isCancel": false
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| answerId | String | 是 | 回答ID |
| feedback | String | 是 | 反馈类型：1-点赞，2-点踩，0-取消 |
| userId | String | 是 | 用户ID |
| isCancel | Boolean | 否 | 是否为取消操作，默认false |

**响应格式：**

```json
{
    "success": true,
    "message": "操作成功",
    "data": null
}
```

## 使用示例

### 1. 点赞操作

```json
{
    "answerId": "answer-123",
    "feedback": "1",
    "userId": "user-456",
    "isCancel": false
}
```

### 2. 点踩操作

```json
{
    "answerId": "answer-123",
    "feedback": "2",
    "userId": "user-456",
    "isCancel": false
}
```

### 3. 取消操作（方式一：使用isCancel标志）

```json
{
    "answerId": "answer-123",
    "feedback": "0",
    "userId": "user-456",
    "isCancel": true
}
```

### 4. 取消操作（方式二：直接设置feedback为0）

```json
{
    "answerId": "answer-123",
    "feedback": "0",
    "userId": "user-456",
    "isCancel": false
}
```

## 业务逻辑说明

### 1. 点赞/点踩流程

1. 用户提交点赞(1)或点踩(2)请求
2. 系统更新`ai_answer_d`表中的`like_or_no_like_result`字段
3. 系统创建审核记录到`ai_audit_list_d`表，状态为待审核(0)

### 2. 取消流程

1. 用户提交取消请求（`isCancel=true`或`feedback="0"`）
2. 系统检查是否可以取消：
   - 查询对应的审核记录
   - 如果没有审核记录或审核状态为待审核(0)，允许取消
   - 如果审核状态为已审核(1)，拒绝取消
3. 如果允许取消：
   - 更新`ai_answer_d`表中的`like_or_no_like_result`字段为0
   - 删除对应的待审核记录

### 3. 审核流程

1. 管理员在审核列表中查看待审核的反馈
2. 审核通过后，状态变为已审核(1)
3. 已审核的反馈不能被取消

## 数据库变更

### 表结构

本功能使用现有的表结构，主要涉及：

1. **ai_answer_d表**：存储回答和反馈状态
   - `like_or_no_like_result`：1-点赞，2-点踩，0-无

2. **ai_audit_list_d表**：存储审核记录
   - `audit_status`：0-待审核，1-已审核
   - `answer_id`：关联的回答ID
   - `feed_back_user`：反馈用户
   - `like_or_no_like_result`：反馈类型

### 新增索引

为了提高查询性能，建议执行以下SQL：

```sql
-- 创建复合索引用于快速查询用户的反馈记录
CREATE INDEX IF NOT EXISTS idx_audit_answer_user ON ai_audit_list_d (answer_id, feed_back_user);

-- 创建审核状态索引
CREATE INDEX IF NOT EXISTS idx_audit_status ON ai_audit_list_d (audit_status);

-- 创建回答反馈状态索引
CREATE INDEX IF NOT EXISTS idx_answer_like_status ON ai_answer_d (like_or_no_like_result);
```

## 错误处理

### 常见错误码

| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| "回答ID不能为空" | answerId参数为空 | 检查请求参数 |
| "反馈类型不能为空" | feedback参数为空 | 检查请求参数 |
| "回答不存在" | 指定的回答ID不存在 | 检查回答ID是否正确 |
| "该反馈已审核通过，无法取消" | 尝试取消已审核的反馈 | 只能取消待审核的反馈 |

## 注意事项

1. **权限控制**：用户只能取消自己的反馈，不能取消其他用户的反馈
2. **审核状态**：一旦反馈被审核通过，就不能再取消
3. **数据一致性**：取消操作会同时更新回答状态和删除审核记录
4. **并发控制**：在高并发场景下，建议在业务层面加锁防止重复操作

## 测试用例

系统提供了完整的测试用例，位于：
`src/test/java/com/ffcs/oss/service/FeedbackCancelTest.java`

测试覆盖以下场景：
- 正常点赞/点踩操作
- 取消未审核的反馈
- 尝试取消已审核的反馈（应该失败）
- 参数验证
- 边界条件测试
