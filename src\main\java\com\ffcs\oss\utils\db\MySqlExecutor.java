package com.ffcs.oss.utils.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * MySQL SQL执行器实现
 */
public class MySqlExecutor extends AbstractSqlExecutor {
    
    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private final String password;
    private final String params;
    
    public MySqlExecutor(String host, int port, String database, String username, String password, String params) {
        this.host = host;
        this.port = port;
        this.database = database;
        this.username = username;
        this.password = password;
        this.params = params;
    }
    
    @Override
    protected Connection createConnection() throws SQLException {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            String url = "jdbc:mysql://" + host + ":" + port + "/" + database;
            if (params != null && !params.isEmpty()) {
                if (!params.startsWith("?")) {
                    url += "?";
                }
                url += params;
            } else {
                url += "?useSSL=false&serverTimezone=UTC";
            }
            
            return DriverManager.getConnection(url, username, password);
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL JDBC驱动未找到", e);
        }
    }
} 