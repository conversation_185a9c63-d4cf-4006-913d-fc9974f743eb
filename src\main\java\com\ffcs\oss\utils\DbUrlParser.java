package com.ffcs.oss.utils;

import com.ffcs.oss.web.rest.constant.DbConnectionConstant;
import com.ffcs.oss.web.rest.evt.dbconnection.DbConnectionConfigEvt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据库URL解析工具类
 */
public class DbUrlParser {
    private static final Logger logger = LoggerFactory.getLogger(DbUrlParser.class);

    /**
     * 解析数据库连接URL
     *
     * @param url 数据库连接URL
     * @return 解析后的DbConnectionConfigEvt对象
     */
    public static void parseUrl(String url, DbConnectionConfigEvt evt) {
        if (url == null || url.isEmpty()) {
            return;
        }

        logger.debug("开始解析数据库URL: {}", url);

        // 判断数据库类型
        if (url.startsWith("jdbc:mysql:")) {
            logger.debug("识别为MySQL URL");
            parseMySqlUrl(url, evt);
        } else if (url.startsWith("jdbc:postgresql:")) {
            logger.debug("识别为PostgreSQL URL");
            parsePostgreSqlUrl(url, evt);
        } else if (url.startsWith("jdbc:hive2:")) {
            logger.debug("识别为Hive URL");
            parseHiveUrl(url, evt);
        }
        
        logger.debug("URL解析结果: 数据库类型={}, 主机={}, 端口={}, 数据库名={}, 认证类型={}, 参数={}", 
                evt.getDbType(), evt.getHost(), evt.getPort(), evt.getDatabaseName(), evt.getAuthType(), evt.getParams());
    }

    /**
     * 解析MySQL数据库连接URL
     *
     * @param url MySQL连接URL
     * @param evt 数据库连接配置事件
     */
    private static void parseMySqlUrl(String url, DbConnectionConfigEvt evt) {
        // 设置数据库类型
        evt.setDbType(DbConnectionConstant.DB_TYPE_MYSQL);

        // 解析URL
        // 格式：***********************************************************
        Pattern pattern = Pattern.compile("jdbc:mysql://([^:/]+)(?::(\\d+))?/([^?]+)(?:\\?(.*))?");
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            // 主机地址
            evt.setHost(matcher.group(1));

            // 端口号，默认3306
            String portStr = matcher.group(2);
            evt.setPort(portStr != null ? Integer.parseInt(portStr) : 3306);

            // 数据库名
            evt.setDatabaseName(matcher.group(3));

            // 解析参数
            String paramsStr = matcher.group(4);
            if (paramsStr != null && !paramsStr.isEmpty()) {
                evt.setParams(paramsStr);
            }
        }
    }

    /**
     * 解析PostgreSQL数据库连接URL
     *
     * @param url PostgreSQL连接URL
     * @param evt 数据库连接配置事件
     */
    private static void parsePostgreSqlUrl(String url, DbConnectionConfigEvt evt) {
        // 设置数据库类型
        evt.setDbType(DbConnectionConstant.DB_TYPE_POSTGRESQL);

        // 解析URL
        // 格式：****************************************************************
        Pattern pattern = Pattern.compile("jdbc:postgresql://([^:/]+)(?::(\\d+))?/([^?]+)(?:\\?(.*))?");
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            // 主机地址
            evt.setHost(matcher.group(1));

            // 端口号，默认5432
            String portStr = matcher.group(2);
            evt.setPort(portStr != null ? Integer.parseInt(portStr) : 5432);

            // 数据库名
            String dbName = matcher.group(3);
            evt.setDatabaseName(dbName);

            // 解析参数
            String paramsStr = matcher.group(4);
            if (paramsStr != null && !paramsStr.isEmpty()) {
                // 直接将所有参数保存到params中
                evt.setParams(paramsStr);
            }
        }
    }

    /**
     * 解析Hive数据库连接URL
     *
     * @param url Hive连接URL
     * @param evt 数据库连接配置事件
     */
    private static void parseHiveUrl(String url, DbConnectionConfigEvt evt) {
        // 设置数据库类型
        evt.setDbType(DbConnectionConstant.DB_TYPE_HIVE);

        // 解析URL
        // 标准格式：***********************************************************
        // 或ZooKeeper格式：************************,host2:port2/database;serviceDiscoveryMode=zooKeeper;...
        String basePattern = "jdbc:hive2://([^/]+)/([^;]+)(?:;(.*))?";
        Pattern pattern = Pattern.compile(basePattern);
        Matcher matcher = pattern.matcher(url);
        
        logger.debug("Hive URL解析，正则表达式: {}", basePattern);

        if (matcher.find()) {
            // 提取主机地址和端口
            String hostPart = matcher.group(1);
            logger.debug("解析到主机部分: {}", hostPart);
            
            // 处理可能包含逗号的主机列表（ZooKeeper格式）
            if (hostPart.contains(",")) {
                // ZooKeeper模式，使用第一个主机作为主要连接地址
                String[] hosts = hostPart.split(",");
                String firstHost = hosts[0];
                
                logger.debug("检测到ZooKeeper模式，主机列表: {}, 使用第一个主机: {}", hostPart, firstHost);
                
                // 解析第一个主机的主机名和端口
                String[] hostPort = firstHost.split(":");
                evt.setHost(hostPort[0]);
                evt.setPort(hostPort.length > 1 ? Integer.parseInt(hostPort[1]) : 10000);
                
                // 将完整的主机列表保存在params中
                String hostList = "hosts=" + hostPart;
                if (evt.getParams() != null && !evt.getParams().isEmpty()) {
                    evt.setParams(hostList + ";" + evt.getParams());
                } else {
                    evt.setParams(hostList);
                }
                
                logger.debug("设置主机: {}, 端口: {}, 参数中添加主机列表", evt.getHost(), evt.getPort());
            } else {
                // 标准模式
                logger.debug("标准模式，主机部分: {}", hostPart);
                String[] hostPort = hostPart.split(":");
                evt.setHost(hostPort[0]);
                evt.setPort(hostPort.length > 1 ? Integer.parseInt(hostPort[1]) : 10000);
                
                logger.debug("设置主机: {}, 端口: {}", evt.getHost(), evt.getPort());
            }

            // 数据库名
            String dbName = matcher.group(2);
            evt.setDatabaseName(dbName);
            logger.debug("设置数据库名: {}", dbName);

            // 解析参数
            String paramsStr = matcher.group(3);
            if (paramsStr != null && !paramsStr.isEmpty()) {
                logger.debug("解析参数部分: {}", paramsStr);
                
                // 检查认证类型
                if (DbConnectionConstant.AUTH_TYPE_KERBEROS.equals(evt.getAuthType())) {
                    evt.setAuthType(DbConnectionConstant.AUTH_TYPE_KERBEROS);
                    logger.debug("检测到Kerberos认证");
                    
                    // 提取principal
                    Pattern principalPattern = Pattern.compile("principal=([^;]+)");
                    Matcher principalMatcher = principalPattern.matcher(paramsStr);
                    if (principalMatcher.find()) {
                        String principal = principalMatcher.group(1);
                        evt.setPrincipal(principal);
                        logger.debug("设置Kerberos principal: {}", principal);
                        
                        // 从principal中提取用户名
                        try {
                            String[] parts = principal.split("@")[0].split("/");
                            String username = parts[0]; // 通常principal格式为username/instance@REALM
                            evt.setUsername(username);
                            logger.debug("从principal中提取用户名: {}", username);
                        } catch (Exception e) {
                            logger.warn("无法从principal({})中提取用户名", principal, e);
                        }
                        
                        // 将其他参数保存
                        String otherParams = paramsStr.replace(principalMatcher.group(0), "")
                                .replace(";;", ";")
                                .replaceAll("^;|;$", "");
                        
                        if (!otherParams.isEmpty()) {
                            if (evt.getParams() != null && !evt.getParams().isEmpty()) {
                                evt.setParams(evt.getParams() + ";" + otherParams);
                            } else {
                                evt.setParams(otherParams);
                            }
                            logger.debug("设置其他参数: {}", otherParams);
                        }
                    }
                } else if (DbConnectionConstant.AUTH_TYPE_PASSWORD.equals(evt.getAuthType())){
                    evt.setAuthType(DbConnectionConstant.AUTH_TYPE_PASSWORD);
                    logger.debug("设置密码认证");
                    
                    // 尝试从参数中提取用户名和密码
                    Pattern usernamePattern = Pattern.compile("user=([^;]+)");
                    Matcher usernameMatcher = usernamePattern.matcher(paramsStr);
                    if (usernameMatcher.find()) {
                        String username = usernameMatcher.group(1);
                        evt.setUsername(username);
                        logger.debug("从参数中提取用户名: {}", username);
                    }
                    
                    // 尝试提取密码
                    Pattern passwordPattern = Pattern.compile("password=([^;]+)");
                    Matcher passwordMatcher = passwordPattern.matcher(paramsStr);
                    if (passwordMatcher.find()) {
                        String password = passwordMatcher.group(1);
                        evt.setPassword(password);
                        logger.debug("从参数中提取密码");
                    }
                    
                    if (evt.getParams() != null && !evt.getParams().isEmpty()) {
                        evt.setParams(evt.getParams() + ";" + paramsStr);
                    } else {
                        evt.setParams(paramsStr);
                    }
                    logger.debug("设置参数: {}", paramsStr);
                }
            } else {
                evt.setAuthType(DbConnectionConstant.AUTH_TYPE_PASSWORD);
                logger.debug("无参数，默认设置密码认证");
            }
            
            // 如果用户名仍然为空，设置默认值
            if (evt.getUsername() == null || evt.getUsername().isEmpty()) {
                // 对于Kerberos认证，使用"hiveuser"作为默认用户名
                if (DbConnectionConstant.AUTH_TYPE_KERBEROS.equals(evt.getAuthType())) {
                    evt.setUsername("hiveuser");
                } else {
                    // 对于密码认证，如果没有设置用户名，使用"default"
                    evt.setUsername("default");
                }
                logger.debug("设置默认用户名: {}", evt.getUsername());
            }
            
            // 如果密码为空，设置默认值
            if (evt.getPassword() == null || evt.getPassword().isEmpty()) {
                evt.setPassword("default_password");
                logger.debug("设置默认密码");
            }
        } else {
            logger.error("URL格式不匹配: {}", url);
        }
    }

    /**
     * 测试URL解析
     * 用于内部测试和调试URL解析逻辑
     * 
     * @param url 要测试的URL
     * @return 解析后的参数Map
     */
    public static Map<String, Object> testParseUrl(String url) {
        DbConnectionConfigEvt evt = new DbConnectionConfigEvt();
        parseUrl(url, evt);
        
        Map<String, Object> result = new HashMap<>();
        result.put("dbType", evt.getDbType());
        result.put("host", evt.getHost());
        result.put("port", evt.getPort());
        result.put("databaseName", evt.getDatabaseName());
        result.put("authType", evt.getAuthType());
        result.put("params", evt.getParams());
        result.put("principal", evt.getPrincipal());
        
        logger.info("测试URL解析结果: {}", result);
        return result;
    }
} 