package com.ffcs.oss.config;

import com.ffcs.oss.param.out.ServiceResp;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.List;


/**
 * springboot实体类校验注解解析
 * <AUTHOR>
 * @Date 2022/10/19 22:58
 **/
@ControllerAdvice
public class ValidationExceptionHandler extends ResponseEntityExceptionHandler {

    @Override
    public ResponseEntity<Object> handleBindException(final BindException ex, final HttpHeaders headers,
                                                      final HttpStatus status, final WebRequest request) {

        return new ResponseEntity<>(getError(ex.getBindingResult().getAllErrors()), status);
    }

    /**
     * 解决 JSON 请求统一返回参数
     */
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(final MethodArgumentNotValidException ex,
                                                                  final HttpHeaders headers, final HttpStatus status,
                                                                  final WebRequest request) {

        ResponseEntity.status(HttpStatus.OK).body(getError(ex.getBindingResult().getAllErrors()));
        //return new ResponseEntity<>(getError(ex.getBindingResult().getAllErrors()), status);
        return ResponseEntity.status(HttpStatus.OK).body(getError(ex.getBindingResult().getAllErrors()));
    }

    private ServiceResp getError(final List<ObjectError> allErrors) {

        final StringBuffer message = new StringBuffer();
        for (final ObjectError error : allErrors) {
            message.append(error.getDefaultMessage()).append("; ");
        }
        logger.error(message.substring(0, message.length() - 2)); // 因为&两边空格
        return ServiceResp.getInstance().error(message.substring(0, message.length() - 2));
    }
}
