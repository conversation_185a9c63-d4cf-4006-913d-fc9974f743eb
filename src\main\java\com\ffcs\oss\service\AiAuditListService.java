package com.ffcs.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ffcs.oss.domain.AiAuditListD;
import com.ffcs.oss.web.rest.evt.audit.AuditEvt;

/**
 * 审核列表Service接口
 */
public interface AiAuditListService extends IService<AiAuditListD> {
    
    /**
     * 将反馈加入待办列表
     * @param evt
     * @return 是否成功
     */
    boolean addToTodoList(AuditEvt evt);
    
    /**
     * 完成审核
     * @param auditListD 审核信息
     * @return 是否成功
     */
    boolean completeAudit(AiAuditListD auditListD);
} 