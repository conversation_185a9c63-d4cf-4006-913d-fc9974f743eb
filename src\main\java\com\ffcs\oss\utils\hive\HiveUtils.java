package com.ffcs.oss.utils.hive;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Closeable;
import java.io.IOException;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;

/**
 * Hive工具类
 * 提供常用的Hive操作方法
 */
@Slf4j
public class HiveUtils {

    private HiveUtils() {
    }

    /**
     * 创建一个默认的Hive连接参数对象
     * 
     * @return Hive连接参数
     */
    public static HiveConnectionParam createDefaultConnectionParam() {
        HiveConnectionParam param = new HiveConnectionParam();
        param.setJdbcUrl("**************************************,bigdata2.oss.ffcs.cn:2181,bigdata3.oss.ffcs.cn:2181/ffcs;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2");
        param.setKerberosEnabled(true);
        param.setJavaSecurityKrb5Conf("E:\\ffcs\\ai\\hive\\ai-chat-bi-web\\krb5.conf");
        param.setLoginUserKeytabUsername("ffcs/<EMAIL>");
        param.setLoginUserKeytabPath("E:\\ffcs\\ai\\hive\\ai-chat-bi-web\\ffcs.service.keytab");
        return param;
    }

    /**
     * 测试Hive连接
     * 
     * @param connectionParam Hive连接参数
     * @return 连接是否成功
     */
    public static boolean testConnection(HiveConnectionParam connectionParam) {
        try (Connection connection = HiveConnectionManager.getAdHocConnection(connectionParam)) {
            return connection != null && !connection.isClosed();
        } catch (Exception e) {
            log.error("测试Hive连接失败", e);
            return false;
        }
    }

    /**
     * 执行查询操作
     * 
     * @param connectionParam Hive连接参数
     * @param sql SQL语句
     * @return 查询结果列表
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    public static List<Map<String, Object>> executeQuery(HiveConnectionParam connectionParam, String sql) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        try (Connection connection = HiveConnectionManager.getPooledConnection(connectionParam);
             Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery(sql)) {
            
            return resultSetToList(resultSet);
        }
    }

    /**
     * 执行更新操作
     * 
     * @param connectionParam Hive连接参数
     * @param sql SQL语句
     * @return 影响的行数
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    public static int executeUpdate(HiveConnectionParam connectionParam, String sql) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        try (Connection connection = HiveConnectionManager.getPooledConnection(connectionParam);
             Statement statement = connection.createStatement()) {
            
            return statement.executeUpdate(sql);
        }
    }

    /**
     * 获取数据库列表
     * 
     * @param connectionParam Hive连接参数
     * @return 数据库列表
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    public static List<String> getDatabases(HiveConnectionParam connectionParam) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        String sql = "SHOW DATABASES";
        List<Map<String, Object>> result = executeQuery(connectionParam, sql);
        List<String> databases = new ArrayList<>();
        for (Map<String, Object> row : result) {
            databases.add(row.values().iterator().next().toString());
        }
        return databases;
    }

    /**
     * 获取表列表
     * 
     * @param connectionParam Hive连接参数
     * @param database 数据库名称
     * @return 表列表
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    public static List<String> getTables(HiveConnectionParam connectionParam, String database) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        String sql = StringUtils.isBlank(database) ? "SHOW TABLES" : "SHOW TABLES IN " + database;
        List<Map<String, Object>> result = executeQuery(connectionParam, sql);
        List<String> tables = new ArrayList<>();
        for (Map<String, Object> row : result) {
            tables.add(row.values().iterator().next().toString());
        }
        return tables;
    }

    /**
     * 获取表结构
     * 
     * @param connectionParam Hive连接参数
     * @param database 数据库名称
     * @param table 表名称
     * @return 列信息列表
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    public static List<Map<String, Object>> getTableSchema(HiveConnectionParam connectionParam, String database, String table) 
            throws SQLException, IOException, InterruptedException, ExecutionException {
        String tableName = StringUtils.isBlank(database) ? table : database + "." + table;
        String sql = "DESCRIBE " + tableName;
        return executeQuery(connectionParam, sql);
    }

    /**
     * 执行Hive操作并自动处理资源关闭
     * 
     * @param connectionParam Hive连接参数
     * @param function 操作函数
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 异常
     */
    public static <T> T withConnection(HiveConnectionParam connectionParam, Function<Connection, T> function) throws Exception {
        try (Connection connection = HiveConnectionManager.getPooledConnection(connectionParam)) {
            return function.apply(connection);
        }
    }

    /**
     * 将ResultSet转换为List<Map<String, Object>>
     * 
     * @param rs ResultSet对象
     * @return 结果列表
     * @throws SQLException 数据库异常
     */
    private static List<Map<String, Object>> resultSetToList(ResultSet rs) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        while (rs.next()) {
            Map<String, Object> row = new HashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                row.put(metaData.getColumnName(i), rs.getObject(i));
            }
            results.add(row);
        }
        
        return results;
    }

    /**
     * 安全关闭资源
     * 
     * @param closeable 可关闭资源
     */
    public static void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                log.error("关闭资源失败", e);
            }
        }
    }

    /**
     * 安全关闭SQL资源
     * 
     * @param autoCloseable 可关闭资源
     */
    public static void closeQuietly(AutoCloseable autoCloseable) {
        if (autoCloseable != null) {
            try {
                autoCloseable.close();
            } catch (Exception e) {
                log.error("关闭资源失败", e);
            }
        }
    }
} 