package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.domain.AiAssistantD;
import com.ffcs.oss.mapper.AiAssistantMapper;
import com.ffcs.oss.service.AiAssistantService;
import com.ffcs.oss.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 助理Service实现类
 */
@Service
public class AiAssistantServiceImpl extends ServiceImpl<AiAssistantMapper, AiAssistantD> implements AiAssistantService {
    
    @Autowired
    private UserService userService;
    
    /**
     * 检查助理名称是否已存在
     *
     * @param assistantName 助理名称
     * @param excludeAssistantId 排除的助理ID（用于更新时排除自身）
     * @return 如果名称已存在返回true，否则返回false
     */
    @Override
    public boolean checkAssistantNameExists(String assistantName, Long excludeAssistantId) {
        LambdaQueryWrapper<AiAssistantD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiAssistantD::getAssistantName, assistantName);
        
        // 只检查当前用户创建的助理名称是否重复
        String currentUserName = userService.getCurrentUserName();
        queryWrapper.eq(AiAssistantD::getCreateUserName, currentUserName);
        
        // 如果是更新操作，需要排除自身
        if (excludeAssistantId != null) {
            queryWrapper.ne(AiAssistantD::getAssistantId, excludeAssistantId);
        }
        
        return baseMapper.selectCount(queryWrapper) > 0;
    }
} 