package com.ffcs.oss.utils.hive;

import lombok.Data;

/**
 * Hive连接参数类
 */
@Data
public class HiveConnectionParam {

    /**
     * JDBC URL
     */
    private String jdbcUrl;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 是否启用Kerberos认证
     */
    private boolean kerberosEnabled = false;

    /**
     * Kerberos配置文件路径
     */
    private String javaSecurityKrb5Conf;

    /**
     * Kerberos用户名
     */
    private String loginUserKeytabUsername;

    /**
     * Kerberos密钥表文件路径
     */
    private String loginUserKeytabPath;

    /**
     * Hive配置文件路径
     */
    private String hiveSitePath;

    /**
     * Hadoop core-site.xml配置文件路径
     */
    private String coreSitePath;

    /**
     * Hadoop hdfs-site.xml配置文件路径
     */
    private String hdfsSitePath;
} 