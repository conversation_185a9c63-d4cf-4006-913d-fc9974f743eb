package com.ffcs.oss.utils.dfs;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ffcs.oss.config.CtdfsConfig;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 小文件系统服务类
 */
@Component
public class CtdfsService {
    
    private static final Logger log = LoggerFactory.getLogger(CtdfsService.class);
    
    /**
     * DFS会话
     */
    private DfsSession dfsSession;
    
    @Resource
    private CtdfsConfig ctdfsConfig;
    
    /**
     * 连接到小文件系统服务器
     */
    public void connect() {
        try {
            log.info("正在连接小文件系统: {}:{}，类型: {}", 
                ctdfsConfig.getHost(), ctdfsConfig.getPort(), ctdfsConfig.getType());
            createDfsSession();
            dfsSession.connect();
            log.info("小文件系统连接成功");
        } catch (Exception e) {
            log.error("连接小文件系统失败: {}", e.getMessage(), e);
            // 不再抛出异常，而是让调用者处理错误，便于降级
            dfsSession = null;
        }
    }
    
    /**
     * 关闭资源
     * 
     * @param dfsSession DFS会话
     * @param inputStream 输入流
     * @param fileOutputStream 文件输出流
     */
    public void close(DfsSession dfsSession, InputStream inputStream, FileOutputStream fileOutputStream) {
        try {
            if (dfsSession != null) {
                dfsSession.destroy();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            if (fileOutputStream != null) {
                fileOutputStream.close();
            }
        } catch (IOException e) {
            log.error("关闭资源异常", e);
        }
    }
    
    /**
     * 上传文件
     * 
     * @param localFile 本地文件
     * @param remoteFilename 远程文件名
     * @return 远程文件路径
     */
    public String uploadFile(File localFile, String remoteFilename) {
        log.debug("开始上传文件");
        try {
            this.connect();
            dfsSession.uploadFile(localFile, remoteFilename);
            return remoteFilename;
        } catch (Exception e) {
            log.error("文件上传失败,{}", remoteFilename, e);
            return null;
        } finally {
            close(dfsSession, null, null);
        }
    }
    
    /**
     * 上传文件
     * 
     * @param remoteFilename 远程文件名
     * @param fileName 文件名
     * @param originFileName 本地文件路径
     * @return 远程文件路径
     */
    public String uploadFile(String remoteFilename, String fileName, String originFileName) {
        log.debug("开始上传文件");
        try (InputStream input = new FileInputStream(new File(originFileName))) {
            return uploadFile(remoteFilename, fileName, input);
        } catch (Exception e) {
            log.error("文件上传失败,{}", fileName, e);
            return null;
        }
    }
    
    /**
     * 上传文件
     * 
     * @param remoteFilename 远程文件名
     * @param fileName 文件名
     * @param inputStream 输入流
     * @return 远程文件路径
     */
    public String uploadFile(String remoteFilename, String fileName, InputStream inputStream) {
        try {
            this.connect();
            log.debug("开始上传文件,上传路径:{}", remoteFilename);
            try {
                dfsSession.uploadFile(remoteFilename, inputStream);
                return remoteFilename;
            } catch (Exception e) {
                // 记录详细错误信息但继续执行
                log.error("上传文件失败,{}, 错误: {}", fileName, e.getMessage(), e);
                // 返回null表示上传失败，会触发降级逻辑
                return null;
            }
        } catch (Exception e) {
            log.error("连接小文件系统或上传文件失败,{}, 错误: {}", fileName, e.getMessage(), e);
            return null;
        } finally {
            close(dfsSession, inputStream, null);
        }
    }
    
    /**
     * 下载文件到本地
     * 
     * @param remoteFilename 远程文件名
     * @param localFile 本地文件
     * @return 远程文件路径
     */
    public String downloadFile(String remoteFilename, File localFile) {
        try {
            this.connect();
            if (dfsSession.exists(remoteFilename)) {
                dfsSession.downloadFile(remoteFilename, localFile);
                log.info("文件下载成功！{},文件大小:{}", remoteFilename, localFile.length());
                return remoteFilename;
            } else {
                log.info("{}可能不存在", remoteFilename);
            }
        } catch (Exception e) {
            log.error("文件下载失败！{}", remoteFilename, e);
        } finally {
            close(dfsSession, null, null);
        }
        return null;
    }
    
    /**
     * 获取文件流
     * 
     * @param remoteFilename 远程文件名
     * @return 文件输入流
     */
    public InputStream downloadStream(String remoteFilename) {
        try {
            this.connect();
            if (dfsSession.exists(remoteFilename)) {
                return dfsSession.downloadStream(remoteFilename);
            } else {
                log.info("{}可能不存在", remoteFilename);
            }
        } catch (Exception e) {
            log.error("获取文件流失败！{}", remoteFilename, e);
        }
        return null;
    }
    
    /**
     * 删除文件
     * 
     * @param remoteFilename 远程文件名
     */
    public void deleteFile(String remoteFilename) {
        try {
            this.connect();
            if (dfsSession.exists(remoteFilename)) {
                dfsSession.deleteFile(remoteFilename);
                log.info("文件删除成功！{}", remoteFilename);
            } else {
                log.info("{}可能不存在", remoteFilename);
            }
        } catch (Exception e) {
            log.error("文件删除失败！{}", remoteFilename, e);
        } finally {
            close(dfsSession, null, null);
        }
    }
    
    /**
     * 创建DFS会话
     */
    private void createDfsSession() {
        if (dfsSession != null) {
            dfsSession.destroy();
        }
        
        DfsConnInfo connInfo = new DfsConnInfo();
        connInfo.setHost(ctdfsConfig.getHost());
        try {
            connInfo.setPort(Integer.parseInt(ctdfsConfig.getPort()));
        } catch (NumberFormatException e) {
            connInfo.setPort(21); // 默认FTP端口
        }
        connInfo.setUserName(ctdfsConfig.getUserName());
        connInfo.setPassword(ctdfsConfig.getPassword());
        connInfo.setBaseUrl(ctdfsConfig.getBaseUrl());
        connInfo.setTimeDiff(ctdfsConfig.getTimeDiff());
        connInfo.setRestPassword(ctdfsConfig.getRestPassword());
        try {
            connInfo.setRestPort(Integer.parseInt(ctdfsConfig.getRestPort()));
        } catch (NumberFormatException e) {
            connInfo.setRestPort(8080); // 默认REST端口
        }
        connInfo.setScheme(ctdfsConfig.getScheme());
        connInfo.setPoint(ctdfsConfig.getPoint());
        connInfo.setType(ctdfsConfig.getType());
        connInfo.setEncryption(ctdfsConfig.getEncryption());
        
        // Minio相关设置
        connInfo.setAccessKey(ctdfsConfig.getAccessKey());
        connInfo.setSecretKey(ctdfsConfig.getSecretKey());
        connInfo.setBucketName(ctdfsConfig.getBucketName());
        connInfo.setEndpoint(ctdfsConfig.getEndpoint());
        
        // 根据配置选择会话类型
        String type = ctdfsConfig.getType();
        dfsSession = new DfsSessionProxy(type, connInfo);
    }
} 