package com.ffcs.oss.service;

import com.ffcs.oss.web.rest.evt.permission.PermissionConfigEvt;
import com.ffcs.oss.web.rest.vm.user.UserGroupVm;
import com.ffcs.oss.web.rest.vm.user.UserVm;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 获取当前登录用户
     *
     * @return 当前登录用户名
     */
    String getCurrentUserName();
    
    /**
     * 搜索用户列表
     *
     * @param keyword 搜索关键字
     * @return 用户列表
     */
    List<UserVm> searchUsers(String keyword);
    
    /**
     * 搜索用户组列表
     *
     * @param keyword 搜索关键字
     * @return 用户组列表
     */
    List<UserGroupVm> searchUserGroups(String keyword);
} 