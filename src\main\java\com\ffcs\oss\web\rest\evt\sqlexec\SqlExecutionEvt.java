package com.ffcs.oss.web.rest.evt.sqlexec;


import com.ffcs.oss.param.in.QueryPageEvt;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * SQL执行请求模型
 */
public class SqlExecutionEvt extends QueryPageEvt implements Serializable {
    
    /**
     * 数据源类型
     */
    @NotEmpty(message = "数据源类型不能为空")
    private String dbType;
    
    /**
     * 数据源名称
     */
    @NotEmpty(message = "数据源名称不能为空")
    private String dataSourceName;
    
    /**
     * 要执行的SQL语句
     */
    @NotEmpty(message = "SQL语句不能为空")
    private String sqlStatement;
    
    /**
     * 最大返回结果行数（可选，默认1000）
     */
    private Integer maxRows = 1000;

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getDataSourceName() {
        return dataSourceName;
    }

    public void setDataSourceName(String dataSourceName) {
        this.dataSourceName = dataSourceName;
    }

    public String getSqlStatement() {
        return sqlStatement;
    }

    public void setSqlStatement(String sqlStatement) {
        this.sqlStatement = sqlStatement;
    }

    public Integer getMaxRows() {
        return maxRows;
    }

    public void setMaxRows(Integer maxRows) {
        this.maxRows = maxRows;
    }
} 