package com.ffcs.oss.web.rest.vm.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 用户组视图模型
 */
@ApiModel("用户组视图模型")
public class UserGroupVm implements Serializable {
    
    @ApiModelProperty("用户组ID")
    private String groupId;
    
    @ApiModelProperty("用户组名称")
    private String groupName;
    
    @ApiModelProperty("描述")
    private String description;
    
    public String getGroupId() {
        return groupId;
    }
    
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    
    public String getGroupName() {
        return groupName;
    }
    
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
} 