package com.ffcs.oss;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @description
 * @create 2023/8/2 13:42
 */
@SpringBootApplication
@MapperScan(basePackages = {"com.ffcs.oss.mapper"})
@EnableFeignClients(basePackages = "com.ffcs.oss.client")
//@SpringBootApplication(scanBasePackages = {"com.ffcs.oss.database","com.ffcs.oss"})
//@MapperScan(basePackages = {"com.ffcs.oss.database.mapper","com.ffcs.oss.mapper"})
public class AiChatBiWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(AiChatBiWebApplication.class, args);
    }
}
