package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.domain.AiAuditListD;
import com.ffcs.oss.domain.AiTodoListD;
import com.ffcs.oss.mapper.AiTodoListMapper;
import com.ffcs.oss.security.SecurityUtils;import com.ffcs.oss.service.AiAuditListService;
import com.ffcs.oss.service.AiTodoListService;
import com.ffcs.oss.utils.StringUtils;import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 待办列表Service实现类
 */
@Service
public class AiTodoListServiceImpl extends ServiceImpl<AiTodoListMapper, AiTodoListD> implements AiTodoListService {

    @Autowired
    private AiAuditListService aiAuditListService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeAuditFromTodo(Long todoListId) {
        // 查询待办记录
        AiTodoListD todoListD = this.getById(todoListId);
        if (todoListD == null) {
            return false;
        }
        
        // 从待办列表中删除
        boolean removed = this.removeById(todoListId);
        if (!removed) {
            return false;
        }
        
        // 创建审核记录
        AiAuditListD auditListD = new AiAuditListD();
        BeanUtils.copyProperties(todoListD, auditListD);
        auditListD.setAuditListId(null); // 清空ID，让数据库生成
        auditListD.setAuditStatus("1"); // 设置状态为已审核
        auditListD.setCreateTime(LocalDateTime.now());
        
        // 保存到审核列表
        return aiAuditListService.save(auditListD);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeTodoAudit(com.ffcs.oss.web.rest.evt.audit.AuditEvt evt) {
        // 查询待办项
        AiTodoListD todoListD = this.getById(evt.getTodoListId());
        if (todoListD == null) {
            return false;
        }
        
        // 更新待办项信息
        todoListD.setCorrectSql(evt.getCorrectSql());
        todoListD.setProfessionalKnowledge(evt.getProfessionalKnowledge());
        todoListD.setUpdateTime(LocalDateTime.now());
        todoListD.setUpdateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");
        
        // 更新待办项
        this.updateById(todoListD);
        
        // 从待办列表移到审核列表并设为已审核
        return this.completeAuditFromTodo(evt.getTodoListId());
    }
} 