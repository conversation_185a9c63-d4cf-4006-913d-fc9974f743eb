package com.ffcs.oss.utils.dfs;

import com.ffcs.oss.common.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.io.IOUtils;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * 基于JSCH实现的SFTP会话
 */
public class SftpSession implements DfsSession {
    private static final Logger logger = LoggerFactory.getLogger(SftpSession.class);
    
    /**
     * 连接信息
     */
    private DfsConnInfo connInfo;
    
    /**
     * JSCH的SFTP客户端
     */
    private ChannelSftp sftpClient;
    
    /**
     * JSch会话
     */
    private Session session;
    
    /**
     * 构造函数
     *
     * @param connInfo 连接信息
     */
    public SftpSession(DfsConnInfo connInfo) {
        this.connInfo = connInfo;
    }
    
    @Override
    public void connect() {
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(connInfo.getUserName(), connInfo.getHost(), connInfo.getPort());
            session.setPassword(connInfo.getPassword());
            
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            session.setConfig(sshConfig);
            
            // 设置连接超时
            session.connect(30000); // 30秒
            
            Channel channel = session.openChannel("sftp");
            
            // 设置通道连接超时
            channel.connect(30000); // 30秒
            sftpClient = (ChannelSftp) channel;
            
            // 优化目录处理：确保工作目录存在或降级处理
            String baseUrl = connInfo.getBaseUrl() != null ? connInfo.getBaseUrl() : "/";
            try {
                // 先尝试使用根目录
                sftpClient.cd("/");
                
                if (!"/".equals(baseUrl)) {
                    // 再尝试切换到基础目录
                    try {
                        sftpClient.cd(baseUrl);
                        logger.info("成功切换到目录: {}", baseUrl);
                    } catch (SftpException e) {
                        // 如果基础目录不存在，记录错误但不抛出异常
                        logger.warn("基础目录不存在，将使用根目录: {}, 错误: {}", baseUrl, e.getMessage());
                        // 设置baseUrl为根目录
                        connInfo.setBaseUrl("/");
                    }
                }
            } catch (SftpException e) {
                // 连接到根目录都失败，可能是严重错误
                logger.error("无法切换到根目录，SFTP连接可能存在问题: {}", e.getMessage(), e);
                throw e; // 重新抛出异常，将导致连接失败
            }
            
            logger.info("SFTP server connected: {}:{}, 工作目录: {}", connInfo.getHost(), connInfo.getPort(), connInfo.getBaseUrl());
        } catch (JSchException | SftpException e) {
            logger.error("Failed to connect to SFTP server: {}", e.getMessage(), e);
            throw new BusinessException("连接SFTP服务器失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public void destroy() {
        if (sftpClient != null) {
            sftpClient.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }
    
    @Override
    public boolean exists(String filename) {
        checkConnection();
        try {
            sftpClient.lstat(filename);
            return true;
        } catch (SftpException e) {
            return false;
        }
    }

    @Override
    public void downloadFile(String remoteFilename, File localFile) {
        checkConnection();
        try (OutputStream out = new FileOutputStream(localFile)) {
            sftpClient.get(remoteFilename, out);
        } catch (SftpException | IOException e) {
            logger.error("下载文件失败: {} -> {}, 错误: {}", remoteFilename, localFile.getAbsolutePath(), e.getMessage(), e);
            throw new BusinessException("下载文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public InputStream downloadStream(String remoteFilename) {
        checkConnection();
        try {
            InputStream inputStream = sftpClient.get(remoteFilename);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 使用IOUtils类复制流内容
            IOUtils.copy(inputStream, outputStream);
            inputStream.close();
            // 返回可重复读取的输入流
            return new ByteArrayInputStream(outputStream.toByteArray());
        } catch (SftpException | IOException e) {
            logger.error("获取文件流失败: {}, 错误: {}", remoteFilename, e.getMessage(), e);
            throw new BusinessException("获取文件流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void uploadFile(File localFile, String remoteFilename) {
        checkConnection();
        String dir = getParentPath(remoteFilename);
        if (!exists(dir)) {
            mkDirs(dir);
        }
        try (InputStream in = new FileInputStream(localFile)) {
            sftpClient.put(in, remoteFilename);
            logger.info("文件上传成功: {} -> {}", localFile.getAbsolutePath(), remoteFilename);
        } catch (SftpException | IOException e) {
            logger.error("上传文件失败: {} -> {}, 错误: {}", localFile.getAbsolutePath(), remoteFilename, e.getMessage(), e);
            throw new BusinessException("上传文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean uploadFile(String remoteFilename, InputStream inputStream) {
        checkConnection();
        String dir = getParentPath(remoteFilename);
        if (!exists(dir)) {
            try {
                mkDirs(dir);
            } catch (Exception e) {
                logger.warn("创建目录失败: {}, 将尝试直接上传文件", dir);
                // 即使创建目录失败，也尝试上传文件（目录可能已存在但权限不允许列出）
            }
        }
        try {
            sftpClient.put(inputStream, remoteFilename);
            logger.info("文件流上传成功: {}", remoteFilename);
            return true;
        } catch (SftpException e) {
            logger.error("上传文件流失败: {}, 错误: {}", remoteFilename, e.getMessage(), e);
            // 如果是权限问题，不抛出异常，让调用方降级处理
            if (e.getMessage() != null && e.getMessage().contains("Permission denied")) {
                logger.warn("文件上传权限被拒绝: {}, 将进行降级处理", remoteFilename);
                return false;
            }
            throw new BusinessException("上传文件流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void deleteFile(String filename) {
        checkConnection();
        try {
            sftpClient.rm(filename);
            logger.info("文件删除成功: {}", filename);
        } catch (SftpException e) {
            logger.error("删除文件失败: {}, 错误: {}", filename, e.getMessage(), e);
            throw new BusinessException("删除文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void mkDirs(String dir) {
        checkConnection();
        // 完全禁止创建目录，使用baseUrl作为存储位置
        logger.info("跳过创建目录: {}, 将直接使用配置的baseUrl目录", dir);
        return;
    }

    /**
     * 获取父路径
     *
     * @param path 路径
     * @return 父路径
     */
    private String getParentPath(String path) {
        if (path == null || path.isEmpty() || "/".equals(path)) {
            return "/";
        }
        int lastIndex = path.lastIndexOf('/');
        if (lastIndex <= 0) {
            return "/";
        }
        return path.substring(0, lastIndex);
    }

    /**
     * 检查连接状态，如果未连接则自动连接
     */
    private void checkConnection() {
        if (sftpClient == null || session == null || !session.isConnected()) {
            connect();
        }
    }
} 