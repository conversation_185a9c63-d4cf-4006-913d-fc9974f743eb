package com.ffcs.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ffcs.oss.domain.AiShareD;

import java.util.Map;

/**
 * 分享Service接口
 */
public interface AiShareService extends IService<AiShareD> {
    /**
     * 创建分享
     * @param questionId 问题ID
     * @param answerId 回答ID
     * @param username 用户名
     * @return 分享链接
     */
    String createShare(String questionId, String answerId, String username);
    
    /**
     * 获取分享详情
     * @param shareId 分享ID
     * @return 分享详情（包含问题和回答）
     */
    Map<String, Object> getShareDetail(String shareId);
    
    /**
     * 增加访问次数
     * @param shareId 分享ID
     * @return 是否成功
     */
    boolean increaseVisitCount(String shareId);
} 