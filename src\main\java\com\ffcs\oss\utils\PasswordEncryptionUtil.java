package com.ffcs.oss.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;

/**
 * 密码加密解密工具类
 */
public class PasswordEncryptionUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(PasswordEncryptionUtil.class);
    
    // 加密算法
    private static final String ALGORITHM = "AES";
    // 加密秘钥 (16字节)
    private static final String SECRET_KEY = "F1F2A3B4C5D6E7G8";
    
    /**
     * 加密密码
     *
     * @param plainPassword 明文密码
     * @return 加密后的密码
     */
    public static String encrypt(String plainPassword) {
        if (plainPassword == null || plainPassword.isEmpty()) {
            return plainPassword;
        }
        
        try {
            Key key = generateKey();
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encryptedBytes = cipher.doFinal(plainPassword.getBytes(StandardCharsets.UTF_8));
            return Base64Utils.encodeToString(encryptedBytes);
        } catch (Exception e) {
            logger.error("密码加密失败", e);
            // 加密失败时返回原密码，避免系统无法使用
            return plainPassword;
        }
    }
    
    /**
     * 解密密码
     *
     * @param encryptedPassword 加密后的密码
     * @return 明文密码
     */
    public static String decrypt(String encryptedPassword) {
        if (encryptedPassword == null || encryptedPassword.isEmpty()) {
            return encryptedPassword;
        }
        
        try {
            Key key = generateKey();
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] decryptedBytes = cipher.doFinal(Base64Utils.decodeFromString(encryptedPassword));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("密码解密失败", e);
            // 解密失败时返回原密码，避免系统无法使用
            return encryptedPassword;
        }
    }
    
    /**
     * 生成密钥
     *
     * @return 密钥
     */
    private static Key generateKey() {
        return new SecretKeySpec(SECRET_KEY.getBytes(), ALGORITHM);
    }
} 