package com.ffcs.oss.web.rest.evt.datamodel;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 数据模型请求参数
 */
@ApiModel("数据模型请求参数")
public class DataModelEvt extends QueryPageEvt implements Serializable {

    @ApiModelProperty("数据模型id")
    private String dataModelId;

    @ApiModelProperty("模型名称")
    @NotBlank(message = "模型名称不能为空")
    private String modelName;

    @ApiModelProperty("模型描述")
    private String modelDesc;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("创建人别名")
    private String alias;

    @ApiModelProperty("更新人")
    private String updateUserName;
    @ApiModelProperty("模糊搜索")
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getDataModelId() {
        return dataModelId;
    }

    public void setDataModelId(String dataModelId) {
        this.dataModelId = dataModelId;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelDesc() {
        return modelDesc;
    }

    public void setModelDesc(String modelDesc) {
        this.modelDesc = modelDesc;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }
} 