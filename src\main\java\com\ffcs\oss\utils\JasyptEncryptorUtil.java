package com.ffcs.oss.utils;

import org.jasypt.encryption.StringEncryptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * Jasypt加密工具类
 */
@Component
public class JasyptEncryptorUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JasyptEncryptorUtil.class);
    
    private static StringEncryptor stringEncryptor;
    
    @Autowired
    public JasyptEncryptorUtil(@Qualifier("jasyptStringEncryptor") StringEncryptor stringEncryptor) {
        JasyptEncryptorUtil.stringEncryptor = stringEncryptor;
    }
    
    /**
     * 使用Jasypt加密
     *
     * @param plainText 明文
     * @return 加密后的文本
     */
    public static String encrypt(String plainText) {
        if (plainText == null || plainText.isEmpty()) {
            return plainText;
        }
        
        try {
            if (stringEncryptor == null) {
                logger.warn("StringEncryptor未初始化，使用默认加密方式");
                return PasswordEncryptionUtil.encrypt(plainText);
            }
            return "ENC(" + stringEncryptor.encrypt(plainText) + ")";
        } catch (Exception e) {
            logger.error("Jasypt加密失败", e);
            // 加密失败时回退到原始加密方式
            return PasswordEncryptionUtil.encrypt(plainText);
        }
    }
    
    /**
     * 使用Jasypt解密
     *
     * @param encryptedText 加密文本
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText) {
        if (encryptedText == null || encryptedText.isEmpty()) {
            return encryptedText;
        }
        
        try {
            // 处理带ENC()前缀的密码
            String textToDecrypt = encryptedText;
            if (encryptedText.startsWith("ENC(") && encryptedText.endsWith(")")) {
                textToDecrypt = encryptedText.substring(4, encryptedText.length() - 1);
            }
            
            if (stringEncryptor == null) {
                logger.warn("StringEncryptor未初始化，使用默认解密方式");
                return PasswordEncryptionUtil.decrypt(textToDecrypt);
            }
            return stringEncryptor.decrypt(textToDecrypt);
        } catch (Exception e) {
            logger.error("Jasypt解密失败，尝试使用原始解密方式", e);
            try {
                // 处理带ENC()前缀的密码
                String textToDecrypt = encryptedText;
                if (encryptedText.startsWith("ENC(") && encryptedText.endsWith(")")) {
                    textToDecrypt = encryptedText.substring(4, encryptedText.length() - 1);
                }
                // 尝试使用原始解密方式
                return PasswordEncryptionUtil.decrypt(textToDecrypt);
            } catch (Exception ex) {
                logger.error("所有解密方式均失败", ex);
                return encryptedText;
            }
        }
    }
} 