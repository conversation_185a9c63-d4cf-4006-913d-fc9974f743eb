# 助理权限重新分配功能说明

## 功能概述

在修改助理时，如果助理的数据模型或数据源发生了变化，系统会自动为拥有该助理权限的用户重新分配相关的数据模型和数据源权限。

## 功能特性

### 1. 自动检测变化
- **数据模型变化检测**：检测助理关联的数据模型ID是否发生变化
- **数据源变化检测**：检测助理直接关联的数据源ID是否发生变化
- **智能判断**：只有在实际发生变化时才进行权限重新分配

### 2. 权限重新分配流程
1. **获取原有权限**：查找所有拥有该助理权限的用户
2. **移除旧权限**：移除用户对旧数据模型和数据源的权限
3. **分配新权限**：为用户分配新数据模型和数据源的权限
4. **保持权限类型**：新分配的权限类型与原助理权限类型保持一致

### 3. 支持的变化场景
- 数据模型从有变为无
- 数据模型从无变为有  
- 数据模型ID发生变化
- 数据源从有变为无
- 数据源从无变为有
- 数据源ID发生变化
- 数据模型和数据源同时发生变化

## 技术实现

### 核心方法

#### AiPermissionConfigService.handleAssistantUpdatePermissions()
```java
ServiceResp handleAssistantUpdatePermissions(String assistantId, AiAssistantD oldAssistant, 
                                           AiAssistantD newAssistant, String currentUserName)
```

**参数说明：**
- `assistantId`: 助理ID
- `oldAssistant`: 修改前的助理信息
- `newAssistant`: 修改后的助理信息  
- `currentUserName`: 当前操作用户

**返回值：**
- `ServiceResp`: 操作结果，包含成功/失败信息

### 调用时机

在 `AiAssistantController.addOrUpdateAssistant()` 方法中，当执行助理更新操作成功后自动调用：

```java
// 如果更新成功，处理权限重新分配
if (result) {
    ServiceResp permissionResult = permissionConfigService.handleAssistantUpdatePermissions(
        String.valueOf(assistant.getAssistantId()), 
        oldAssistant, 
        assistant, 
        currentUserName
    );
    
    // 记录权限重新分配结果
    if (!permissionResult.isSuccess()) {
        log.warn("助理更新成功，但权限重新分配失败: assistantId={}, error={}", 
                assistant.getAssistantId(), permissionResult.getBody());
    }
}
```

## 权限类型说明

### 资源类型常量
- `CommonConstant.RESOURCE_TYPE_DATA_MODEL = 1`: 数据模型
- `CommonConstant.RESOURCE_TYPE_ASSISTANT = 2`: 助理
- `DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION = 3`: 数据源

### 权限类型
- `1`: 管理员权限
- `2`: 普通用户权限

### 用户类型
- `1`: 用户
- `2`: 用户组

## 使用示例

### 场景1：修改助理的数据模型

```java
// 原助理信息
AiAssistantD oldAssistant = new AiAssistantD();
oldAssistant.setAssistantId(1L);
oldAssistant.setDataModelId("old-model-id");
oldAssistant.setConnectionId(BigInteger.valueOf(100L));

// 新助理信息
AiAssistantD newAssistant = new AiAssistantD();
newAssistant.setAssistantId(1L);
newAssistant.setDataModelId("new-model-id"); // 数据模型发生变化
newAssistant.setConnectionId(BigInteger.valueOf(100L));

// 系统会自动：
// 1. 移除用户对 "old-model-id" 的权限
// 2. 为用户分配 "new-model-id" 的权限
```

### 场景2：修改助理的数据源

```java
// 原助理信息
AiAssistantD oldAssistant = new AiAssistantD();
oldAssistant.setAssistantId(1L);
oldAssistant.setDataModelId("model-id");
oldAssistant.setConnectionId(BigInteger.valueOf(100L));

// 新助理信息  
AiAssistantD newAssistant = new AiAssistantD();
newAssistant.setAssistantId(1L);
newAssistant.setDataModelId("model-id");
newAssistant.setConnectionId(BigInteger.valueOf(200L)); // 数据源发生变化

// 系统会自动：
// 1. 移除用户对数据源 "100" 的权限
// 2. 为用户分配数据源 "200" 的权限
```

## 日志记录

系统会记录权限重新分配的详细日志：

```
INFO  - 开始处理助理修改时的权限重新分配: assistantId=1
INFO  - 助理更新成功，权限重新分配完成: assistantId=1, result=成功: 用户 testuser 权限重新分配成功
WARN  - 助理更新成功，但权限重新分配失败: assistantId=1, error=权限重新分配失败: xxx
```

## 注意事项

1. **事务性**：权限重新分配操作在事务中执行，确保数据一致性
2. **错误处理**：即使权限重新分配失败，助理更新操作仍然成功，但会记录警告日志
3. **权限检查**：只有拥有助理管理权限的用户才能修改助理
4. **性能考虑**：对于拥有大量用户权限的助理，权限重新分配可能需要一定时间

## 测试验证

项目包含了完整的单元测试和集成测试：

- `AiPermissionConfigServiceTest`: 单元测试
- `AssistantPermissionIntegrationTest`: 集成测试

可以通过运行测试来验证功能的正确性。
