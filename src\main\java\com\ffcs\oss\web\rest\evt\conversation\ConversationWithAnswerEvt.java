package com.ffcs.oss.web.rest.evt.conversation;

import com.ffcs.oss.domain.AiAnswerD;
import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 对话和回答请求参数
 */
@ApiModel("对话和回答请求参数")
public class ConversationWithAnswerEvt implements Serializable {
    
    @ApiModelProperty("会话信息")
    private ConversationEvt conversation;
    
    @ApiModelProperty("回答信息")
    private AiAnswerD answer;

    public ConversationEvt getConversation() {
        return conversation;
    }

    public void setConversation(ConversationEvt conversation) {
        this.conversation = conversation;
    }

    public AiAnswerD getAnswer() {
        return answer;
    }

    public void setAnswer(AiAnswerD answer) {
        this.answer = answer;
    }
} 