package com.ffcs.oss.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-08-08
 */

@TableName ( value ="ai_data_source_config_d" )
public class AiDataSourceConfigD  implements Serializable {

	private static final long serialVersionUID =  8970331532581147553L;

	/**
	 * 自增id
	 */
   	@TableField(value = "data_source_config_id" )
	private BigInteger dataSourceConfigId;

	/**
	 * 数源名称
	 */
   	@TableField(value = "name" )
	private String name;

	/**
	 * 数源全称
	 */
   	@TableField(value = "full_name" )
	private String fullName;

	/**
	 * 数源枚举：“CCTV”，“people”，“BBC”，“CNN”
	 */
   	@TableField(value = "key" )
	private String key;

	/**
	 * cron表达式
	 */
   	@TableField(value = "cron_expression" )
	private String cronExpression;

	/**
	 * 数源类型：1、国际 2、国内
	 */
   	@TableField(value = "type" )
	private Integer type;

	/**
	 * 是否开启：1、开启 0、关闭
	 */
   	@TableField(value = "is_on" )
	private Integer isOn;

	/**
	 * 正文
	 */
   	@TableField(value = "content" )
	private String content;

	/**
	 * 创建人
	 */
   	@TableField(value = "creator_name" )
	private String creatorName;

	/**
	 * 创建时间
	 */
   	@TableField(value = "create_time" )
	private Date createTime;

	/**
	 * 更新人
	 */
   	@TableField(value = "updater_name" )
	private String updaterName;

	/**
	 * 更新时间
	 */
   	@TableField(value = "update_time" )
	private Date updateTime;

	public BigInteger getDataSourceConfigId() {
		return dataSourceConfigId;
	}

	public void setDataSourceConfigId(BigInteger dataSourceConfigId) {
		this.dataSourceConfigId = dataSourceConfigId;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFullName() {
		return this.fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getKey() {
		return this.key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getCronExpression() {
		return this.cronExpression;
	}

	public void setCronExpression(String cronExpression) {
		this.cronExpression = cronExpression;
	}

	public Integer getType() {
		return this.type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getIsOn() {
		return this.isOn;
	}

	public void setIsOn(Integer isOn) {
		this.isOn = isOn;
	}

	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getCreatorName() {
		return this.creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdaterName() {
		return this.updaterName;
	}

	public void setUpdaterName(String updaterName) {
		this.updaterName = updaterName;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
