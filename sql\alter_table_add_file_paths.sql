-- 添加文件存储路径相关字段
ALTER TABLE ai_db_connection_config ADD COLUMN IF NOT EXISTS keytab_file_path VARCHAR(500);
ALTER TABLE ai_db_connection_config ADD COLUMN IF NOT EXISTS krb5_conf_file_path VARCHAR(500);

-- 添加注释
COMMENT ON COLUMN ai_db_connection_config.keytab_file_path IS 'Kerberos keytab文件在磁盘上的存储路径';
COMMENT ON COLUMN ai_db_connection_config.krb5_conf_file_path IS 'krb5.conf文件在磁盘上的存储路径';

ALTER TABLE ai_audit_list_d ADD COLUMN data_model_id VARCHAR(64);
COMMENT ON COLUMN ai_audit_list_d.data_model_id IS '数据模型ID';

-- 添加data_model_id字段到待办列表表
ALTER TABLE ai_todo_list_d ADD COLUMN data_model_id VARCHAR(64);
COMMENT ON COLUMN ai_todo_list_d.data_model_id IS '数据模型ID';

-- 创建索引以提高查询性能
CREATE INDEX idx_audit_data_model ON ai_audit_list_d (data_model_id);
CREATE INDEX idx_todo_data_model ON ai_todo_list_d (data_model_id);
-- 添加alias字段到数据模型表，用于存储创建人别名
ALTER TABLE ai_data_model_d ADD COLUMN alias VARCHAR(100);
COMMENT ON COLUMN ai_data_model_d.alias IS '创建人别名';
