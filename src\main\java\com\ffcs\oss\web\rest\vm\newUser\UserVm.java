package com.ffcs.oss.web.rest.vm.newUser;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2019-12-18 11:42:03
 */
@Alias("UserVm")
@ApiModel("用户")
public class UserVm implements Serializable {
    private static final long serialVersionUID = -1001429392929208102L;
    @ApiModelProperty("ID")
    private String id;
    @ApiModelProperty("名")
    private String name;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("登录名")
    private String loginName;
    @ApiModelProperty("昵称")
    private String alias;
    @ApiModelProperty("用户状态")
    private String status;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("手机号码")
    private String telephone;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("办公电话")
    private String officePhone;
    @ApiModelProperty("区域ID")
    private Long regionId;
    @ApiModelProperty("组织机构ID")
    private Long orgId;
    @ApiModelProperty("组织机构名称")
    private String orgName;
    @ApiModelProperty("性别")
    private String sex;
    @ApiModelProperty("身份证号码")
    private String pid;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("密码更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pwdUpdateTime;
    @ApiModelProperty("新增用户时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @ApiModelProperty("全量区域，用/分隔")
    private String fullRegionName;
    @ApiModelProperty("全量区域ID，用,拼接")
    private String fullRegion;
    @ApiModelProperty("工号")
    private String jobNumber;
    @ApiModelProperty(value = "用户的所属用户组id",notes = "异步用户树回填用到")
    private List<Long> userGroupIds;
    @ApiModelProperty(value = "用户的所属用户组名称",notes = "异步用户树回填用到")
    private String userGroupNames;


    @ApiModelProperty("最大在线会话数")
    private Integer maxOnlineSessionNum = 1;
    @ApiModelProperty("账号有效策略")
    private Integer accountEffective = 180;
    @ApiModelProperty("密码有效策略")
    private Integer pwdEffective = 90;
    @ApiModelProperty("是否必须修改密码")
    private String isMustUpdPwd;
    @ApiModelProperty("账号锁定策略 天数")
    private Integer accountLockout = 90;
    @ApiModelProperty("账号停用（注销）策略 天数")
    private Integer accountDisabled = 180;
    @ApiModelProperty("密码过期提示天数")
    private Integer pwdPromptDays = 14;
    @ApiModelProperty("系统风格类型")
    private Integer systemStyleType = 0;

    @ApiModelProperty("拓展1 - 单位")
    private String arg1;
    @ApiModelProperty("拓展2 - 岗位")
    private String arg2;
    @ApiModelProperty("拓展3")
    private String arg3;
    @ApiModelProperty("拓展3")
    private String arg4;

    @ApiModelProperty("keyName")
    private String keyName;

    @ApiModelProperty("全量用户组")
    private String groupFullName;
    @ApiModelProperty("全量用户组ID，前后用,拼接")
    private String fullGroupId;

    @ApiModelProperty("租户编码")
    private String tenantCode;

    @ApiModelProperty("侧边栏")
    private Integer sideBar;
    @ApiModelProperty("顶边栏")
    private Integer topBar;

    public Integer getSideBar() {
        return sideBar;
    }

    public void setSideBar(Integer sideBar) {
        this.sideBar = sideBar;
    }

    public Integer getTopBar() {
        return topBar;
    }

    public void setTopBar(Integer topBar) {
        this.topBar = topBar;
    }

    public String getTenantCode() {
        return null;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }


    public String getGroupFullName() {
        return groupFullName;
    }

    public void setGroupFullName(String groupFullName) {
        this.groupFullName = groupFullName;
    }

    public String getFullGroupId() {
        return fullGroupId;
    }

    public void setFullGroupId(String fullGroupId) {
        this.fullGroupId = fullGroupId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }



    @Override
    public String toString() {
        return "UserVm{" +
            "userId=" + userId +
            ", loginName='" + loginName + '\'' +
            ", alias='" + alias + '\'' +
            ", status='" + status + '\'' +
            ", userType='" + userType + '\'' +
            ", telephone='" + telephone + '\'' +
            ", email='" + email + '\'' +
            ", officePhone='" + officePhone + '\'' +
            ", regionId=" + regionId +
            ", orgId=" + orgId +
            ", orgName='" + orgName + '\'' +
            ", sex='" + sex + '\'' +
            ", pid='" + pid + '\'' +
            ", description='" + description + '\'' +
            ", pwdUpdateTime=" + pwdUpdateTime +
            ", createTime=" + createTime +
            ", fullRegionName='" + fullRegionName + '\'' +
            ", fullRegion='" + fullRegion + '\'' +
            ", jobNumber='" + jobNumber + '\'' +
            ", userGroupIds=" + userGroupIds +
            ", userGroupNames='" + userGroupNames + '\'' +
            ", maxOnlineSessionNum=" + maxOnlineSessionNum +
            ", accountEffective=" + accountEffective +
            ", pwdEffective=" + pwdEffective +
            ", isMustUpdPwd='" + isMustUpdPwd + '\'' +
            ", accountLockout=" + accountLockout +
            ", accountDisabled=" + accountDisabled +
            ", pwdPromptDays=" + pwdPromptDays +
            ", systemStyleType=" + systemStyleType +
            ", arg1='" + arg1 + '\'' +
            ", arg2='" + arg2 + '\'' +
            ", arg3='" + arg3 + '\'' +
            ", arg4='" + arg4 + '\'' +
            '}';
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getArg1() {
        return arg1;
    }

    public void setArg1(String arg1) {
        this.arg1 = arg1;
    }

    public String getArg2() {
        return arg2;
    }

    public void setArg2(String arg2) {
        this.arg2 = arg2;
    }

    public String getArg3() {
        return arg3;
    }

    public void setArg3(String arg3) {
        this.arg3 = arg3;
    }

    public String getArg4() {
        return arg4;
    }

    public void setArg4(String arg4) {
        this.arg4 = arg4;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getOfficePhone() {
        return officePhone;
    }

    public void setOfficePhone(String officePhone) {
        this.officePhone = officePhone;
    }

    public Long getRegionId() {
        return regionId;
    }

    public void setRegionId(Long regionId) {
        this.regionId = regionId;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getPwdUpdateTime() {
        return pwdUpdateTime;
    }

    public void setPwdUpdateTime(Date pwdUpdateTime) {
        this.pwdUpdateTime = pwdUpdateTime;
    }

    public String getFullRegionName() {
        return fullRegionName;
    }

    public void setFullRegionName(String fullRegionName) {
        this.fullRegionName = fullRegionName;
    }

    public String getFullRegion() {
        return fullRegion;
    }

    public void setFullRegion(String fullRegion) {
        this.fullRegion = fullRegion;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public List<Long> getUserGroupIds() {
        return userGroupIds;
    }

    public void setUserGroupIds(List<Long> userGroupIds) {
        this.userGroupIds = userGroupIds;
    }

    public String getUserGroupNames() {
        return userGroupNames;
    }

    public void setUserGroupNames(String userGroupNames) {
        this.userGroupNames = userGroupNames;
    }

    public Integer getMaxOnlineSessionNum() {
        return maxOnlineSessionNum;
    }

    public void setMaxOnlineSessionNum(Integer maxOnlineSessionNum) {
        this.maxOnlineSessionNum = maxOnlineSessionNum;
    }

    public Integer getAccountEffective() {
        return accountEffective;
    }

    public void setAccountEffective(Integer accountEffective) {
        this.accountEffective = accountEffective;
    }

    public Integer getPwdEffective() {
        return pwdEffective;
    }

    public void setPwdEffective(Integer pwdEffective) {
        this.pwdEffective = pwdEffective;
    }

    public String getIsMustUpdPwd() {
        return isMustUpdPwd;
    }

    public void setIsMustUpdPwd(String isMustUpdPwd) {
        this.isMustUpdPwd = isMustUpdPwd;
    }

    public Integer getAccountLockout() {
        return accountLockout;
    }

    public void setAccountLockout(Integer accountLockout) {
        this.accountLockout = accountLockout;
    }

    public Integer getAccountDisabled() {
        return accountDisabled;
    }

    public void setAccountDisabled(Integer accountDisabled) {
        this.accountDisabled = accountDisabled;
    }

    public Integer getPwdPromptDays() {
        return pwdPromptDays;
    }

    public void setPwdPromptDays(Integer pwdPromptDays) {
        this.pwdPromptDays = pwdPromptDays;
    }

    public Integer getSystemStyleType() {
        return systemStyleType;
    }

    public void setSystemStyleType(Integer systemStyleType) {
        this.systemStyleType = systemStyleType;
    }
}
