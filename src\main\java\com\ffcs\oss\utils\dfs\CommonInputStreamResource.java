package com.ffcs.oss.utils.dfs;

import org.springframework.core.io.InputStreamResource;

import java.io.IOException;
import java.io.InputStream;

/**
 * 通用InputStreamResource实现类
 * 重写了getFilename方法，方便在HTTP上传时设置文件名
 */
public class CommonInputStreamResource extends InputStreamResource {
    
    /**
     * 文件名
     */
    private final String filename;

    /**
     * 构造函数
     * 
     * @param inputStream 输入流
     * @param filename 文件名
     */
    public CommonInputStreamResource(InputStream inputStream, String filename) {
        super(inputStream);
        this.filename = filename;
    }

    /**
     * 获取文件名
     * 
     * @return 文件名
     */
    @Override
    public String getFilename() {
        return this.filename;
    }

    /**
     * 是否打开文件
     * 
     * @return 总是返回true，表示已打开
     */
    @Override
    public boolean isOpen() {
        return true;
    }
} 