package com.ffcs.oss.web.rest.vm.sqlexec;

import java.util.List;
import java.util.Map;

/**
 * SQL执行结果响应模型
 */
public class SqlExecutionResultVm {
    
    /**
     * 列信息
     */
    private List<ColumnInfo> columns;
    
    /**
     * 数据行
     */
    private List<Map<String, Object>> rows;
    
    /**
     * 影响的行数（适用于更新、插入、删除操作）
     */
    private Integer affectedRows;
    
    /**
     * 执行状态：成功/失败
     */
    private Boolean success;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long executionTime;
    
    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 列信息类
     */
    public static class ColumnInfo {
        /**
         * 列名
         */
        private String name;
        
        /**
         * 列类型
         */
        private String type;
        
        public ColumnInfo() {
        }
        
        public ColumnInfo(String name, String type) {
            this.name = name;
            this.type = type;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
    }

    public List<ColumnInfo> getColumns() {
        return columns;
    }

    public void setColumns(List<ColumnInfo> columns) {
        this.columns = columns;
    }

    public List<Map<String, Object>> getRows() {
        return rows;
    }

    public void setRows(List<Map<String, Object>> rows) {
        this.rows = rows;
    }

    public Integer getAffectedRows() {
        return affectedRows;
    }

    public void setAffectedRows(Integer affectedRows) {
        this.affectedRows = affectedRows;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
} 