package com.ffcs.oss.config.i18n;


import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.util.ResourceUtils;
import org.springframework.web.servlet.LocaleResolver;

import java.io.File;


/**
 * @ClassName SpringConfig
 * @Description Spring配置
 * <AUTHOR>
 * @Date 2022/8/23 11:30
 */
@Configuration
public class MessageSpringConfig {

    private static Logger log = LoggerFactory.getLogger(MessageSpringConfig.class);


    @Autowired
    private MessageConfig messageConfig;

    @Bean
    @Primary
    public LocaleResolver localeResolver() {
        return new MessageLocaleResolver();
    }

    @Primary
    @Bean(name = "messageSource")
    @DependsOn(value = "messageConfig")
    public ReloadableResourceBundleMessageSource messageSource() {
        String path = null;
        if (!messageConfig.getLocalfile()) {
            String baseName = StringUtils.isNotBlank(messageConfig.getBasename()) ? messageConfig.getBasename() : "basename";
            path = ResourceUtils.FILE_URL_PREFIX + System.getProperty("user.dir") + File.separator + File.separator + baseName;
            log.info("Nacos国际化配置本地缓存路径:{}", path);
        } else {
            path = messageConfig.getBasename();
            if (!path.contains("classpath")) {
                path = "classpath:" + path;
            }
            log.info("国际化配置本地路径:{}", path);
        }
        if (StringUtils.isBlank(path)) {
            path = "classpath:i18n/messages";
            log.info("国际化未配置，使用默认路径:{}", path);
        }
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename(path);
        messageSource.setDefaultEncoding(messageConfig.getEncoding());
        messageSource.setCacheMillis(messageConfig.getCacheMillis());
        return messageSource;
    }


}
