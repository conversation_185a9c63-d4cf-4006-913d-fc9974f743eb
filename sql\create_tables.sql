-- 创建收藏表
CREATE TABLE IF NOT EXISTS ai_collection_d (
    collection_id VARCHAR(32) PRIMARY KEY,
    assistant_id BIGINT NOT NULL,
    question_id VARCHAR(32) NOT NULL,
    answer_id VARCHAR(32) NOT NULL,
    create_user_name VARCHAR(50),
    create_time TIMESTAMP,
    update_user_name VARCHAR(50),
    update_time TIMESTAMP,
    title VARCHAR(500),
    description TEXT
);

-- 创建分享表
CREATE TABLE IF NOT EXISTS ai_share_d (
    share_id VARCHAR(32) PRIMARY KEY,
    share_link VARCHAR(500) NOT NULL,
    question_id VARCHAR(32) NOT NULL,
    answer_id VARCHAR(32) NOT NULL,
    create_user_name VARCHAR(50),
    create_time TIMESTAMP,
    update_user_name VARCHAR(50),
    update_time TIMESTAMP,
    visit_count INTEGER DEFAULT 0,
    expire_time TIMESTAMP
);

-- 修改助理表，添加多轮对话字段
ALTER TABLE ai_assistant_d ADD COLUMN IF NOT EXISTS enable_multi_turn VARCHAR(2) DEFAULT '0';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_collection_assistant_id ON ai_collection_d (assistant_id);
CREATE INDEX IF NOT EXISTS idx_collection_question_answer ON ai_collection_d (question_id, answer_id);
CREATE INDEX IF NOT EXISTS idx_share_question_answer ON ai_share_d (question_id, answer_id);

ALTER TABLE ai_db_connection_config ADD COLUMN IF NOT EXISTS url VARCHAR(1024);