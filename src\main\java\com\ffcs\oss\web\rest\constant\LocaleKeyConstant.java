package com.ffcs.oss.web.rest.constant;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @date 2020/1/3 10:34
 */
@ApiModel(description = "国际化-对应key相关统一管理")
public class LocaleKeyConstant {

    /*-------------------日志相关------------------*/
    /**
     * 函数名【{}】入参信息【{}】
     */
    public static final String LOG_OUTPUT_PARAM = "log.output.param";
    /**
     * 函数名【{}】入参信息【{}】异常信息:
     */
    public static final String LOG_OUTPUT_EXCEP = "log.output.excep";

    /*-------------------枚举相关------------------*/
    /**
     * 否
     */
    public static final String ENUM_YESNO_NO = "enum.yesno.no";
    /**
     * 是
     */
    public static final String ENUM_YESNO_YES = "enum.yesno.yes";

    /**
     * 启用
     */
    public static final String ENUM_STATUS_ENABLED = "enum.status.enabled";
    /**
     * 禁用
     */
    public static final String ENUM_STATUS_DISABLED = "enum.status.disabled";


    /*------------------通用相关------------------*/
    /**
     * 操作成功
     */
    public static final String OPERATE_SUCCESS = "operate.success";
    /**
     * 操作异常
     */
    public static final String OPERATE_EXCEPTION = "operate.exception";
    /**
     * 操作失败
     */
    public static final String OPERATE_FAIL = "operate.fail";
    /**
     * 查询成功
     */
    public static final String QUERY_SUCCESS = "query.success";
    /**
     * 查询异常
     */
    public static final String QUERY_EXCEPTION = "query.exception";

    /**
     * {0}不能为空
     */
    public static final String PLEASE_UPLOAD_THE_NECESSARY_PARAMETERS = "please.upload.the.necessary.parameters";

    /**
     * 无此预案
     */
    public static final String NO_SUCH_RULE = "no.such.rule";


    /**
     *
     */
    public static final String NOT_CREATOR_ADMINISTRATOR_WITHOUT_PERMISSION = "not.creator.administrator.without.permission";

    /**
     * 该手机号已添加不能重复添加
     */
    public static final String mobile_number_added_cannot_added_repeatedly = "mobile.number.added.cannot.added.repeatedly";

    /**
     * 之前已添加
     */
    public static final String previously_added = "previously.added";

    /**
     * {0}不能为空
     */
    public static final String MATCH_WORDS_CANNOT_REPEATED = "match.words.cannot.repeated";
    /**
     * 参数不能为空
     */
    public static final String PARAMS_CANNOT_NULL="params.cannot.null";
    /**
     * 规则标题不能重复
     */
    public static final String RULE_TITLE_CANNOT_REPEATED = "rule.title.cannot.repeated";
    /**
     * 操作0条记录
     */
    public static final String OPERATE_ZERO_RECORDS="operate.zero.records";

    /**
     * 名称不能重复
     */
    public static final String NAME_CANNOT_REPEATED = "name.cannot.repeated";

    /**
     * 校验发生异常
     */
    public static final String CHECK_EXCEPTION = "check.exception";
    /**
     * 无权限
     */
    public static final String NO_PERMISSION="no.permission";
}
