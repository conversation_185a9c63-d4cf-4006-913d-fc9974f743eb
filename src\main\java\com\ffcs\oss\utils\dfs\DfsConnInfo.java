package com.ffcs.oss.utils.dfs;

/**
 * DFS连接信息
 */
public class DfsConnInfo {
    /**
     * 服务器地址
     */
    private String host;
    
    /**
     * 端口
     */
    private int port;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * REST端口
     */
    private int restPort;
    
    /**
     * REST密码
     */
    private String restPassword;
    
    /**
     * 时间差
     */
    private Integer timeDiff = 2000;
    
    /**
     * 方案
     */
    private String scheme;
    
    /**
     * 点
     */
    private String point;
    
    /**
     * 类型
     */
    private String type;
    
    /**
     * 加密方式
     */
    private String encryption;
    
    /**
     * 基础URL
     */
    private String baseUrl;
    
    /**
     * MinIO访问密钥
     */
    private String accessKey;
    
    /**
     * MinIO安全密钥
     */
    private String secretKey;
    
    /**
     * MinIO桶名称
     */
    private String bucketName;
    
    /**
     * MinIO端点
     */
    private String endpoint;
    
    /**
     * 字符集
     */
    private String charset = "UTF-8";
    
    // Getters and Setters
    
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public int getRestPort() {
        return restPort;
    }
    
    public void setRestPort(int restPort) {
        this.restPort = restPort;
    }
    
    public String getRestPassword() {
        return restPassword;
    }
    
    public void setRestPassword(String restPassword) {
        this.restPassword = restPassword;
    }
    
    public Integer getTimeDiff() {
        return timeDiff;
    }
    
    public void setTimeDiff(Integer timeDiff) {
        this.timeDiff = timeDiff;
    }
    
    public String getScheme() {
        return scheme;
    }
    
    public void setScheme(String scheme) {
        this.scheme = scheme;
    }
    
    public String getPoint() {
        return point;
    }
    
    public void setPoint(String point) {
        this.point = point;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getEncryption() {
        return encryption;
    }
    
    public void setEncryption(String encryption) {
        this.encryption = encryption;
    }
    
    public String getBaseUrl() {
        return baseUrl;
    }
    
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    public String getAccessKey() {
        return accessKey;
    }
    
    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }
    
    public String getSecretKey() {
        return secretKey;
    }
    
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }
    
    public String getBucketName() {
        return bucketName;
    }
    
    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    public String getCharset() {
        return charset;
    }
    
    public void setCharset(String charset) {
        this.charset = charset;
    }
    
    @Override
    public String toString() {
        return "[" + host + ":" + port + "]";
    }
} 