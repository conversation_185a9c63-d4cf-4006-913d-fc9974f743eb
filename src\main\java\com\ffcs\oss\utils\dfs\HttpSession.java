package com.ffcs.oss.utils.dfs;

import com.alibaba.fastjson.JSON;
import com.ffcs.oss.common.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * HTTP会话实现类
 */
public class HttpSession implements DfsSession {

    private static final Logger log = LoggerFactory.getLogger(HttpSession.class);

    /**
     * 连接信息
     */
    private final DfsConnInfo connInfo;

    private boolean connected = false;

    public HttpSession(DfsConnInfo connInfo) {
        this.connInfo = connInfo;
    }

    @Override
    public void connect() {
        String httpUrl = connInfo.getScheme() + "://" + connInfo.getHost() + ":" 
                + connInfo.getRestPort() + "/" + connInfo.getPoint();
        log.info("连接HTTP服务: {}", httpUrl);
        connected = true;
    }

    @Override
    public void destroy() {
        // HTTP方式不需要关闭连接
    }

    @Override
    public boolean exists(String filename) {
        // 简单实现，可以通过HEAD请求检查文件是否存在
        return true;
    }

    @Override
    public void downloadFile(String remoteFilename, File localFile) {
        try {
            if (!StringUtils.hasText(remoteFilename)) {
                return;
            }
            ResponseEntity<Resource> response = RestTemplateUtil.get(
                    HttpUrlUtils.getFileUrl(connInfo.getHost(), connInfo.getRestPort(), connInfo.getScheme(), connInfo.getPoint()) + remoteFilename,
                    getHeaders(),
                    Resource.class);

            // 获取输入流并写入到本地文件
            try (InputStream in = Objects.requireNonNull(response.getBody()).getInputStream();
                 FileOutputStream out = new FileOutputStream(localFile)) {
                byte[] buffer = new byte[1024];
                int length;
                while ((length = in.read(buffer)) > 0) {
                    out.write(buffer, 0, length);
                }
            }
        } catch (IOException | NullPointerException e) {
            throw new BusinessException("下载文件失败: " + remoteFilename + ", " + e.getMessage());
        }
    }

    @Override
    public InputStream downloadStream(String remoteFilename) {
        try {
            if (!StringUtils.hasText(remoteFilename)) {
                return null;
            }
            ResponseEntity<Resource> response = RestTemplateUtil.get(
                    HttpUrlUtils.getFileUrl(connInfo.getHost(), connInfo.getRestPort(), connInfo.getScheme(), connInfo.getPoint()) + remoteFilename,
                    getHeaders(),
                    Resource.class);
            return response.getBody().getInputStream();
        } catch (IOException e) {
            throw new BusinessException("下载文件流失败: " + remoteFilename + ", " + e.getMessage());
        }
    }

    @Override
    public void uploadFile(File localFile, String remoteFilename) {
        // 未实现，可以根据需要添加
    }

    @Override
    public Boolean uploadFile(String remoteFilename, InputStream inputStream) {
        try {
            if (!StringUtils.hasText(remoteFilename)) {
                return false;
            }
            log.info("上传文件名:{}", remoteFilename);
            String fileName = FileUtil.getFileSimpleName(remoteFilename);
            String fileDir = FileUtil.getParentPath(remoteFilename);

            // 将输入流转换为字节数组，以便可以重复读取
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byte[] fileContent = byteArrayOutputStream.toByteArray();

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            CommonByteArrayResource resource = new CommonByteArrayResource(fileContent, fileName);
            body.add("file", resource);
            
            ResponseEntity<HttpResultVm> response = RestTemplateUtil.post(
                    HttpUrlUtils.getFileUrl(connInfo.getHost(), connInfo.getRestPort(), connInfo.getScheme(), connInfo.getPoint()) + fileDir,
                    getHeaders(),
                    body,
                    HttpResultVm.class);
            log.info("上传文件回参:{}", JSON.toJSONString(response.getBody()));
            return response.getBody().getCode() == 0;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new BusinessException("上传文件失败: " + e.getMessage());
        }
    }

    @Override
    public void deleteFile(String filename) {
        if (!StringUtils.hasText(filename)) {
            return;
        }
        ResponseEntity<HttpResultVm> response = RestTemplateUtil.delete(
                HttpUrlUtils.getFileUrl(connInfo.getHost(), connInfo.getRestPort(), connInfo.getScheme(), connInfo.getPoint()) + filename,
                getHeaders(),
                HttpResultVm.class);
        log.info("删除文件回参:{}", JSON.toJSONString(response.getBody()));
    }

    @Override
    public void mkDirs(String dir) {
        // HTTP方式通常不需要显式创建目录
    }

    /**
     * 获取HTTP请求头
     *
     * @return HTTP请求头
     */
    private HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.add("fileName", "file");
        return headers;
    }

    /**
     * 文件工具类
     */
    public static class FileUtil {
        /**
         * 获取文件简单名称
         *
         * @param filePath 文件路径
         * @return 文件名称
         */
        public static String getFileSimpleName(String filePath) {
            if (!StringUtils.hasText(filePath)) {
                return "";
            }
            int index = filePath.lastIndexOf("/");
            return index != -1 ? filePath.substring(index + 1) : filePath;
        }

        /**
         * 获取父级路径
         *
         * @param filePath 文件路径
         * @return 父级路径
         */
        public static String getParentPath(String filePath) {
            if (!StringUtils.hasText(filePath)) {
                return "";
            }
            int index = filePath.lastIndexOf("/");
            return index != -1 ? filePath.substring(0, index + 1) : "/";
        }
    }
} 