package com.ffcs.oss.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 会话回答
 */
@Data
@TableName("ai_answer_d")
public class AiAnswerD {
    
    /**
     * 主键id
     */
    @TableId(value = "answer_id",type = IdType.UUID)
    private String answerId;
    
    /**
     * sql查询
     */
    @TableField("sql")
    private String sql;
    
    /**
     * 数据查询
     */
    @TableField("data_search")
    private String dataSearch;
    
    /**
     * 结果
     */
    @TableField("result")
    private String result;
    
    /**
     * 创建人
     */
    @TableField("create_user_name")
    private String createUserName;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField("update_user_name")
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 会话id
     */
    @TableField("session_id")
    private String sessionId;
    
    /**
     * 提问id
     */
    @TableField("question_id")
    private String questionId;
    
    /**
     * 1点赞|2点踩|0无
     */
    @TableField("like_or_no_like_result")
    private String likeOrNoLikeResult;

    /**
     * 图表数据
     */
    @TableField("extra_data")
    private String extraData;
    /**
     * sql状态
     */
    @TableField("sql_status")
    private String sqlStatus;
    /**
     * 数据查询状态
     */
    @TableField("data_search_status")
    private String dataSearchStatus;
    /**
     * 结果状态
     */
    @TableField("result_status")
    private String resultStatus;
} 