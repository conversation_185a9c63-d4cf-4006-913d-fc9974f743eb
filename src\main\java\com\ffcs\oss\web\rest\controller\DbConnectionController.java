package com.ffcs.oss.web.rest.controller;

import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiDbConnectionConfigD;
import com.ffcs.oss.mapper.dbconnection.DbConnectionConfigMapper;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.service.AiPermissionConfigService;
import com.ffcs.oss.service.DbConnectionService;
import com.ffcs.oss.service.UserService;
import com.ffcs.oss.utils.hive.HiveConnectionClient;
import com.ffcs.oss.web.rest.constant.DbConnectionConstant;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.dbconnection.DbConnectionConfigEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Map;

/**
 * 数据库连接配置控制器
 */
@RestController
@RequestMapping(value = "/api/dbConnection")
@ApiModel("数据库连接管理")
public class DbConnectionController {
    
    private static final Logger logger = LoggerFactory.getLogger(DbConnectionController.class);
    
    private final DbConnectionService dbConnectionService;
    private final AiPermissionConfigService permissionConfigService;

    private final UserService userService;
    
    @Resource
    private DbConnectionConfigMapper dbConnectionConfigMapper;
    
    public DbConnectionController(DbConnectionService dbConnectionService, AiPermissionConfigService permissionConfigService, UserService userService) {
        this.dbConnectionService = dbConnectionService;
        this.permissionConfigService = permissionConfigService;
        this.userService = userService;
    }
    
    /**
     * 获取数据库连接配置列表
     */
    @ApiOperation("获取数据库连接配置列表")
    @PostMapping("/getDbConnectionList")
    public ServiceResp getDbConnectionList(@RequestBody DbConnectionConfigEvt evt) {
        return ServiceResp.getInstance().success(
                dbConnectionService.getDbConnectionList(evt),
                LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS)
        );
    }
    
    /**
     * 新增或修改数据库连接配置
     */
    @ApiOperation("新增或修改数据库连接配置")
    @PostMapping("/addOrUpdateDbConnection")
    public ServiceResp addOrUpdateDbConnection(@RequestBody @Validated DbConnectionConfigEvt evt) {
        // 如果提供了URL，解析URL并填充相关字段
        if (evt.getUrl() != null && !evt.getUrl().isEmpty()) {
            com.ffcs.oss.utils.DbUrlParser.parseUrl(evt.getUrl(), evt);
        }
        
        // 如果是修改，检查权限
        if (evt.getConnectionId() != null) {
            // 检查用户是否有管理权限
            if (!permissionConfigService.hasAdminPermission(
                    DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION, 
                    evt.getConnectionId().toString(), 
                    evt.getCreatorName())) {
                return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.NO_PERMISSION));
            }
        }
        
        ServiceResp resp = dbConnectionService.addOrUpdateDbConnection(evt);
        
        // 如果是新增且成功，为创建者添加管理权限
        if (evt.getConnectionId() == null && resp.isSuccess() && resp.getBody() != null) {
            String resourceId = resp.getBody().toString();
            permissionConfigService.addCreatorAdminPermission(
                    DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION, 
                    resourceId, 
                    evt.getCreatorName()
            );
        }
        
        return resp;
    }
    
    /**
     * 新增或修改数据库连接配置（支持文件上传）
     */
    @ApiOperation("新增或修改数据库连接配置（支持文件上传）")
    @PostMapping("/addOrUpdateDbConnectionWithFiles")
    public ServiceResp addOrUpdateDbConnectionWithFiles(
            @RequestParam(value = "connectionId", required = false) BigInteger connectionId,
            @RequestParam("connectionName") String connectionName,
            @RequestParam(value = "dbType", required = false) String dbType,
            @RequestParam(value = "dbVersion", required = false) String dbVersion,
            @RequestParam(value = "authType", required = false) String authType,
            @RequestParam(value = "host", required = false) String host,
            @RequestParam(value = "port", required = false) Integer port,
            @RequestParam(value = "databaseName", required = false) String databaseName,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "password", required = false) String password,
            @RequestParam(value = "principal", required = false) String principal,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "params", required = false) String params,
            @RequestParam(value = "creatorName", required = false) String creatorName,
            @RequestParam(value = "keytabFile", required = false) MultipartFile keytabFile,
            @RequestParam(value = "krb5ConfFile", required = false) MultipartFile krb5ConfFile,
            @RequestParam(value = "url", required = false) String url) {
        
        try {
            // 构建事件对象
            DbConnectionConfigEvt evt = new DbConnectionConfigEvt();
            evt.setConnectionId(connectionId);
            evt.setConnectionName(connectionName);
            evt.setDbType(dbType);
            evt.setDbVersion(dbVersion);
            evt.setAuthType(authType);
            evt.setHost(host);
            evt.setPort(port);
            evt.setDatabaseName(databaseName);
            evt.setUsername(username);
            evt.setPassword(password);
            evt.setPrincipal(principal);
            evt.setDescription(description);
            evt.setParams(params);
            evt.setCreatorName(userService.getCurrentUserName());
            evt.setKeytabFile(keytabFile);
            evt.setKrb5ConfFile(krb5ConfFile);
            evt.setUrl(url);
            
            // 如果提供了URL，解析URL并填充相关字段
            if (url != null && !url.isEmpty()) {
                com.ffcs.oss.utils.DbUrlParser.parseUrl(url, evt);
                // 二次检查host字段，确保它已被设置
                if (evt.getHost() == null || evt.getHost().isEmpty()) {
                    return ServiceResp.getInstance().error("无法从URL中解析主机地址，请检查URL格式或手动指定主机地址");
                }
            }
            
            // 如果是Kerberos认证，并且username为空，设置默认值
            if (DbConnectionConstant.AUTH_TYPE_KERBEROS.equals(evt.getAuthType()) && 
                    (evt.getUsername() == null || evt.getUsername().isEmpty())) {
                // 从principal中提取用户名部分作为默认username
                if (evt.getPrincipal() != null && !evt.getPrincipal().isEmpty()) {
                    String[] parts = evt.getPrincipal().split("@")[0].split("/");
                    evt.setUsername(parts[parts.length > 1 ? 0 : 0]);
                    logger.debug("从principal({})中提取用户名: {}", evt.getPrincipal(), evt.getUsername());
                } else {
                    // 如果没有principal，使用默认用户名
                    evt.setUsername("hiveuser");
                    logger.debug("设置默认用户名: {}", evt.getUsername());
                }
            }
            
            // 确保username不为空，因为数据库字段有非空约束
            if (evt.getUsername() == null || evt.getUsername().isEmpty()) {
                evt.setUsername("default");
                logger.debug("设置默认用户名: default");
            }
            
            // 确保password不为空，因为数据库字段有非空约束
            if (evt.getPassword() == null || evt.getPassword().isEmpty()) {
                evt.setPassword("default_password");
                logger.debug("设置默认密码");
            }
            
            // 如果是修改，检查权限
            if (connectionId != null) {
                // 检查用户是否有管理权限
                if (!permissionConfigService.hasAdminPermission(
                        DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION, 
                        connectionId.toString(), 
                        userService.getCurrentUserName())) {
                    return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.NO_PERMISSION));
                }
            }
            
            ServiceResp resp = dbConnectionService.addOrUpdateDbConnection(evt);
            
            // 如果是新增且成功，为创建者添加管理权限
            if (evt.getConnectionId() == null && resp.isSuccess() && resp.getBody() != null) {
                String resourceId = resp.getBody().toString();
                permissionConfigService.addCreatorAdminPermission(
                        DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION, 
                        resourceId,
                        userService.getCurrentUserName()
                );
            }
            
            return resp;
        } catch (Exception e) {
            // 记录详细异常日志
            logger.error("添加或更新数据库连接时发生异常", e);
            return ServiceResp.getInstance().error("处理数据库连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除数据库连接配置
     */
    @ApiOperation("删除数据库连接配置")
    @PostMapping("/deleteDbConnection")
    public ServiceResp deleteDbConnection(@RequestBody @Validated DbConnectionConfigEvt evt) {
        // 检查用户是否有管理权限
        if (!permissionConfigService.hasAdminPermission(
                DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION, 
                evt.getConnectionId().toString(), 
                evt.getCreatorName())) {
            return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.NO_PERMISSION));
        }
        
        return dbConnectionService.deleteDbConnection(evt);
    }
    
    /**
     * 测试数据库连接
     */
    @ApiOperation("测试数据库连接")
    @PostMapping("/testDbConnection")
    public ServiceResp testDbConnection(@RequestBody @Validated DbConnectionConfigEvt evt) {
        // 如果提供了URL，解析URL并填充相关字段
        if (evt.getUrl() != null && !evt.getUrl().isEmpty()) {
            com.ffcs.oss.utils.DbUrlParser.parseUrl(evt.getUrl(), evt);
        }
        
        // 如果是修改现有连接，检查权限
        if (evt.getConnectionId() != null) {
            // 检查用户是否有使用权限
            if (!permissionConfigService.hasUsePermission(
                    DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION, 
                    evt.getConnectionId().toString(), 
                    evt.getCreatorName())) {
                return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.NO_PERMISSION));
            }
        }
        
        return dbConnectionService.testDbConnection(evt);
    }
    
    /**
     * 测试数据库连接（支持文件上传）
     */
    @ApiOperation("测试数据库连接（支持文件上传）")
    @PostMapping("/testDbConnectionWithFiles")
    public ServiceResp testDbConnectionWithFiles(
            @RequestParam(value = "connectionId", required = false) BigInteger connectionId,
            @RequestParam(value = "dbType", required = false) String dbType,
            @RequestParam(value = "dbVersion", required = false) String dbVersion,
            @RequestParam(value = "authType", required = false) String authType,
            @RequestParam(value = "host", required = false) String host,
            @RequestParam(value = "port", required = false) Integer port,
            @RequestParam(value = "databaseName", required = false) String databaseName,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "password", required = false) String password,
            @RequestParam(value = "principal", required = false) String principal,
            @RequestParam(value = "params", required = false) String params,
            @RequestParam(value = "creatorName", required = false) String creatorName,
            @RequestParam(value = "keytabFile", required = false) MultipartFile keytabFile,
            @RequestParam(value = "krb5ConfFile", required = false) MultipartFile krb5ConfFile,
            @RequestParam(value = "url", required = false) String url) {
        
        try {
            // 如果是修改现有连接，检查权限
            if (connectionId != null) {
                // 检查用户是否有使用权限
                if (!permissionConfigService.hasUsePermission(
                        DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION, 
                        connectionId.toString(),
                        userService.getCurrentUserName())) {
                    return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.NO_PERMISSION));
                }
            }
            
            // 如果是Hive连接且使用Kerberos认证，使用HiveConnectionClient
            if (DbConnectionConstant.DB_TYPE_HIVE.equals(dbType) && 
                    DbConnectionConstant.AUTH_TYPE_KERBEROS.equals(authType)) {
                
                try {
                    // 如果没有上传keytabFile和krb5ConfFile文件，但有connectionId，则查找已有的文件路径
                    String keytabFilePath = null;
                    String krb5ConfFilePath = null;
                    
                    if (connectionId != null) {
                        // 查询数据库获取连接配置
                        AiDbConnectionConfigD config = dbConnectionConfigMapper.selectById(connectionId);
                        if (config != null) {
                            // 如果没有上传keytab文件，使用已有的文件路径
                            if (keytabFile == null || keytabFile.isEmpty()) {
                                keytabFilePath = config.getKeytabFilePath();
                                if (keytabFilePath == null || keytabFilePath.isEmpty()) {
                                    logger.warn("数据库中没有保存keytab文件路径，connectionId={}", connectionId);
                                } else {
                                    logger.info("使用已有的keytab文件路径: {}", keytabFilePath);
                                }
                            }
                            
                            // 如果没有上传krb5.conf文件，使用已有的文件路径
                            if (krb5ConfFile == null || krb5ConfFile.isEmpty()) {
                                krb5ConfFilePath = config.getKrb5ConfFilePath();
                                if (krb5ConfFilePath == null || krb5ConfFilePath.isEmpty()) {
                                    logger.warn("数据库中没有保存krb5.conf文件路径，connectionId={}", connectionId);
                                } else {
                                    logger.info("使用已有的krb5.conf文件路径: {}", krb5ConfFilePath);
                                }
                            }
                        } else {
                            logger.warn("未找到指定的数据库连接配置，connectionId={}", connectionId);
                        }
                    }
                    
                    // 检查Kerberos认证所需的文件是否可用
                    if ((keytabFile == null || keytabFile.isEmpty()) && (keytabFilePath == null || keytabFilePath.isEmpty())) {
                        return ServiceResp.getInstance().error("Kerberos认证需要keytab文件，请上传或确保数据库中已保存文件路径");
                    }
                    
                    if ((krb5ConfFile == null || krb5ConfFile.isEmpty()) && (krb5ConfFilePath == null || krb5ConfFilePath.isEmpty())) {
                        return ServiceResp.getInstance().error("Kerberos认证需要krb5.conf文件，请上传或确保数据库中已保存文件路径");
                    }
                    
                    boolean connected = HiveConnectionClient.testConnection(
                            url,
                            username,
                            password,
                            principal,
                            keytabFile,
                            krb5ConfFile,
                            keytabFilePath,
                            krb5ConfFilePath
                    );
                    
                    if (connected) {
                        return ServiceResp.getInstance().success("Hive连接测试成功");
                    } else {
                        return ServiceResp.getInstance().error("Hive连接测试失败");
                    }
                } catch (Exception e) {
                    logger.error("测试Hive连接失败", e);
                    return ServiceResp.getInstance().error("测试Hive连接失败: " + e.getMessage());
                }
            }
            
            // 构建事件对象，使用DbConnectionService进行测试
            DbConnectionConfigEvt evt = new DbConnectionConfigEvt();
            evt.setConnectionId(connectionId);
            evt.setDbType(dbType);
            evt.setDbVersion(dbVersion);
            evt.setAuthType(authType);
            evt.setHost(host);
            evt.setPort(port);
            evt.setDatabaseName(databaseName);
            evt.setUsername(username);
            evt.setPassword(password);
            evt.setPrincipal(principal);
            evt.setParams(params);
            evt.setCreatorName(creatorName);
            evt.setKeytabFile(keytabFile);
            evt.setKrb5ConfFile(krb5ConfFile);
            evt.setUrl(url);
            
            // 如果提供了URL，解析URL并填充相关字段
            if (url != null && !url.isEmpty()) {
                try {
                    com.ffcs.oss.utils.DbUrlParser.parseUrl(url, evt);
                } catch (Exception e) {
                    logger.error("解析URL时发生错误: {}", e.getMessage());
                    return ServiceResp.getInstance().error("解析JDBC URL时发生错误: " + e.getMessage());
                }
            }
            
            return dbConnectionService.testDbConnection(evt);
        } catch (Exception e) {
            logger.error("处理测试连接请求时发生异常", e);
            return ServiceResp.getInstance().error("处理测试连接请求时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户有权限的数据库连接列表
     */
    @ApiOperation("获取用户有权限的数据库连接列表")
    @PostMapping("/getUserDbConnectionList")
    public ServiceResp getUserDbConnectionList(@RequestBody DbConnectionConfigEvt evt) {
        // 确保传入了用户名
        if (evt.getCreatorName() == null || evt.getCreatorName().isEmpty()) {
            return ServiceResp.getInstance().error("用户名不能为空");
        }
        
        return ServiceResp.getInstance().success(
                dbConnectionService.getDbConnectionList(evt),
                LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS)
        );
    }
    
    /**
     * 获取支持的数据库类型列表
     */
    @ApiOperation("获取支持的数据库类型列表")
    @GetMapping("/getSupportedDbTypes")
    public ServiceResp getSupportedDbTypes() {
        // 返回支持的数据库类型列表
        String[] dbTypes = {
                DbConnectionConstant.DB_TYPE_MYSQL,
                DbConnectionConstant.DB_TYPE_POSTGRESQL,
                DbConnectionConstant.DB_TYPE_HIVE
        };
        
        return ServiceResp.getInstance().success(dbTypes, "获取成功");
    }
    
    /**
     * 获取MySQL支持的版本列表
     */
    @ApiOperation("获取MySQL支持的版本列表")
    @GetMapping("/getMySqlVersions")
    public ServiceResp getMySqlVersions() {
        // 返回MySQL支持的版本列表
        String[] versions = {
                DbConnectionConstant.MYSQL_VERSION_5_7,
                DbConnectionConstant.MYSQL_VERSION_8_0
        };
        
        return ServiceResp.getInstance().success(versions, "获取成功");
    }
    
    /**
     * 获取支持的认证类型列表
     */
    @ApiOperation("获取支持的认证类型列表")
    @GetMapping("/getSupportedAuthTypes")
    public ServiceResp getSupportedAuthTypes(@RequestParam("dbType") String dbType) {
        // 根据数据库类型返回支持的认证类型列表
        if (DbConnectionConstant.DB_TYPE_HIVE.equals(dbType)) {
            // Hive支持用户名密码和Kerberos认证
            String[] authTypes = {
                    DbConnectionConstant.AUTH_TYPE_PASSWORD,
                    DbConnectionConstant.AUTH_TYPE_KERBEROS
            };
            return ServiceResp.getInstance().success(authTypes, "获取成功");
        } else {
            // 其他数据库只支持用户名密码认证
            String[] authTypes = {
                    DbConnectionConstant.AUTH_TYPE_PASSWORD
            };
            return ServiceResp.getInstance().success(authTypes, "获取成功");
        }
    }
    
    /**
     * 测试解析数据库连接URL
     */
    @ApiOperation("测试解析数据库连接URL")
    @PostMapping("/testParseUrl")
    public ServiceResp testParseUrl(@RequestBody DbConnectionConfigEvt evt) {
        if (evt.getUrl() == null || evt.getUrl().isEmpty()) {
            return ServiceResp.getInstance().error("URL不能为空");
        }
        
        try {
            Map<String, Object> result = com.ffcs.oss.utils.DbUrlParser.testParseUrl(evt.getUrl());
            return ServiceResp.getInstance().success(result, "URL解析成功");
        } catch (Exception e) {
            return ServiceResp.getInstance().error("URL解析失败: " + e.getMessage());
        }
    }
} 
 