package com.ffcs.oss.service.impl;

import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.security.SecurityUtils;
import com.ffcs.oss.service.UserService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.vm.user.UserGroupVm;
import com.ffcs.oss.web.rest.vm.user.UserVm;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {
    
    @Override
    public String getCurrentUserName() {
//        return "chatbi";

        return StringUtils.isNotBlank(PtSecurityUtils.getUsername()) ?
               PtSecurityUtils.getUsername() :
               StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get()) ?
               SecurityUtils.getCurrentUserLogin().get() : "";
    }
    
    @Override
    public List<UserVm> searchUsers(String keyword) {
        // 这里应该调用门户系统的用户搜索接口
        // 由于没有实际的门户系统接口，这里模拟返回一些测试数据
        List<UserVm> userList = new ArrayList<>();
        
        // 添加当前用户
        String currentUser = getCurrentUserName();
        UserVm currentUserVm = new UserVm();
        currentUserVm.setUserId(UUID.randomUUID().toString());
        currentUserVm.setUserName(currentUser);
        currentUserVm.setUserType("普通用户");
        currentUserVm.setMobile("13800000000");
        currentUserVm.setEmail(currentUser + "@example.com");
        userList.add(currentUserVm);
        
        // 添加一些测试用户
        for (int i = 1; i <= 5; i++) {
            UserVm user = new UserVm();
            user.setUserId(UUID.randomUUID().toString());
            user.setUserName("测试用户" + i);
            user.setUserType("普通用户");
            user.setMobile("1380000000" + i);
            user.setEmail("test" + i + "@example.com");
            userList.add(user);
        }
        
        // 如果有关键字，进行过滤
        if (StringUtils.isNotBlank(keyword)) {
            List<UserVm> filteredList = new ArrayList<>();
            for (UserVm user : userList) {
                if (user.getUserName().contains(keyword) || 
                    user.getMobile().contains(keyword) || 
                    user.getEmail().contains(keyword)) {
                    filteredList.add(user);
                }
            }
            return filteredList;
        }
        
        return userList;
    }
    
    @Override
    public List<UserGroupVm> searchUserGroups(String keyword) {
        // 这里应该调用门户系统的用户组搜索接口
        // 由于没有实际的门户系统接口，这里模拟返回一些测试数据
        List<UserGroupVm> groupList = new ArrayList<>();
        
        // 添加一些测试用户组
        for (int i = 1; i <= 5; i++) {
            UserGroupVm group = new UserGroupVm();
            group.setGroupId(UUID.randomUUID().toString());
            group.setGroupName("测试用户组" + i);
            group.setDescription("这是测试用户组" + i + "的描述信息");
            groupList.add(group);
        }
        
        // 如果有关键字，进行过滤
        if (StringUtils.isNotBlank(keyword)) {
            List<UserGroupVm> filteredList = new ArrayList<>();
            for (UserGroupVm group : groupList) {
                if (group.getGroupName().contains(keyword) || 
                    group.getDescription().contains(keyword)) {
                    filteredList.add(group);
                }
            }
            return filteredList;
        }
        
        return groupList;
    }
} 