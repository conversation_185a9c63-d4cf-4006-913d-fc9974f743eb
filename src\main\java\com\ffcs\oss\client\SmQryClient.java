package com.ffcs.oss.client;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.web.rest.evt.user.GetGroupRelateUserEvt;
import com.ffcs.oss.web.rest.evt.user.QryUserEvt;
import com.ffcs.oss.web.rest.evt.user.QueryUserEvt;
import com.ffcs.oss.web.rest.evt.user.UserIdsEvt;
import com.ffcs.oss.web.rest.vm.user.GetGroupRelateUserVm;
import com.ffcs.oss.web.rest.vm.user.QueryUserVm;
import com.ffcs.oss.web.rest.vm.user.SimpleUserVm;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient("cs-sm-qry-srv")
public interface SmQryClient {

    @ApiOperation("根据用户ID或登录名查询某个用户简单信息")
    @PostMapping("/rsc/user/qrySimpleUser")
    ServiceResp<SimpleUserVm> qrySimpleUser(@RequestBody QryUserEvt evt);

    @ApiOperation("获取用户组携带用户数据")
    @PostMapping("/rsc/user/getUserGroupRelateUser")
    ServiceResp<QueryPageVm<GetGroupRelateUserVm>> getUserGroupRelateUser(GetGroupRelateUserEvt evt);

    @ApiOperation("查询用户信息")
    @PostMapping("/rsc/user/queryUser")
    ServiceResp<QueryPageVm<QueryUserVm>> queryUser(@RequestBody QueryUserEvt evt);

    @ApiOperation("根据用户名称获取用户组id")
    @GetMapping("/rsc/usergroup/getGroupIdByUserName")
    ServiceResp getGroupIdByUserName(@RequestParam("userName") String userName);

    @ApiOperation("根据ID数组获取用户信息")
    @PostMapping("/rsc/user/getUserByIds")
    ServiceResp<List<com.ffcs.oss.web.rest.vm.newUser.UserVm>> getUserByIds(@RequestBody UserIdsEvt evt);
}

