package com.ffcs.oss.web.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiAnswerD;
import com.ffcs.oss.domain.AiAssistantD;
import com.ffcs.oss.domain.AiAuditListD;
import com.ffcs.oss.domain.AiQuestionD;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.security.SecurityUtils;
import com.ffcs.oss.service.AiAnswerService;
import com.ffcs.oss.service.AiAssistantService;
import com.ffcs.oss.service.AiAuditListService;
import com.ffcs.oss.service.AiQuestionService;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.conversation.ConversationEvt;
import com.ffcs.oss.web.rest.evt.conversation.ConversationWithAnswerEvt;
import com.ffcs.oss.web.rest.evt.conversation.FeedbackEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 对话控制器
 */
@RestController
@RequestMapping(value = "/api/conversation")
@ApiModel("对话管理")
public class AiConversationController {

    @Autowired
    private AiQuestionService aiQuestionService;

    @Autowired
    private AiAnswerService aiAnswerService;

    @Autowired
    private AiAuditListService aiAuditListService;
    
    @Autowired
    private AiAssistantService aiAssistantService;

    /**
     * 保存用户提问
     */
    @PostMapping("/saveQuestion")
    @ApiOperation("保存用户提问")
    public ServiceResp saveQuestion(@RequestBody ConversationEvt evt) {
        if (StringUtils.isBlank(evt.getSessionId())) {
            return ServiceResp.getInstance().error("会话ID不能为空");
        }
        if (StringUtils.isBlank(evt.getQuestion())) {
            return ServiceResp.getInstance().error("问题内容不能为空");
        }

        // 创建问题记录
        AiQuestionD question = new AiQuestionD();
        BeanUtils.copyProperties(evt, question);

        // 设置问题ID和创建时间
        if (StringUtils.isBlank(question.getQuestionId())) {
            question.setQuestionId(UUID.randomUUID().toString().replace("-", ""));
        }
        question.setCreateTime(LocalDateTime.now());
        question.setCreateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");

        // 保存问题
        boolean savedQuestion = aiQuestionService.save(question);
        if (!savedQuestion) {
            return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_EXCEPTION));
        }

        return ServiceResp.getInstance().success(question, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 保存AI回答
     */
    @PostMapping("/saveAnswer")
    @ApiOperation("保存AI回答")
    public ServiceResp saveAnswer(@RequestBody ConversationEvt evt) {
        if (StringUtils.isBlank(evt.getQuestionId())) {
            return ServiceResp.getInstance().error("问题ID不能为空");
        }

        // 验证问题是否存在
        AiQuestionD question = aiQuestionService.getById(evt.getQuestionId());
        if (question == null) {
            return ServiceResp.getInstance().error("问题不存在");
        }

        // 创建回答记录
        AiAnswerD answer = new AiAnswerD();
        BeanUtils.copyProperties(evt, answer);

        // 设置必要字段
        answer.setAnswerId(UUID.randomUUID().toString().replace("-", ""));
        // 确保回答关联到原始问题
        answer.setQuestionId(evt.getQuestionId());
        answer.setSessionId(question.getSessionId());
        answer.setCreateTime(LocalDateTime.now());
        answer.setCreateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");

        // 保存回答
        boolean saved = aiAnswerService.save(answer);
        if (!saved) {
            return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_EXCEPTION));
        }

        return ServiceResp.getInstance().success(answer, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 保存对话及回答（同时保存问题和回答）
     */
    @PostMapping("/saveConversationWithAnswer")
    @ApiOperation("保存对话及回答")
    public ServiceResp saveConversationWithAnswer(@RequestBody ConversationWithAnswerEvt evt) {
        if (evt.getConversation() == null) {
            return ServiceResp.getInstance().error("会话信息不能为空");
        }

        ConversationEvt conversationEvt = evt.getConversation();
        if (StringUtils.isBlank(conversationEvt.getSessionId())) {
            return ServiceResp.getInstance().error("会话ID不能为空");
        }
        if (StringUtils.isBlank(conversationEvt.getQuestion())) {
            return ServiceResp.getInstance().error("问题内容不能为空");
        }


        // 创建问题记录
        AiQuestionD question = new AiQuestionD();
        BeanUtils.copyProperties(conversationEvt, question);

        // 设置问题ID和创建时间
        if (StringUtils.isBlank(question.getQuestionId())) {
            question.setQuestionId(UUID.randomUUID().toString().replace("-", ""));
            question.setCreateTime(LocalDateTime.now());
            question.setCreateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");
        }

        // 保存问题
        boolean savedQuestion = aiQuestionService.saveOrUpdate(question);
        if (!savedQuestion) {
            return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_EXCEPTION));
        }

        Map<String, Object> result = new HashMap<>();
        result.put("question", question);

        // 如果有回答信息，则同时保存回答
        if (evt.getAnswer() != null) {
            AiAnswerD answer = evt.getAnswer();

            // 设置必要字段
            answer.setAnswerId(UUID.randomUUID().toString().replace("-", ""));
            // 使用刚保存的问题ID
            answer.setQuestionId(question.getQuestionId());
            answer.setSessionId(question.getSessionId());
            answer.setCreateTime(LocalDateTime.now());
            answer.setCreateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");

            // 保存回答
            boolean savedAnswer = aiAnswerService.save(answer);
            if (!savedAnswer) {
                return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_EXCEPTION));
            }

            result.put("answer", answer);
        }

        return ServiceResp.getInstance().success(result, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 获取会话历史记录
     */
    @PostMapping("/getConversationHistory")
    @ApiOperation("获取会话历史记录")
    public ServiceResp getConversationHistory(@RequestBody ConversationEvt evt) {
        if (StringUtils.isBlank(evt.getSessionId())) {
            return ServiceResp.getInstance().error("会话ID不能为空");
        }

        // 获取会话下所有问题和回答
        List<Map<String, Object>> history = aiQuestionService.getConversationHistoryWithAnswers(evt.getSessionId());

        return ServiceResp.getInstance().success(history, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }
    
    /**
     * 获取上一轮对话信息（用于构建提示词）
     */
    @PostMapping("/getLastConversationInfo")
    @ApiOperation("获取上一轮对话信息")
    public ServiceResp getLastConversationInfo(@RequestBody ConversationEvt evt) {
        if (StringUtils.isBlank(evt.getSessionId())) {
            return ServiceResp.getInstance().error("会话ID不能为空");
        }
        
        // 获取会话信息
        List<Map<String, Object>> history = aiQuestionService.getConversationHistoryWithAnswers(evt.getSessionId());
        
        // 检查是否启用多轮对话
        AiQuestionD firstQuestion = null;
        if (!history.isEmpty()) {
            Map<String, Object> firstItem = history.get(0);
            firstQuestion = (AiQuestionD) firstItem.get("question");
        }
        
        if (firstQuestion == null) {
            return ServiceResp.getInstance().error("未找到会话信息");
        }
        
        // 获取助理信息
        LambdaQueryWrapper<AiAssistantD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiAssistantD::getAssistantId, evt.getAssistantId());
        AiAssistantD assistant = aiAssistantService.getOne(queryWrapper);
        
        if (assistant == null) {
            return ServiceResp.getInstance().error("未找到助理信息");
        }
        
        // 检查是否启用多轮对话
        boolean enableMultiTurn = "1".equals(assistant.getEnableMultiTurn());
        
        Map<String, Object> result = new HashMap<>();
        result.put("enableMultiTurn", enableMultiTurn);
        
        // 如果启用多轮对话且历史记录中有超过1条记录，则获取最后一轮对话
        if (enableMultiTurn && history.size() > 1) {
            // 最后一轮对话是倒数第二条记录（最后一条是当前问题）
            Map<String, Object> lastItem = history.get(history.size() - 2);
            AiQuestionD lastQuestion = (AiQuestionD) lastItem.get("question");
            AiAnswerD lastAnswer = (AiAnswerD) lastItem.get("answer");
            
            if (lastQuestion != null && lastAnswer != null) {
                Map<String, Object> lastConversation = new HashMap<>();
                lastConversation.put("question", lastQuestion);
                lastConversation.put("answer", lastAnswer);
                result.put("lastConversation", lastConversation);
            }
        }
        
        return ServiceResp.getInstance().success(result, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 点赞/点踩回答
     */
    @PostMapping("/feedback")
    @ApiOperation("点赞/点踩回答")
    public ServiceResp feedback(@RequestBody FeedbackEvt evt) {
        if (StringUtils.isBlank(evt.getAnswerId())) {
            return ServiceResp.getInstance().error("回答ID不能为空");
        }
        if (StringUtils.isBlank(evt.getFeedback())) {
            return ServiceResp.getInstance().error("反馈类型不能为空");
        }

        // 获取回答
        AiAnswerD answer = aiAnswerService.getById(evt.getAnswerId());
        if (answer == null) {
            return ServiceResp.getInstance().error("回答不存在");
        }

        // 更新回答的点赞/点踩状态
        answer.setLikeOrNoLikeResult(evt.getFeedback());
        answer.setUpdateTime(LocalDateTime.now());
        answer.setUpdateUserName(evt.getUserId());
        boolean updated = aiAnswerService.updateById(answer);

        if (!updated) {
            return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_EXCEPTION));
        }

        // 获取问题
        AiQuestionD question = aiQuestionService.getById(answer.getQuestionId());
        if (question == null) {
            return ServiceResp.getInstance().error("问题不存在");
        }

        // 如果是点踩，创建审核记录
        if ("1".equals(evt.getFeedback())||"2".equals(evt.getFeedback())) {
            AiAuditListD auditList = new AiAuditListD();
            auditList.setAuditStatus("0"); // 待审核
            auditList.setQuestion(question.getQuestion());
            auditList.setBigModelResult(answer.getResult());
            auditList.setOldCorrectSql(answer.getSql());
            auditList.setCorrectSql(answer.getSql());
            auditList.setLikeOrNoLikeResult(evt.getFeedback());
            auditList.setFeedBackUser(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");
            auditList.setFeedBackTime(LocalDateTime.now());
            auditList.setSessionId(answer.getSessionId());
            auditList.setCreateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername())?PtSecurityUtils.getUsername():StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get())?SecurityUtils.getCurrentUserLogin().get():"");
            auditList.setCreateTime(LocalDateTime.now());
            auditList.setUpdateUserName(evt.getUserId());
            auditList.setUpdateTime(LocalDateTime.now());
            auditList.setAnswerId(answer.getAnswerId());
            
            boolean savedAudit = aiAuditListService.save(auditList);
            if (!savedAudit) {
                return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_EXCEPTION));
            }
        }

        return ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }
} 