package com.ffcs.oss.utils.hive;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.security.UserGroupInformation;

import java.io.IOException;
import java.security.PrivilegedExceptionAction;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Hive连接管理器
 */
@Slf4j
public class HiveConnectionManager {

    private static final String HIVE_DRIVER = "org.apache.hive.jdbc.HiveDriver";
    
    // 默认过期时间(小时)
    private static final int DEFAULT_EXPIRE_TIME_HOURS = 10;
    
    // 连接缓存，避免频繁创建连接
    private static final Cache<String, HivePooledConnection> CONNECTION_CACHE =
            CacheBuilder.newBuilder()
                    .expireAfterWrite(DEFAULT_EXPIRE_TIME_HOURS, TimeUnit.HOURS)
                    .removalListener((RemovalListener<String, HivePooledConnection>) notification -> {
                        try {
                            HivePooledConnection connection = notification.getValue();
                            if (connection != null) {
                                connection.close();
                                log.info("Hive连接已从缓存中移除: {}", notification.getKey());
                            }
                        } catch (Exception e) {
                            log.error("关闭Hive连接失败", e);
                        }
                    })
                    .maximumSize(20)
                    .build();

    /**
     * 私有构造函数，防止实例化
     */
    private HiveConnectionManager() {
    }
    
    /**
     * 获取一个一次性连接（每次调用都创建新连接，用完即关闭）
     *
     * @param connectionParam Hive连接参数
     * @return 数据库连接
     * @throws SQLException SQL异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    public static Connection getAdHocConnection(HiveConnectionParam connectionParam) 
            throws SQLException, IOException, InterruptedException {
        // 注册驱动
        try {
            Class.forName(HIVE_DRIVER);
        } catch (ClassNotFoundException e) {
            throw new SQLException("Hive JDBC驱动未找到", e);
        }

        UserGroupInformation ugi;
        if (connectionParam.isKerberosEnabled()) {
            // 使用Kerberos认证
            validateKerberosParams(connectionParam);
            ugi = UserGroupInformationFactory.createKerberosUser(
                    connectionParam.getJavaSecurityKrb5Conf(),
                    connectionParam.getLoginUserKeytabPath(),
                    connectionParam.getLoginUserKeytabUsername());
        } else {
            // 使用普通用户名
            ugi = UserGroupInformationFactory.createRemoteUser(connectionParam.getUsername());
        }

        // 使用UGI执行获取连接操作
        return ugi.doAs((PrivilegedExceptionAction<Connection>) () -> 
            DriverManager.getConnection(
                connectionParam.getJdbcUrl(),
                connectionParam.getUsername(),
                connectionParam.getPassword())
        );
    }
    
    /**
     * 获取一个池化连接（连接会被缓存重用）
     *
     * @param connectionParam Hive连接参数
     * @return 数据库连接
     * @throws SQLException SQL异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     * @throws ExecutionException 执行异常
     */
    public static Connection getPooledConnection(HiveConnectionParam connectionParam)
            throws SQLException, IOException, InterruptedException, ExecutionException {
        String connectionId = generateConnectionId(connectionParam);
        HivePooledConnection hivePooledConnection = CONNECTION_CACHE.get(connectionId, () -> {
            log.info("创建新的Hive池化连接: {}", connectionId);
            return new HivePooledConnection(connectionParam);
        });
        
        return hivePooledConnection.getConnection();
    }
    
    /**
     * 关闭连接池中的所有连接
     */
    public static void closeAllConnections() {
        CONNECTION_CACHE.invalidateAll();
    }
    
    /**
     * 验证Kerberos参数
     * 
     * @param connectionParam Hive连接参数
     */
    private static void validateKerberosParams(HiveConnectionParam connectionParam) {
        if (StringUtils.isBlank(connectionParam.getJavaSecurityKrb5Conf())) {
            throw new IllegalArgumentException("使用Kerberos认证时，krb5.conf文件路径不能为空");
        }
        if (StringUtils.isBlank(connectionParam.getLoginUserKeytabPath())) {
            throw new IllegalArgumentException("使用Kerberos认证时，keytab文件路径不能为空");
        }
        if (StringUtils.isBlank(connectionParam.getLoginUserKeytabUsername())) {
            throw new IllegalArgumentException("使用Kerberos认证时，Kerberos principal不能为空");
        }
    }
    
    /**
     * 为连接生成唯一ID
     * 
     * @param connectionParam Hive连接参数
     * @return 连接ID
     */
    private static String generateConnectionId(HiveConnectionParam connectionParam) {
        StringBuilder sb = new StringBuilder();
        sb.append(connectionParam.getJdbcUrl()).append(":");
        sb.append(connectionParam.getUsername()).append(":");
        if (connectionParam.isKerberosEnabled()) {
            sb.append("kerberos:").append(connectionParam.getLoginUserKeytabUsername());
        } else {
            sb.append("simple");
        }
        return sb.toString();
    }
} 