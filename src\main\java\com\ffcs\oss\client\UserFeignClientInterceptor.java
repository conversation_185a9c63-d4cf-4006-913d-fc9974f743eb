package com.ffcs.oss.client;

//import com.ffcs.oss.pto.dcoos.constant.DcoosConstant;
//import com.ffcs.oss.pto.dcoos.properties.DcoosProperties;
//import com.ffcs.oss.pto.dcoos.service.DcoosAuthentication;
//import com.ffcs.oss.pto.dcoos.utils.DcoosPathMatcherUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;

public class UserFeignClientInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {

    }

//    private static final String AUTHORIZATION_HEADER = "Authorization";
//
//    private static final String BEARER_TOKEN_TYPE = "Bearer";
//
//    @Autowired
//    private DcoosProperties dcoosProperties;
//
//    @Autowired
//    private DcoosAuthentication dcoosAuthentication;
//
//    @Autowired
//    private Environment environment;
//
//    @Override
//    public void apply(RequestTemplate template) {
//        SecurityContext securityContext = SecurityContextHolder.getContext();
//        Authentication authentication = securityContext.getAuthentication();
//        if (authentication != null && authentication.getDetails() instanceof OAuth2AuthenticationDetails) {
//            OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails) authentication.getDetails();
//            template.header(AUTHORIZATION_HEADER, String.format("%s %s", BEARER_TOKEN_TYPE, details.getTokenValue()));
//        }
//        template.header("remoteServiceName", this.environment.getProperty("spring.application.name"));
//        template.header("remoteCenterCode", this.environment.getProperty("oss.centerCode"));
//        if (this.dcoosProperties.getEnabled()) {
//            String url = template.url();
//            if (!isDcoosCall(url)) {
//                return;
//            }
//            try {
//                template.header(DcoosConstant.TOKEN, this.dcoosAuthentication.getAccessToken(url, template.method()));
//            } finally {
//            }
//        }
//    }
//
//
//    /**
//     * 检查是否是服务与服务之间的调用
//     *
//     * @param uri uri
//     * @return boolean
//     */
//    private boolean isDcoosCall(String uri) {
//        String urlPattern = this.dcoosProperties.getUrlPattern();
//        if (org.springframework.util.StringUtils.isEmpty(urlPattern)) {
//            urlPattern = DcoosConstant.ANY_PATTERN;
//        }
//        String[] urlPatterns = org.springframework.util.StringUtils.commaDelimitedListToStringArray(urlPattern);
//        for (String pattern : urlPatterns) {
//            if (DcoosPathMatcherUtil.match(pattern, uri)) {
//                return true;
//            }
//        }
//        return false;
//    }
}
