package com.ffcs.oss.web.rest.evt.user;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2018/12/17  15:02
 */
@ApiModel("用户组关联用户查询参数")
public class GetGroupRelateUserEvt extends QueryPageEvt implements Serializable {
    private static final long serialVersionUID = -219872856053385910L;
    @ApiModelProperty(value = "机构ID")
    private Long orgId;
    @ApiModelProperty(value = "用户组ID")
    private Long userGroupId;
    @ApiModelProperty(value = "用户名")
    private String userName;
    @ApiModelProperty(value = "电话号码")
    private String phone;
    /*@ApiModelProperty(value = "关联方式")
    private Integer relatedType;*/
    @ApiModelProperty("关键字")
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return "GetGroupRelateUserEvt{" +
                "orgId=" + orgId +
                ", userGroupId=" + userGroupId +
                ", userName='" + userName + '\'' +
                ", phone='" + phone + '\'' +
                ", keyword='" + keyword + '\'' +
                '}';
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Long getUserGroupId() {
        return userGroupId;
    }

    public void setUserGroupId(Long userGroupId) {
        this.userGroupId = userGroupId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

}
