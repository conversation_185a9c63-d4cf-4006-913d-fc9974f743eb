package com.ffcs.oss.web.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiAuditListD;
import com.ffcs.oss.domain.AiTodoListD;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.security.SecurityUtils;
import com.ffcs.oss.service.AiAuditListService;
import com.ffcs.oss.service.AiChatManageService;
import com.ffcs.oss.service.AiPermissionConfigService;
import com.ffcs.oss.service.AiTodoListService;
import com.ffcs.oss.service.UserService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.constant.CommonConstant;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.audit.AuditEvt;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 审核控制器
 */
@RestController
@RequestMapping(value = "/api/audit")
@ApiModel("审核管理")
@Slf4j
public class AiAuditController {

    private final AiAuditListService aiAuditListService;

    private final AiTodoListService aiTodoListService;
    
    @Autowired
    private AiChatManageService aiChatManageService;
    
    @Autowired
    private AiPermissionConfigService permissionConfigService;
    
    @Autowired
    private UserService userService;

    public AiAuditController(AiAuditListService aiAuditListService, AiTodoListService aiTodoListService) {
        this.aiAuditListService = aiAuditListService;
        this.aiTodoListService = aiTodoListService;
    }

    /**
     * 分页查询审核列表
     */
    @PostMapping("/getAuditList")
    public ServiceResp getAuditList(@RequestBody AuditEvt evt) {
        Page<AiAuditListD> page = new Page<>(evt.getPageNo(), evt.getPageSize());
        LambdaQueryWrapper<AiAuditListD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(evt.getAuditStatus() != null, AiAuditListD::getAuditStatus, evt.getAuditStatus());
        queryWrapper.eq(StringUtils.isNotBlank(evt.getCreateUserName()), AiAuditListD::getCreateUserName, evt.getCreateUserName());
        queryWrapper.like(StringUtils.isNotBlank(evt.getQuestion()), AiAuditListD::getQuestion, evt.getQuestion());
        queryWrapper.orderByDesc(AiAuditListD::getCreateTime);
        
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 查询原始列表
        aiAuditListService.page(page, queryWrapper);
        
        // 对结果进行过滤，保留用户有权限的数据模型关联的记录
        List<AiAuditListD> filteredRecords = page.getRecords().stream()
                .filter(audit -> {
                    // 如果没有会话ID，则保留
                    if (StringUtils.isBlank(audit.getSessionId())) {
                        return true;
                    }
                    
                    // 获取关联的数据模型ID
                    String dataModelId = audit.getDataModelId();
                    if (StringUtils.isBlank(dataModelId)) {
                        // 如果记录中没有数据模型ID，尝试从会话获取
                        dataModelId = aiChatManageService.getDataModelIdBySessionId(audit.getSessionId());
                        // 更新记录中的数据模型ID
                        if (StringUtils.isNotBlank(dataModelId)) {
                            audit.setDataModelId(dataModelId);
                            aiAuditListService.updateById(audit);
                        }
                    }
                    
                    // 如果没有关联数据模型，则保留
                    if (StringUtils.isBlank(dataModelId)) {
                        return true;
                    }
                    
                    // 检查用户是否有该数据模型的权限
                    return permissionConfigService.hasUsePermission(
                            CommonConstant.RESOURCE_TYPE_DATA_MODEL,
                            dataModelId,
                            currentUserName
                    );
                })
                .collect(Collectors.toList());
        
        // 更新总记录数
        long totalCount = filteredRecords.size();
        
        return ServiceResp.getInstance().success(
                com.ffcs.oss.param.vm.QueryPageVm.getInstance(evt, filteredRecords, totalCount),
                LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 查询待办列表
     */
    @PostMapping("/getTodoList")
    public ServiceResp getTodoList(@RequestBody AuditEvt evt) {
        Page<AiTodoListD> page = new Page<>(evt.getPageNo(), evt.getPageSize());
        LambdaQueryWrapper<AiTodoListD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(evt.getCreateUserName()), AiTodoListD::getCreateUserName, evt.getCreateUserName());
        queryWrapper.orderByDesc(AiTodoListD::getUpdateTime, AiTodoListD::getCreateTime);
        
        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();
        
        // 查询原始列表
        aiTodoListService.page(page, queryWrapper);
        
        // 对结果进行过滤，保留用户有权限的数据模型关联的记录
        List<AiTodoListD> filteredRecords = page.getRecords().stream()
                .filter(todo -> {
                    // 如果没有会话ID，则保留
                    if (StringUtils.isBlank(todo.getSessionId())) {
                        return true;
                    }
                    
                    // 获取关联的数据模型ID
                    String dataModelId = todo.getDataModelId();
                    if (StringUtils.isBlank(dataModelId)) {
                        // 如果记录中没有数据模型ID，尝试从会话获取
                        dataModelId = aiChatManageService.getDataModelIdBySessionId(todo.getSessionId());
                        // 更新记录中的数据模型ID
                        if (StringUtils.isNotBlank(dataModelId)) {
                            todo.setDataModelId(dataModelId);
                            aiTodoListService.updateById(todo);
                        }
                    }
                    
                    // 如果没有关联数据模型，则保留
                    if (StringUtils.isBlank(dataModelId)) {
                        return true;
                    }
                    
                    // 检查用户是否有该数据模型的权限
                    return permissionConfigService.hasUsePermission(
                            CommonConstant.RESOURCE_TYPE_DATA_MODEL,
                            dataModelId,
                            currentUserName
                    );
                })
                .collect(Collectors.toList());
        
        // 更新总记录数
        long totalCount = filteredRecords.size();
        
        return ServiceResp.getInstance().success(
                com.ffcs.oss.param.vm.QueryPageVm.getInstance(evt, filteredRecords, totalCount),
                LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 获取审核详情
     */
    @PostMapping("/getAuditDetail")
    public ServiceResp getAuditDetail(@RequestBody AuditEvt evt) {
        AiAuditListD auditListD = aiAuditListService.getById(evt.getAuditListId());
        return ServiceResp.getInstance().success(auditListD, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 获取待办详情
     */
    @PostMapping("/getTodoDetail")
    public ServiceResp getTodoDetail(@RequestBody AuditEvt evt) {
        AiTodoListD todoListD = aiTodoListService.getById(evt.getTodoListId());
        return ServiceResp.getInstance().success(todoListD, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    /**
     * 审核操作 - 加入待办
     */
    @PostMapping("/addTodo")
    public ServiceResp addToTodo(@RequestBody AuditEvt evt) {
        boolean result = aiAuditListService.addToTodoList(evt);
        return result ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    /**
     * 审核操作 - 完成审核
     */
    @PostMapping("/completeAudit")
    public ServiceResp completeAudit(@RequestBody AuditEvt evt) {
        AiAuditListD auditListD = new AiAuditListD();
        BeanUtils.copyProperties(evt, auditListD);
        auditListD.setUpdateTime(LocalDateTime.now());
        auditListD.setUpdateUserName(StringUtils.isNotBlank(PtSecurityUtils.getUsername()) ? PtSecurityUtils.getUsername() : StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get()) ? SecurityUtils.getCurrentUserLogin().get() : "");

        boolean result = aiAuditListService.completeAudit(auditListD);
        return result ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    /**
     * 待办审核 - 完成审核
     */
    @PostMapping("/completeTodoAudit")
    public ServiceResp completeTodoAudit(@RequestBody AuditEvt evt) {
        boolean result = aiTodoListService.completeTodoAudit(evt);
        return result ? ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS))
                : ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }
} 