package com.ffcs.oss.mapper.dbconnection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ffcs.oss.domain.AiDbConnectionConfigD;
import com.ffcs.oss.web.rest.evt.dbconnection.DbConnectionConfigEvt;
import com.ffcs.oss.web.rest.vm.dbconnection.DbConnectionConfigVm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据库连接配置Mapper
 */
public interface DbConnectionConfigMapper extends BaseMapper<AiDbConnectionConfigD> {
    
    /**
     * 获取数据库连接配置列表
     *
     * @param evt
     * @return
     */
    List<DbConnectionConfigVm> getDbConnectionList(@Param("evt") DbConnectionConfigEvt evt);
    
    /**
     * 连接名称是否重复
     *
     * @param evt
     * @return
     */
    Integer nameWhetherRepeat(@Param("evt") DbConnectionConfigEvt evt);
} 