package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.domain.AiAnswerD;
import com.ffcs.oss.domain.AiCollectionD;
import com.ffcs.oss.domain.AiQuestionD;
import com.ffcs.oss.mapper.AiCollectionMapper;
import com.ffcs.oss.service.AiAnswerService;
import com.ffcs.oss.service.AiCollectionService;
import com.ffcs.oss.service.AiQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 收藏Service实现类
 */
@Service
public class AiCollectionServiceImpl extends ServiceImpl<AiCollectionMapper, AiCollectionD> implements AiCollectionService {

    @Autowired
    private AiQuestionService aiQuestionService;

    @Autowired
    private AiAnswerService aiAnswerService;

    /**
     * 获取助理的收藏列表
     * @param assistantId 助理ID
     * @return 收藏列表（包含问题和回答）
     */
    @Override
    public List<Map<String, Object>> getCollectionsByAssistantId(Long assistantId) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 获取助理的所有收藏
        LambdaQueryWrapper<AiCollectionD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCollectionD::getAssistantId, assistantId);
        queryWrapper.orderByDesc(AiCollectionD::getCreateTime);
        List<AiCollectionD> collections = this.list(queryWrapper);
        
        // 遍历收藏，获取问题和回答
        for (AiCollectionD collection : collections) {
            Map<String, Object> item = new HashMap<>();
            item.put("collection", collection);
            
            // 获取问题
            AiQuestionD question = aiQuestionService.getById(collection.getQuestionId());
            if (question != null) {
                item.put("question", question);
            }
            
            // 获取回答
            AiAnswerD answer = aiAnswerService.getById(collection.getAnswerId());
            if (answer != null) {
                item.put("answer", answer);
            }
            
            result.add(item);
        }
        
        return result;
    }
    
    /**
     * 获取收藏详情
     * @param collectionId 收藏ID
     * @return 收藏详情（包含问题和回答）
     */
    @Override
    public Map<String, Object> getCollectionDetail(String collectionId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取收藏
        AiCollectionD collection = this.getById(collectionId);
        if (collection == null) {
            return result;
        }
        
        result.put("collection", collection);
        
        // 获取问题
        AiQuestionD question = aiQuestionService.getById(collection.getQuestionId());
        if (question != null) {
            result.put("question", question);
        }
        
        // 获取回答
        AiAnswerD answer = aiAnswerService.getById(collection.getAnswerId());
        if (answer != null) {
            result.put("answer", answer);
        }
        
        return result;
    }
    
    /**
     * 添加收藏
     * @param questionId 问题ID
     * @param answerId 回答ID
     * @param assistantId 助理ID
     * @param username 用户名
     * @return 是否成功
     */
    @Override
    public boolean addCollection(String questionId, String answerId, Long assistantId, String username) {
        // 检查是否已收藏
        if (isCollected(questionId, answerId)) {
            return true;
        }
        
        // 获取问题，用于设置收藏标题
        AiQuestionD question = aiQuestionService.getById(questionId);
        if (question == null) {
            return false;
        }
        
        // 创建收藏
        AiCollectionD collection = new AiCollectionD();
        collection.setQuestionId(questionId);
        collection.setAnswerId(answerId);
        collection.setAssistantId(assistantId);
        collection.setCreateUserName(username);
        collection.setUpdateUserName(username);
        collection.setCreateTime(LocalDateTime.now());
        collection.setUpdateTime(LocalDateTime.now());
        collection.setTitle(question.getQuestion());
        
        return this.save(collection);
    }
    
    /**
     * 检查是否已收藏
     * @param questionId 问题ID
     * @param answerId 回答ID
     * @return 是否已收藏
     */
    @Override
    public boolean isCollected(String questionId, String answerId) {
        LambdaQueryWrapper<AiCollectionD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCollectionD::getQuestionId, questionId);
        queryWrapper.eq(AiCollectionD::getAnswerId, answerId);
        return this.count(queryWrapper) > 0;
    }
    
    /**
     * 根据问题ID和回答ID获取收藏
     * @param questionId 问题ID
     * @param answerId 回答ID
     * @return 收藏对象
     */
    @Override
    public AiCollectionD getByQuestionIdAndAnswerId(String questionId, String answerId) {
        LambdaQueryWrapper<AiCollectionD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCollectionD::getQuestionId, questionId);
        queryWrapper.eq(AiCollectionD::getAnswerId, answerId);
        return this.getOne(queryWrapper);
    }
} 