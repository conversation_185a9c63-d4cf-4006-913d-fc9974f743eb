package com.ffcs.oss.utils.db;

import com.ffcs.oss.utils.JasyptEncryptorUtils;
import com.ffcs.oss.utils.StringUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * PostgreSQL SQL执行器实现
 */
public class PostgreSqlExecutor extends AbstractSqlExecutor {

    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private String password;
    private final String params;

    public PostgreSqlExecutor(String host, int port, String database, String username, String password, String params) {
        this.host = host;
        this.port = port;
        this.database = database;
        this.username = username;
        this.password = password;
        this.params = params;
    }

    @Override
    protected Connection createConnection() throws SQLException {
        try {
            Class.forName("org.postgresql.Driver");

            String url = "jdbc:postgresql://" + host + ":" + port + "/" + database;
            if (params != null && !params.isEmpty()) {
                if (!params.startsWith("?")) {
                    url += "?";
                }
                url += params;
            }
            if (StringUtils.isNotBlank(password) && password.startsWith("ENC")) {
                String decryptPassword = JasyptEncryptorUtils.decrypt(password);
                password = decryptPassword;
            }
            return DriverManager.getConnection(url, username, password);
        } catch (ClassNotFoundException e) {
            throw new SQLException("PostgreSQL JDBC驱动未找到", e);
        }
    }
} 