package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.domain.AiAssistantD;
import com.ffcs.oss.domain.AiChatManageD;
import com.ffcs.oss.mapper.AiAssistantMapper;
import com.ffcs.oss.mapper.AiChatManageMapper;
import com.ffcs.oss.service.AiChatManageService;
import com.ffcs.oss.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 聊天管理Service实现类
 */
@Service
public class AiChatManageServiceImpl extends ServiceImpl<AiChatManageMapper, AiChatManageD> implements AiChatManageService {
    
    @Autowired
    private AiAssistantMapper aiAssistantMapper;
    
    @Override
    public String getDataModelIdBySessionId(String sessionId) {
        if (StringUtils.isBlank(sessionId)) {
            return null;
        }
        
        // 根据会话ID查询会话信息
        AiChatManageD chatManage = this.getById(sessionId);
        if (chatManage == null || chatManage.getAssistantId() == null) {
            return null;
        }
        
        // 根据助理ID查询助理信息
        AiAssistantD assistant = aiAssistantMapper.selectById(chatManage.getAssistantId());
        if (assistant == null) {
            return null;
        }
        
        // 返回助理关联的数据模型ID
        return assistant.getDataModelId();
    }
} 