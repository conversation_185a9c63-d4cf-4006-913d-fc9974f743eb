package com.ffcs.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ffcs.oss.domain.AiAssistantD;

/**
 * 助理Service接口
 */
public interface AiAssistantService extends IService<AiAssistantD> {
    
    /**
     * 检查助理名称是否已存在
     *
     * @param assistantName 助理名称
     * @param excludeAssistantId 排除的助理ID（用于更新时排除自身）
     * @return 如果名称已存在返回true，否则返回false
     */
    boolean checkAssistantNameExists(String assistantName, Long excludeAssistantId);
} 