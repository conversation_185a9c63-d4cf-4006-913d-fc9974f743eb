package com.ffcs.oss.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.math.BigDecimal;
import java.sql.Clob;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.regex.Pattern;

/**
 * About:字符串工具类
 * Other:
 * Created: chenchen on 2019/04/09 11:41
 * Editored:
 */
public class StringUtils {
    /**
     * oracle.sql.Clob类型转换成String类型
     * @param clob
     * @return
     * @throws SQLException
     * @throws IOException
     */
    public static String clobToString(Clob clob) throws SQLException, IOException {
        String reString = "";
        if (clob != null) {
            Reader is = clob.getCharacterStream();// 得到流
            BufferedReader br = new BufferedReader(is);
            String s = br.readLine();
            StringBuffer sb = new StringBuffer();
            while (s != null) {
                sb.append(s);
                s = br.readLine();
            }
            reString = sb.toString();
        }
        return reString;
    }

    /**
     * 将obj 转换为String
     * @param obj
     * @return
     */
    public static String getObjStr(Object obj){
        return obj == null?"":obj.toString().trim();
    }

    /**
     * 将obj转换为int
     * @param obj
     * @return
     */
    public static int getObjInt(Object obj){
        return Integer.parseInt(obj==null?"0":obj+"");
    }

    public static Integer getObjInteger(Object obj){
        return obj==null||isEmpty(obj.toString().trim())?null: Integer.parseInt(obj.toString());
    }

    /**
     * 根据指定字符分隔字符串，
     * @param str
     * @param split
     * @param idx
     * @return 并返回指定下标的数据,下标超出长度则返回第0个
     */
    public static String getSplitData(String str, String split, int idx){
        if(null == str) {
            return "";
        }
        String resStr = "";
        String[] strList = str.split("\\" + split);
        if (idx < 0) {
            return resStr;
        }
        if (strList.length > idx) {
            return strList[idx];
        } else {
            return strList[0];
        }
    }

    /**
     * 判断是否为空字符串，空返回true,非空返回false
     * @param str 判断是否为空字符串
     * @return 空返回true,非空返回false
     */
    public static boolean isEmpty(Object str){
        return ((str == null || str.toString().trim().equals("")) ? true:false);
    }

    /**
     * 判断是否为空字符串，空返回true,非空返回false
     * @param str 判断是否为空字符串
     * @return 空返回true,非空返回false
     */
    public static boolean isEmpty(String str){
        return ((str == null || str.trim().equals("")) ? true:false);
    }

    /**
     * 检查指定的字符串列表是否不为空。
     */
    public static boolean areNotEmpty(String... values) {
        boolean result = true;
        if (values == null || values.length == 0) {
            result = false;
        } else {
            for (String value : values) {
                result &= !isEmpty(value);
            }
        }
        return result;
    }

    /**
     *将obj转换为Boolean
     *@param obj
     * @return
     */
    public static boolean getobjBoolean(Object obj){
        if(obj instanceof String){
            return Boolean.parseBoolean((String) obj);
        }
        return obj == null?false:(boolean)obj;
    }

    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }

    /**
     * 判断是否为空字符串，空返回false,非空返回true
     * @param str 判断是否为空字符串
     * @return 空返回false,非空返回true
     */
    public static boolean isNotEmpty(String str){
        return ((str == null || "".equals(str.trim())) ? false:true);
    }
    /**
     * 分数转百分比
     */
    public static String getPercent(String str) {
        DecimalFormat format = new DecimalFormat("0.0000");
        Double getPercent = Double.parseDouble(str) * 100;
        String percentStr = getObjStr(getPercent);
        percentStr = format.format(new BigDecimal(percentStr));
        percentStr = percentStr.substring(0, percentStr.length() - 2);
        return percentStr + "%";
    }
    /**
     * 分数转百分比
     */
    public static String getStringPercent(String str) {
        DecimalFormat format = new DecimalFormat("0.0000");
        Double getPercent = Double.parseDouble(str) * 100;
        String percentStr = getObjStr(getPercent);
        percentStr = format.format(new BigDecimal(percentStr));
        percentStr = percentStr.substring(0, percentStr.length() - 2);
        return percentStr;
    }
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }
    public static boolean isBlank(String str) {
        int strLen;
        if (str != null && (strLen = str.length()) != 0) {
            for(int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(str.charAt(i))) {
                    return false;
                }
            }

            return true;
        } else {
            return true;
        }
    }
    /**
     * 去掉首字母为0
     */
    public static String getResult(String str){
        StringBuffer resultStr = new StringBuffer();//用来存储删除后的字符串。
        for (int i = 0; i<str.length(); i++) {
            char getChar = str.charAt(i);//得到字符串中第i个字符
            if(i==0){
                if(getChar == '0' ){
                    continue;
                }
            }
            resultStr.append(getChar);//将不是/的字符追加到resultStr中
        }
        return resultStr.toString();//将结果转为String类型，并返回
    }


    /**
     * 截取最后
     */
    public static String getSubsEnd(String str,String str1,String str2){
        String result = str.substring(str.lastIndexOf(str1)+1,str.lastIndexOf(str2));
        return result;
    }

    public static void main(String[] args) {
        String abc = "海缆=【TPE】/站点段=【Qingdao-Nedonna(TPE);Qingdao-Keoje(TPE)】";
        System.out.println(getSubsEnd(abc,"【","】"));
    }
}
