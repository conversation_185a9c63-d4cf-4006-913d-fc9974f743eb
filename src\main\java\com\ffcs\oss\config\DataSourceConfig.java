package com.ffcs.oss.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 数据源配置类
 */
@Configuration
@ConfigurationProperties(prefix = "datasource")
public class DataSourceConfig {
    
    /**
     * Hive Kerberos配置
     */
    private Hive hive = new Hive();
    
    /**
     * Elasticsearch配置
     */
    private Elasticsearch elasticsearch = new Elasticsearch();
    
    /**
     * Hive配置类
     */
    public static class Hive {
        /**
         * 是否启用Kerberos认证
         */
        private boolean kerberosEnabled = false;
        
        /**
         * Kerberos principal
         */
        private String principal;
        
        /**
         * Kerberos keytab文件路径
         */
        private String keytabPath;

        public boolean isKerberosEnabled() {
            return kerberosEnabled;
        }

        public void setKerberosEnabled(boolean kerberosEnabled) {
            this.kerberosEnabled = kerberosEnabled;
        }

        public String getPrincipal() {
            return principal;
        }

        public void setPrincipal(String principal) {
            this.principal = principal;
        }

        public String getKeytabPath() {
            return keytabPath;
        }

        public void setKeytabPath(String keytabPath) {
            this.keytabPath = keytabPath;
        }
    }
    
    /**
     * Elasticsearch配置类
     */
    public static class Elasticsearch {
        /**
         * Elasticsearch版本，默认为7
         */
        private String version = "7";

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }
    
    public Hive getHive() {
        return hive;
    }
    
    public void setHive(Hive hive) {
        this.hive = hive;
    }
    
    public Elasticsearch getElasticsearch() {
        return elasticsearch;
    }
    
    public void setElasticsearch(Elasticsearch elasticsearch) {
        this.elasticsearch = elasticsearch;
    }
} 