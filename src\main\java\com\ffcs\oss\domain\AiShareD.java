package com.ffcs.oss.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分享表
 */
@Data
@TableName("ai_share_d")
public class AiShareD {
    
    /**
     * 分享id
     */
    @TableId(value = "share_id", type = IdType.UUID)
    private String shareId;
    
    /**
     * 分享链接
     */
    @TableField("share_link")
    private String shareLink;
    
    /**
     * 问题id
     */
    @TableField("question_id")
    private String questionId;
    
    /**
     * 回答id
     */
    @TableField("answer_id")
    private String answerId;
    
    /**
     * 创建人
     */
    @TableField("create_user_name")
    private String createUserName;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField("update_user_name")
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 访问次数
     */
    @TableField("visit_count")
    private Integer visitCount;
    
    /**
     * 过期时间
     */
    @TableField("expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;
} 