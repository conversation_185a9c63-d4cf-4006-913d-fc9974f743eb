package com.ffcs.oss.utils.hive;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.security.UserGroupInformation;

import java.io.Closeable;
import java.io.IOException;
import java.security.PrivilegedExceptionAction;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * Hive池化连接类
 * 用于保持Hive连接和相关资源
 */
@Slf4j
public class HivePooledConnection implements Closeable {

    private static final String HIVE_DRIVER = "org.apache.hive.jdbc.HiveDriver";
    
    private final HiveConnectionParam connectionParam;
    private final UserGroupInformation ugi;
    private Connection connection;

    /**
     * 构造函数
     * 
     * @param connectionParam Hive连接参数
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     */
    public HivePooledConnection(HiveConnectionParam connectionParam) throws SQLException, IOException {
        this.connectionParam = connectionParam;
        
        // 注册驱动
        try {
            Class.forName(HIVE_DRIVER);
        } catch (ClassNotFoundException e) {
            throw new SQLException("Hive JDBC驱动未找到", e);
        }
        
        // 初始化UGI
        if (connectionParam.isKerberosEnabled()) {
            this.ugi = UserGroupInformationFactory.login(
                    connectionParam.getJavaSecurityKrb5Conf(),
                    connectionParam.getLoginUserKeytabPath(),
                    connectionParam.getLoginUserKeytabUsername());
        } else {
            this.ugi = UserGroupInformationFactory.login(connectionParam.getUsername());
        }
    }

    /**
     * 获取数据库连接
     * 
     * @return 数据库连接
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    public synchronized Connection getConnection() throws SQLException, IOException, InterruptedException {
        if (connection == null || connection.isClosed()) {
            connection = createConnection();
        }
        return connection;
    }

    /**
     * 创建数据库连接
     * 
     * @return 数据库连接
     * @throws SQLException 数据库异常
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    private Connection createConnection() throws SQLException, IOException, InterruptedException {
        try {
            return ugi.doAs((PrivilegedExceptionAction<Connection>) () ->
                    DriverManager.getConnection(
                            connectionParam.getJdbcUrl(),
                            connectionParam.getUsername(),
                            connectionParam.getPassword())
            );
        } catch (Exception e) {
            if (e instanceof SQLException) {
                throw (SQLException) e;
            } else if (e instanceof IOException) {
                throw (IOException) e;
            } else if (e instanceof InterruptedException) {
                throw (InterruptedException) e;
            } else {
                throw new SQLException("创建Hive连接失败", e);
            }
        }
    }

    /**
     * 关闭连接和资源
     */
    @Override
    public synchronized void close() {
        if (connection != null) {
            try {
                connection.close();
                log.info("关闭Hive连接成功");
            } catch (SQLException e) {
                log.error("关闭Hive连接失败", e);
            } finally {
                connection = null;
            }
        }
        
        if (ugi != null && connectionParam.isKerberosEnabled()) {
            UserGroupInformationFactory.logout(connectionParam.getLoginUserKeytabUsername());
        } else if (ugi != null) {
            UserGroupInformationFactory.logout(connectionParam.getUsername());
        }
    }
} 