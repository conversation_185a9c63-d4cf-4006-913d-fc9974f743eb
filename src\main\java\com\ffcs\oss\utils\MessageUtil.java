package com.ffcs.oss.utils;

import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * 国际化工具
 *
 * <AUTHOR> on 2022/9/2
 */
public class MessageUtil {

    /**
     * 获取国际化语言
     * @return
     */
    public static String getLocale() {
        Locale locale = LocaleContextHolder.getLocale();
//        String lang = locale.getLanguage();
        return locale.getLanguage();
    }
}
