package com.ffcs.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ffcs.oss.domain.AiQuestionD;

import java.util.List;
import java.util.Map;

/**
 * 提问Service接口
 */
public interface AiQuestionService extends IService<AiQuestionD> {
    /**
     * 获取会话历史记录（包括问题和回答）
     * @param sessionId 会话ID
     * @return 会话历史记录列表
     */
    List<Map<String, Object>> getConversationHistoryWithAnswers(String sessionId);
} 