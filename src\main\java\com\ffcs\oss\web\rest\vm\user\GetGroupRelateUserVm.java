package com.ffcs.oss.web.rest.vm.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2018/12/17  15:07
 */
@ApiModel("用户组关联用户信息")
@Alias("GetGroupRelateUserVm")
public class GetGroupRelateUserVm implements Serializable{
    private static final long serialVersionUID = -2404771166300411403L;
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "用户名")
    private String loginName;
    @ApiModelProperty(value = "用户名")
    private String loginNameBk;
    @ApiModelProperty(value = "姓名")
    private String alias;
    @ApiModelProperty(value = "姓名")
    private String aliasBk;
    @ApiModelProperty(value = "电话号码")
    private String telephone;
    @ApiModelProperty(value = "电话号码")
    private String telephoneBk;
    @ApiModelProperty(value = "组织机构ID")
    private Long orgId;
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;
    @ApiModelProperty(value = "用户状态")
    private Integer status;
    @ApiModelProperty(value = "区域")
    private String regionType;
    @ApiModelProperty(value = "是否该用户组管理员")
    private Boolean permission;

    public Boolean getPermission() {
        return permission;
    }

    public void setPermission(Boolean permission) {
        this.permission = permission;
    }

    public String getLoginNameBk() {
        return loginNameBk;
    }

    public void setLoginNameBk(String loginNameBk) {
        this.loginNameBk = loginNameBk;
    }

    public String getAliasBk() {
        return aliasBk;
    }

    public void setAliasBk(String aliasBk) {
        this.aliasBk = aliasBk;
    }

    public String getTelephoneBk() {
        return telephoneBk;
    }

    public void setTelephoneBk(String telephoneBk) {
        this.telephoneBk = telephoneBk;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRegionType() {
        return regionType;
    }

    public void setRegionType(String regionType) {
        this.regionType = regionType;
    }

    @Override
    public String toString() {
        return "GetGroupRelateUserVm{" +
            "userId=" + userId +
            ", loginName='" + loginName + '\'' +
            ", alias='" + alias + '\'' +
            ", telephone='" + telephone + '\'' +
            ", orgId=" + orgId +
            ", orgName=" + orgName +
            ", status=" + status +
            ", regionType='" + regionType + '\'' +
            '}';
    }
}
