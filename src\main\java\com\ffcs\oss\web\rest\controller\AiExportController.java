package com.ffcs.oss.web.rest.controller;

import com.ffcs.oss.domain.AiAnswerD;
import com.ffcs.oss.domain.AiQuestionD;
import com.ffcs.oss.service.AiAnswerService;
import com.ffcs.oss.service.AiQuestionService;
import com.ffcs.oss.utils.QueryResultUtil;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.utils.WordExportUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 导出控制器
 */
@RestController
@RequestMapping(value = "/api/export")
@ApiModel("导出管理")
@Slf4j
public class AiExportController {

    @Autowired
    private AiQuestionService aiQuestionService;

    @Autowired
    private AiAnswerService aiAnswerService;

    /**
     * 导出Word
     */
    @GetMapping("/exportWord")
    @ApiOperation("导出Word")
    public ResponseEntity<StreamingResponseBody> exportWord(@RequestParam("questionId") String questionId, 
                                           @RequestParam("answerId") String answerId,
                                           @RequestParam(value = "chartImage", required = false) String chartImage) {
        if (StringUtils.isBlank(questionId)) {
            return ResponseEntity.badRequest().build();
        }
        if (StringUtils.isBlank(answerId)) {
            return ResponseEntity.badRequest().build();
        }

        // 获取问题和回答
        AiQuestionD question = aiQuestionService.getById(questionId);
        AiAnswerD answer = aiAnswerService.getById(answerId);

        if (question == null || answer == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }

        try {
            // 创建Word文档
            XWPFDocument document = WordExportUtil.createDocument();
            
            // 导出文件名称
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String baseFileName = "导出文档_" + timestamp;
            final String wordFileName = baseFileName + ".docx";
            String excelFileName = "查询结果_" + timestamp + ".xlsx";
            String zipFileName = baseFileName + ".zip";
            
            // 添加标题
            WordExportUtil.addTitle(document, question.getQuestion(), 1);
    
            // 添加SQL生成部分
            if (StringUtils.isNotBlank(answer.getSql())) {
                WordExportUtil.addTitle(document, "SQL生成", 2);
                WordExportUtil.addCodeBlock(document, answer.getSql());
            }
    
            // 添加数据查询部分
            boolean hasExcelAttachment = false;
            if (StringUtils.isNotBlank(answer.getDataSearch())) {
                WordExportUtil.addTitle(document, "数据查询", 2);
                
                // 解析查询结果并添加简要信息
                List<Map<String, Object>> queryResult = QueryResultUtil.parseQueryResult(answer.getDataSearch());
                if (!queryResult.isEmpty()) {
                    // 添加简要描述
                    WordExportUtil.addParagraph(document, "查询结果包含 " + queryResult.size() + " 条记录");
                    
                    // 生成Excel附件并在Word中添加链接
                    hasExcelAttachment = WordExportUtil.addExcelAttachment(document, queryResult, excelFileName);
                } else {
                    WordExportUtil.addParagraph(document, answer.getDataSearch());
                }
            }
    
            // 添加图表数据部分
            if (StringUtils.isNotBlank(chartImage) || StringUtils.isNotBlank(answer.getExtraData())) {
                WordExportUtil.addTitle(document, "图表数据", 2);
                
                // 如果前端传入了图表图片，则优先使用
                if (StringUtils.isNotBlank(chartImage)) {
                    WordExportUtil.addBase64Image(document, chartImage, 500, 300);
                } 
                // 如果有存储的图表数据，则尝试作为图像处理
//                else if (StringUtils.isNotBlank(answer.getExtraData())) {
//                    if (answer.getExtraData().startsWith("data:image") ||
//                        answer.getExtraData().matches("^[A-Za-z0-9+/=]+$")) {
//                        // 看起来是Base64编码的图片
//                        WordExportUtil.addBase64Image(document, answer.getExtraData(), 500, 300);
//                    } else {
//                        // 否则作为文本添加
//                        WordExportUtil.addParagraph(document, answer.getExtraData());
//                    }
//                }
            }
    
            // 添加结果总结部分
            if (StringUtils.isNotBlank(answer.getResult())) {
                WordExportUtil.addTitle(document, "结果总结", 2);
                WordExportUtil.addParagraph(document, answer.getResult());
            }
    
            // 导出Word文档为字节数组
            final byte[] wordBytes = WordExportUtil.saveToByteArray(document);
            final boolean finalHasExcelAttachment = hasExcelAttachment;
            final String finalExcelFileName = excelFileName;
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFileName = URLEncoder.encode(zipFileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
//            headers.setContentDisposition(ContentDisposition.attachment().filename(encodedFileName).build());
            headers.setContentDisposition(
                    ContentDisposition.builder("attachment").filename(encodedFileName).build()
            );
            
            // 检测是否在容器环境中
            boolean isContainer = new File("/.dockerenv").exists() || 
                new File("/proc/1/cgroup").exists() && 
                containsDocker(new File("/proc/1/cgroup")) || 
                System.getenv("DOCKER_CONTAINER") != null;
            
            log.info("当前运行环境: {}", isContainer ? "容器环境" : "标准环境");
            log.info("导出文件: Word={}, Excel/CSV={}, ZIP={}", wordFileName, excelFileName, zipFileName);
            
            // 创建ZIP流作为响应
            StreamingResponseBody responseBody = outputStream -> {
                try (ZipOutputStream zipOut = new ZipOutputStream(outputStream)) {
                    // 添加Word文档到ZIP
                    ZipEntry wordEntry = new ZipEntry(wordFileName);
                    zipOut.putNextEntry(wordEntry);
                    zipOut.write(wordBytes);
                    zipOut.closeEntry();
                    log.info("已将Word文件添加到ZIP包");
                    
                    // 添加CSV文件
                    if (finalHasExcelAttachment) {
                        // 始终使用CSV文件
                        String tempDir = System.getProperty("java.io.tmpdir");
                        String csvFileName = finalExcelFileName.replace(".xlsx", ".csv");
                        String csvPath = tempDir + "/" + csvFileName;
                        File csvFile = new File(csvPath);
                        
                        log.info("检查CSV文件: {}, 是否存在: {}", csvPath, csvFile.exists());
                        
                        if (csvFile.exists()) {
                            try {
                                ZipEntry csvEntry = new ZipEntry(csvFileName);
                                zipOut.putNextEntry(csvEntry);
                                Files.copy(csvFile.toPath(), zipOut);
                                zipOut.closeEntry();
                                log.info("已将CSV文件添加到ZIP包: {}, 大小: {}字节", csvFileName, csvFile.length());
                            } catch (Exception e) {
                                log.error("将CSV文件添加到ZIP包时出错: {}", e.getMessage(), e);
                            } finally {
                                // 删除临时文件
                                boolean deleted = csvFile.delete();
                                log.info("删除临时CSV文件: {}, 结果: {}", csvPath, deleted);
                            }
                        } else {
                            // 尝试在备用目录查找CSV文件
                            String backupDir = "./temp";
                            String backupCsvPath = backupDir + "/" + csvFileName;
                            File backupCsvFile = new File(backupCsvPath);
                            
                            log.info("检查备用路径的CSV文件: {}, 是否存在: {}", backupCsvPath, backupCsvFile.exists());
                            
                            if (backupCsvFile.exists()) {
                                try {
                                    ZipEntry csvEntry = new ZipEntry(csvFileName);
                                    zipOut.putNextEntry(csvEntry);
                                    Files.copy(backupCsvFile.toPath(), zipOut);
                                    zipOut.closeEntry();
                                    log.info("已将备用路径的CSV文件添加到ZIP包: {}, 大小: {}字节", csvFileName, backupCsvFile.length());
                                } catch (Exception e) {
                                    log.error("将备用路径的CSV文件添加到ZIP包时出错: {}", e.getMessage(), e);
                                } finally {
                                    // 删除临时文件
                                    boolean deleted = backupCsvFile.delete();
                                    log.info("删除备用路径的临时CSV文件: {}, 结果: {}", backupCsvPath, deleted);
                                }
                            } else {
                                log.error("未找到CSV文件: 请检查临时目录权限和系统配置");
                            }
                        }
                    } else {
                        log.warn("没有需要导出的数据文件");
                    }
                } catch (Exception e) {
                    log.error("创建ZIP文件失败: {}", e.getMessage(), e);
                }
            };
            
            return new ResponseEntity<>(responseBody, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("导出文档失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 导出Word（POST方法，支持复杂参数）
     */
    @PostMapping("/exportWordPost")
    @ApiOperation("导出Word（POST方法）")
    public ResponseEntity<StreamingResponseBody> exportWordPost(
            @RequestParam("questionId") String questionId,
            @RequestParam("answerId") String answerId,
            @RequestBody(required = false) ExportWordRequest request) {
        
        String chartImage = request != null ? request.getChartImage() : null;
        return exportWord(questionId, answerId, chartImage);
    }
    
    /**
     * 导出Word（含图片二进制流）
     */
    @PostMapping("/exportWordWithImage")
    @ApiOperation("导出Word（含图表图片二进制流）")
    public ResponseEntity<StreamingResponseBody> exportWordWithImage(
            @RequestParam("questionId") String questionId,
            @RequestParam("answerId") String answerId,
            @RequestParam(value = "chartImage", required = false) MultipartFile chartImage) {
        
        if (StringUtils.isBlank(questionId)) {
            return ResponseEntity.badRequest().build();
        }
        if (StringUtils.isBlank(answerId)) {
            return ResponseEntity.badRequest().build();
        }

        // 获取问题和回答
        AiQuestionD question = aiQuestionService.getById(questionId);
        AiAnswerD answer = aiAnswerService.getById(answerId);

        if (question == null || answer == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }

        try {
            // 创建Word文档
            XWPFDocument document = WordExportUtil.createDocument();
            
            // 导出文件名称
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String baseFileName = "导出文档_" + timestamp;
            String wordFileName = baseFileName + ".docx";
            String excelFileName = "查询结果_" + timestamp + ".xlsx";
            String zipFileName = baseFileName + ".zip";
    
            // 添加标题
            WordExportUtil.addTitle(document, question.getQuestion(), 1);
    
            // 添加SQL生成部分
            if (StringUtils.isNotBlank(answer.getSql())) {
                WordExportUtil.addTitle(document, "SQL生成", 2);
                WordExportUtil.addCodeBlock(document, answer.getSql());
            }
    
            // 添加数据查询部分
            boolean hasExcelAttachment = false;
            if (StringUtils.isNotBlank(answer.getDataSearch())) {
                WordExportUtil.addTitle(document, "数据查询", 2);
                
                // 解析查询结果并添加简要信息
                List<Map<String, Object>> queryResult = QueryResultUtil.parseQueryResult(answer.getDataSearch());
                if (!queryResult.isEmpty()) {
                    // 添加简要描述
                    WordExportUtil.addParagraph(document, "查询结果包含 " + queryResult.size() + " 条记录");
                    
                    // 生成Excel附件并在Word中添加链接
                    hasExcelAttachment = WordExportUtil.addExcelAttachment(document, queryResult, excelFileName);
                } else {
                    WordExportUtil.addParagraph(document, answer.getDataSearch());
                }
            }
    
            // 添加图表数据部分
            WordExportUtil.addTitle(document, "图表数据", 2);
                
            // 处理二进制流图片
            if (chartImage != null && !chartImage.isEmpty()) {
                try {
                    byte[] imageBytes = chartImage.getBytes();
                    WordExportUtil.addImage(document, imageBytes, 500, 300);
                    WordExportUtil.addParagraph(document, "图表已插入文档。");
                } catch (IOException e) {
                    log.error("处理图片数据失败", e);
                    WordExportUtil.addParagraph(document, "图片处理失败：" + e.getMessage());
                }
            } 
            // 如果没有上传图片，尝试使用存储的数据
//            else if (StringUtils.isNotBlank(answer.getExtraData())) {
//                if (answer.getExtraData().startsWith("data:image") ||
//                    answer.getExtraData().matches("^[A-Za-z0-9+/=]+$")) {
//                    // 看起来是Base64编码的图片
//                    WordExportUtil.addBase64Image(document, answer.getExtraData(), 500, 300);
//                } else {
//                    // 否则作为文本添加
//                    WordExportUtil.addParagraph(document, answer.getExtraData());
//                }
//            }
            else {
                WordExportUtil.addParagraph(document, "未提供图表数据。");
            }
    
            // 添加结果总结部分
            if (StringUtils.isNotBlank(answer.getResult())) {
                WordExportUtil.addTitle(document, "结果总结", 2);
                WordExportUtil.addParagraph(document, answer.getResult());
            }
    
            // 导出Word文档为字节数组
            final byte[] wordBytes = WordExportUtil.saveToByteArray(document);
            final boolean finalHasExcelAttachment = hasExcelAttachment;
            final String finalExcelFileName = excelFileName;
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFileName = URLEncoder.encode(zipFileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
//            headers.setContentDisposition(ContentDisposition.attachment().filename(encodedFileName).build());
            headers.setContentDisposition(
                    ContentDisposition.builder("attachment").filename(encodedFileName).build()
            );
            
            // 检测是否在容器环境中
            boolean isContainer = new File("/.dockerenv").exists() || 
                new File("/proc/1/cgroup").exists() && 
                containsDocker(new File("/proc/1/cgroup")) || 
                System.getenv("DOCKER_CONTAINER") != null;
            
            log.info("当前运行环境: {}", isContainer ? "容器环境" : "标准环境");
            log.info("导出文件: Word={}, Excel/CSV={}, ZIP={}", wordFileName, excelFileName, zipFileName);
            
            // 创建ZIP流作为响应
            StreamingResponseBody responseBody = outputStream -> {
                try (ZipOutputStream zipOut = new ZipOutputStream(outputStream)) {
                    // 添加Word文档到ZIP
                    ZipEntry wordEntry = new ZipEntry(wordFileName);
                    zipOut.putNextEntry(wordEntry);
                    zipOut.write(wordBytes);
                    zipOut.closeEntry();
                    log.info("已将Word文件添加到ZIP包");
                    
                    // 添加CSV文件
                    if (finalHasExcelAttachment) {
                        // 始终使用CSV文件
                        String tempDir = System.getProperty("java.io.tmpdir");
                        String csvFileName = finalExcelFileName.replace(".xlsx", ".csv");
                        String csvPath = tempDir + "/" + csvFileName;
                        File csvFile = new File(csvPath);
                        
                        log.info("检查CSV文件: {}, 是否存在: {}", csvPath, csvFile.exists());
                        
                        if (csvFile.exists()) {
                            try {
                                ZipEntry csvEntry = new ZipEntry(csvFileName);
                                zipOut.putNextEntry(csvEntry);
                                Files.copy(csvFile.toPath(), zipOut);
                                zipOut.closeEntry();
                                log.info("已将CSV文件添加到ZIP包: {}, 大小: {}字节", csvFileName, csvFile.length());
                            } catch (Exception e) {
                                log.error("将CSV文件添加到ZIP包时出错: {}", e.getMessage(), e);
                            } finally {
                                // 删除临时文件
                                boolean deleted = csvFile.delete();
                                log.info("删除临时CSV文件: {}, 结果: {}", csvPath, deleted);
                            }
                        } else {
                            // 尝试在备用目录查找CSV文件
                            String backupDir = "./temp";
                            String backupCsvPath = backupDir + "/" + csvFileName;
                            File backupCsvFile = new File(backupCsvPath);
                            
                            log.info("检查备用路径的CSV文件: {}, 是否存在: {}", backupCsvPath, backupCsvFile.exists());
                            
                            if (backupCsvFile.exists()) {
                                try {
                                    ZipEntry csvEntry = new ZipEntry(csvFileName);
                                    zipOut.putNextEntry(csvEntry);
                                    Files.copy(backupCsvFile.toPath(), zipOut);
                                    zipOut.closeEntry();
                                    log.info("已将备用路径的CSV文件添加到ZIP包: {}, 大小: {}字节", csvFileName, backupCsvFile.length());
                                } catch (Exception e) {
                                    log.error("将备用路径的CSV文件添加到ZIP包时出错: {}", e.getMessage(), e);
                                } finally {
                                    // 删除临时文件
                                    boolean deleted = backupCsvFile.delete();
                                    log.info("删除备用路径的临时CSV文件: {}, 结果: {}", backupCsvPath, deleted);
                                }
                            } else {
                                log.error("未找到CSV文件: 请检查临时目录权限和系统配置");
                            }
                        }
                    } else {
                        log.warn("没有需要导出的数据文件");
                    }
                } catch (Exception e) {
                    log.error("创建ZIP文件失败: {}", e.getMessage(), e);
                }
            };
            
            return new ResponseEntity<>(responseBody, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("导出文档失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 导出请求对象
     */
    public static class ExportWordRequest {
        @ApiParam(value = "图表图片（Base64编码）")
        private String chartImage;

        public String getChartImage() {
            return chartImage;
        }

        public void setChartImage(String chartImage) {
            this.chartImage = chartImage;
        }
    }
    
    /**
     * 检查文件内容是否包含Docker或Kubernetes相关信息
     */
    private boolean containsDocker(File file) {
        try {
            List<String> lines = Files.readAllLines(file.toPath());
            for (String line : lines) {
                if (line.contains("docker") || line.contains("kubepods")) {
                    return true;
                }
            }
        } catch (IOException e) {
            // 忽略错误
        }
        return false;
    }
} 