package com.ffcs.oss.service;

import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.web.rest.evt.dbconnection.DbConnectionConfigEvt;
import com.ffcs.oss.web.rest.vm.dbconnection.DbConnectionConfigVm;

/**
 * 数据库连接配置服务接口
 */
public interface DbConnectionService {

    /**
     * 获取数据库连接配置列表
     *
     * @param evt
     * @return
     */
    QueryPageVm<DbConnectionConfigVm> getDbConnectionList(DbConnectionConfigEvt evt);

    /**
     * 增加或修改数据库连接配置
     *
     * @param evt
     * @return
     */
    ServiceResp addOrUpdateDbConnection(DbConnectionConfigEvt evt);

    /**
     * 删除数据库连接配置
     *
     * @param evt
     * @return
     */
    ServiceResp deleteDbConnection(DbConnectionConfigEvt evt);

    /**
     * 测试数据库连接
     *
     * @param evt
     * @return
     */
    ServiceResp testDbConnection(DbConnectionConfigEvt evt);

    /**
     * 检查数据源是否被助理引用
     * 包括直接引用和通过数据模型间接引用
     *
     * @param connectionId 数据源ID
     * @return 如果被引用返回true，否则返回false
     */
    boolean checkConnectionReferenceByAssistant(String connectionId);
}