server:
  port: 8080

spring:
  application:
    name: ai-chat-bi-web
  datasource:
    url: **************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver

# 数据源配置
datasource:
  # Hive配置
  hive:
    # 是否启用Kerberos认证
    kerberos-enabled: true
    # Kerberos principal
    principal: hive/<EMAIL>
    # Kerberos keytab文件路径
    keytab-path: /etc/kerberos/hive.keytab
  
  # Elasticsearch配置
  elasticsearch:
    # Elasticsearch版本，支持7或8
    version: 7

# MyBatis配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.ffcs.oss.domain
  configuration:
    map-underscore-to-camel-case: true

# 日志配置
logging:
  level:
    root: INFO
    com.ffcs.oss: DEBUG 