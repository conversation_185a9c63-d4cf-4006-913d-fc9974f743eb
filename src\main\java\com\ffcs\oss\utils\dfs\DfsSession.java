package com.ffcs.oss.utils.dfs;

import java.io.File;
import java.io.InputStream;

/**
 * 分布式文件系统会话接口
 */
public interface DfsSession {

    /**
     * 会话建立连接
     */
    void connect();

    /**
     * 销毁会话
     */
    void destroy();

    /**
     * 判断文件是否存在
     *
     * @param filename 文件名
     * @return 存在返回true, 否则返回false
     */
    boolean exists(String filename);
    
    /**
     * 从服务器下载文件
     *
     * @param remoteFilename 需要下载的文件名
     * @param localFile      下载到本地文件
     */
    void downloadFile(String remoteFilename, File localFile);

    /**
     * 从服务器下载文件流
     *
     * @param remoteFilename 远程文件名
     * @return 文件输入流
     */
    InputStream downloadStream(String remoteFilename);

    /**
     * 创建文件到服务器
     *
     * @param localFile      本地需要上传的文件
     * @param remoteFilename 上传到dfs中目录路径
     */
    void uploadFile(File localFile, String remoteFilename);

    /**
     * 创建文件流上传到dfs服务器
     *
     * @param remoteFilename 上传到dfs中目录路径
     * @param inputStream    文件输入流
     * @return 是否上传成功
     */
    Boolean uploadFile(String remoteFilename, InputStream inputStream);

    /**
     * 删除dfs文件
     *
     * @param filename 需要删除的文件
     */
    void deleteFile(String filename);
    
    /**
     * 创建目录
     *
     * @param dir 目录路径
     */
    void mkDirs(String dir);
} 