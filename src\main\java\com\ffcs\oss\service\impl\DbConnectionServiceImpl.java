package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiAssistantD;
import com.ffcs.oss.domain.AiDataModelD;
import com.ffcs.oss.domain.AiDbConnectionConfigD;
import com.ffcs.oss.mapper.AiAssistantMapper;
import com.ffcs.oss.mapper.AiDataModelMapper;
import com.ffcs.oss.mapper.dbconnection.DbConnectionConfigMapper;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.service.AiPermissionConfigService;
import com.ffcs.oss.service.DbConnectionService;
import com.ffcs.oss.service.UserService;
import com.ffcs.oss.utils.Asserts;
import com.ffcs.oss.utils.FileStorageUtil;
import com.ffcs.oss.utils.JasyptEncryptorUtils;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.utils.db.SqlExecutor;
import com.ffcs.oss.utils.db.SqlExecutorFactory;
import com.ffcs.oss.utils.hive.HiveConnectionClient;
import com.ffcs.oss.web.rest.constant.CommonConstant;
import com.ffcs.oss.web.rest.constant.DbConnectionConstant;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.dbconnection.DbConnectionConfigEvt;
import com.ffcs.oss.web.rest.vm.dbconnection.DbConnectionConfigVm;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据库连接配置服务实现类
 */
@Service
public class DbConnectionServiceImpl implements DbConnectionService {

    public static final String AUTH_TYPE_KERBEROS = "kerberos";
    private static final Logger logger = LoggerFactory.getLogger(DbConnectionServiceImpl.class);
    
    @Resource
    private DbConnectionConfigMapper dbConnectionConfigMapper;

    @Resource
    private AiPermissionConfigService permissionConfigService;

    @Autowired
    private AiAssistantMapper aiAssistantMapper;

    @Autowired
    private AiDataModelMapper aiDataModelMapper;
    @Autowired
    private UserService userService;
    
    @Override
    public QueryPageVm<DbConnectionConfigVm> getDbConnectionList(DbConnectionConfigEvt evt) {
        if (evt.getPageNo() != null && evt.getPageSize() != null && evt.isCountTotal()) {
            String currentUserName = userService.getCurrentUserName();

            // 先获取所有记录（不分页）用于权限过滤和计数
            List<DbConnectionConfigVm> allConnections = dbConnectionConfigMapper.getDbConnectionList(evt);

            // 权限过滤
            List<DbConnectionConfigVm> filteredList = allConnections.stream()
                    .filter(conn -> permissionConfigService.hasUsePermission(
                            DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                            String.valueOf(conn.getConnectionId()),
                            currentUserName))
                    .peek(conn -> conn.setIsAdmin(
                            permissionConfigService.hasAdminPermission(
                                    DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                                    String.valueOf(conn.getConnectionId()),
                                    evt.getCreatorName())))
                    .collect(Collectors.toList());

            if (evt.getPageNo() != null && evt.getPageSize() != null) {
                // 手动分页
                int total = filteredList.size();
                int fromIndex = (evt.getPageNo() - 1) * evt.getPageSize();
                int toIndex = Math.min(fromIndex + evt.getPageSize(), total);
                List<DbConnectionConfigVm> pageList = filteredList.subList(fromIndex, toIndex);

                return QueryPageVm.getInstance(evt, pageList, (long)total);
            } else {
                return QueryPageVm.getInstance(evt, filteredList, (long)filteredList.size());
            }
        } else {
            List<DbConnectionConfigVm> dbConnectionList = dbConnectionConfigMapper.getDbConnectionList(evt);
            String currentUserName = userService.getCurrentUserName();

            Iterator<DbConnectionConfigVm> iterator = dbConnectionList.iterator();
            while (iterator.hasNext()) {
                DbConnectionConfigVm dbConnectionConfigVm = iterator.next();
                boolean result = permissionConfigService.hasUsePermission(
                        DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                        String.valueOf(dbConnectionConfigVm.getConnectionId()),
                        currentUserName
                );
                if (!result) {
                    iterator.remove();
                }
                boolean isAdmin = permissionConfigService.hasAdminPermission(
                        DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                        String.valueOf(dbConnectionConfigVm.getConnectionId()),
                        evt.getCreatorName()
                );
                dbConnectionConfigVm.setIsAdmin(isAdmin);
            }
            
            return QueryPageVm.getInstance(evt, dbConnectionList, 0L);
        }
    }
    
    /**
     * 移除敏感信息
     * 
     * @param vm 数据库连接配置视图模型
     */
    private void removeSensitiveInfo(DbConnectionConfigVm vm) {
        if (vm != null) {
            vm.setUsername(null);
            vm.setPassword(null);
            // 不返回Kerberos相关的文件内容
            // 但保留文件名和是否有文件的标志
        }
    }
    
    @Override
    public ServiceResp addOrUpdateDbConnection(DbConnectionConfigEvt evt) {
        AiDbConnectionConfigD config = new AiDbConnectionConfigD();
        BeanUtils.copyProperties(evt, config);
        
        // 使用专用ENC格式加密工具类加密密码
        if (evt.getPassword() != null && !evt.getPassword().isEmpty()) {
            if (evt.getPassword().contains("ENC")){
                config.setPassword(evt.getPassword());
            }else{
                config.setPassword(JasyptEncryptorUtils.encrypt(evt.getPassword()));
            }
        } else {
            // 如果密码为空，设置默认密码
            config.setPassword(JasyptEncryptorUtils.encrypt("default_password"));
            logger.debug("设置默认加密密码");
        }
        
        // 确保username不为空
        if (config.getUsername() == null || config.getUsername().isEmpty()) {
            // 根据认证类型设置默认用户名
            if (DbConnectionConstant.AUTH_TYPE_KERBEROS.equals(config.getAuthType())) {
                // 如果是Kerberos认证，尝试从Principal中提取
                if (config.getPrincipal() != null && !config.getPrincipal().isEmpty()) {
                    try {
                        String[] parts = config.getPrincipal().split("@")[0].split("/");
                        config.setUsername(parts[0]);
                        logger.debug("从principal({})中提取用户名: {}", config.getPrincipal(), config.getUsername());
                    } catch (Exception e) {
                        logger.warn("无法从principal({})中提取用户名", config.getPrincipal(), e);
                        config.setUsername("hiveuser");
                    }
                } else {
                    config.setUsername("hiveuser");
                }
            } else {
                // 对于其他认证类型，使用default
                config.setUsername("default");
            }
            logger.debug("设置默认用户名: {}", config.getUsername());
        }
        
        // 处理Kerberos认证相关文件
        if (DbConnectionConstant.AUTH_TYPE_KERBEROS.equals(evt.getAuthType())) {
            try {
                // 处理keytab文件
                if (evt.getKeytabFile() != null && !evt.getKeytabFile().isEmpty()) {
                    // 保存文件内容到数据库
//                    config.setKeytabFile(evt.getKeytabFile().getBytes());
                    config.setKeytabFilename(evt.getKeytabFile().getOriginalFilename());
                    
                    // 保存文件到磁盘或小文件系统
                    String connectionId = evt.getConnectionId() != null ? evt.getConnectionId().toString() : "new";
                    String keytabFilePath = FileStorageUtil.saveKeytabFile(evt.getKeytabFile(), connectionId);
                    config.setKeytabFilePath(keytabFilePath);
                    logger.info("保存keytab文件: {}", keytabFilePath);
                }
                
                // 处理krb5.conf文件
                if (evt.getKrb5ConfFile() != null && !evt.getKrb5ConfFile().isEmpty()) {
                    // 保存文件内容到数据库
//                    config.setKrb5ConfFile(evt.getKrb5ConfFile().getBytes());
                    config.setKrb5ConfFilename(evt.getKrb5ConfFile().getOriginalFilename());
                    
                    // 保存文件到磁盘或小文件系统
                    String connectionId = evt.getConnectionId() != null ? evt.getConnectionId().toString() : "new";
                    String krb5ConfFilePath = FileStorageUtil.saveKrb5ConfFile(evt.getKrb5ConfFile(), connectionId);
                    config.setKrb5ConfFilePath(krb5ConfFilePath);
                    logger.info("保存krb5.conf文件: {}", krb5ConfFilePath);
                }
            } catch (IOException e) {
                logger.error("处理Kerberos文件失败", e);
                return ServiceResp.getInstance().error("处理Kerberos文件失败: " + e.getMessage());
            }
        }
        
        Date now = new Date();
        Integer addOrUpdateNum;
        
        if (evt.getConnectionId() == null) {
            // 新增时连接名称不能重复
            Asserts.isTrue(dbConnectionConfigMapper.nameWhetherRepeat(evt) == 0, 
                    LocalMessageTool.getMessage(LocaleKeyConstant.NAME_CANNOT_REPEATED));
            
            config.setCreateTime(now);
            config.setUpdateTime(now);
            config.setCreatorName(userService.getCurrentUserName());
            addOrUpdateNum = dbConnectionConfigMapper.insert(config);
            
            // 如果是新增，且保存了文件路径，但connectionId是"new"，需要更新文件路径中的connectionId
            if (addOrUpdateNum > 0 && config.getConnectionId() != null) {
                try {
                    updateFilePathsWithCorrectConnectionId(config, "new", config.getConnectionId().toString());
                } catch (IOException e) {
                    logger.error("更新文件路径失败", e);
                    // 不中断流程，继续执行
                }
            }
            
            // 返回新创建的连接ID，用于创建权限记录
            if (addOrUpdateNum > 0) {
                return ServiceResp.getInstance().success(config.getConnectionId(), 
                        LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
            }
        } else {
            // 如果是更新，需要处理文件字段的特殊情况
            AiDbConnectionConfigD oldConfig = dbConnectionConfigMapper.selectById(evt.getConnectionId());
            if (oldConfig != null) {
                // 如果没有上传新的keytab文件，保留原来的文件
                if (evt.getKeytabFile() == null || evt.getKeytabFile().isEmpty()) {
                    config.setKeytabFile(oldConfig.getKeytabFile());
                    config.setKeytabFilename(oldConfig.getKeytabFilename());
                    config.setKeytabFilePath(oldConfig.getKeytabFilePath());
                }
                
                // 如果没有上传新的krb5.conf文件，保留原来的文件
                if (evt.getKrb5ConfFile() == null || evt.getKrb5ConfFile().isEmpty()) {
                    config.setKrb5ConfFile(oldConfig.getKrb5ConfFile());
                    config.setKrb5ConfFilename(oldConfig.getKrb5ConfFilename());
                    config.setKrb5ConfFilePath(oldConfig.getKrb5ConfFilePath());
                }
            }
            
            config.setUpdateTime(now);
            if (StringUtils.isBlank(oldConfig.getCreatorName())){
                config.setCreatorName(userService.getCurrentUserName());
            }
            addOrUpdateNum = dbConnectionConfigMapper.updateById(config);
            
            if (addOrUpdateNum > 0) {
                return ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
            }
        }
        
        return ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }
    
    /**
     * 更新文件路径中的connectionId
     * 
     * @param config 数据库连接配置
     * @param oldConnectionId 旧的connectionId
     * @param newConnectionId 新的connectionId
     * @throws IOException IO异常
     */
    private void updateFilePathsWithCorrectConnectionId(AiDbConnectionConfigD config, String oldConnectionId, String newConnectionId) throws IOException {
        // 更新keytab文件路径
        if (config.getKeytabFilePath() != null && config.getKeytabFilePath().contains(oldConnectionId)) {
            // 首先，删除旧文件
//            FileStorageUtil.deleteFile(config.getKeytabFilePath());
            
            // 然后，使用新的connectionId保存文件
            String newKeytabFilePath = FileStorageUtil.saveKeytabFile(
                    config.getKeytabFile(), config.getKeytabFilename(), newConnectionId);
            
            // 更新配置
            config.setKeytabFilePath(newKeytabFilePath);
            
            // 更新数据库
            dbConnectionConfigMapper.updateById(config);
            
            logger.info("更新keytab文件路径: {} -> {}", config.getKeytabFilePath(), newKeytabFilePath);
        }
        
        // 更新krb5.conf文件路径
        if (config.getKrb5ConfFilePath() != null && config.getKrb5ConfFilePath().contains(oldConnectionId)) {
            // 首先，删除旧文件
//            FileStorageUtil.deleteFile(config.getKrb5ConfFilePath());
            
            // 然后，使用新的connectionId保存文件
            String newKrb5ConfFilePath = FileStorageUtil.saveKrb5ConfFile(
                    config.getKrb5ConfFile(), config.getKrb5ConfFilename(), newConnectionId);
            
            // 更新配置
            config.setKrb5ConfFilePath(newKrb5ConfFilePath);
            
            // 更新数据库
            dbConnectionConfigMapper.updateById(config);
            
            logger.info("更新krb5.conf文件路径: {} -> {}", config.getKrb5ConfFilePath(), newKrb5ConfFilePath);
        }
    }
    
    @Override
    public ServiceResp deleteDbConnection(DbConnectionConfigEvt evt) {
        // 首先获取连接配置，以便删除相关文件
        AiDbConnectionConfigD config = dbConnectionConfigMapper.selectById(evt.getConnectionId());
        
        // 执行删除操作
        int deleteNum = dbConnectionConfigMapper.deleteById(evt.getConnectionId());
        
        // 如果删除成功，同时删除相关文件
        if (deleteNum > 0 && config != null) {
            // 删除keytab文件
            if (config.getKeytabFilePath() != null && !config.getKeytabFilePath().isEmpty()) {
                FileStorageUtil.deleteFile(config.getKeytabFilePath());
                logger.info("删除keytab文件: {}", config.getKeytabFilePath());
            }
            
            // 删除krb5.conf文件
            if (config.getKrb5ConfFilePath() != null && !config.getKrb5ConfFilePath().isEmpty()) {
                FileStorageUtil.deleteFile(config.getKrb5ConfFilePath());
                logger.info("删除krb5.conf文件: {}", config.getKrb5ConfFilePath());
            }
        }
        
        return deleteNum > 0 ?
                ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS)) :
                ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }
    
    @Override
    public ServiceResp testDbConnection(DbConnectionConfigEvt evt) {
        try {
            // 如果是Hive连接且使用Kerberos认证，使用HiveConnectionClient
            if (DbConnectionConstant.DB_TYPE_HIVE.equals(evt.getDbType()) && 
                    DbConnectionConstant.AUTH_TYPE_KERBEROS.equals(evt.getAuthType())) {
                
                boolean connected = HiveConnectionClient.testConnection(
                        evt.getUrl(),
                        evt.getUsername(),
                        evt.getPassword(),
                        evt.getPrincipal(),
                        evt.getKeytabFile(),
                        evt.getKrb5ConfFile(),
                        null,  // keytabFilePath
                        null   // krb5ConfFilePath
                );
                
                if (connected) {
                    return ServiceResp.getInstance().success("Hive连接测试成功");
                } else {
                    return ServiceResp.getInstance().error("Hive连接测试失败");
                }
            } 
            // 否则使用普通的SqlExecutor
            else {
                SqlExecutor executor = SqlExecutorFactory.createSqlExecutor(
                        evt.getDbType(),
                        evt.getHost(),
                        evt.getPort(),
                        evt.getDatabaseName(),
                        evt.getUsername(),
                        evt.getPassword(),
                        evt.getParams(),
                        evt.getDbVersion()
                );
                
                boolean success = executor.testConnection();
                executor.close();
                
                if (success) {
                    return ServiceResp.getInstance().success("数据库连接测试成功");
                } else {
                    return ServiceResp.getInstance().error("数据库连接测试失败");
                }
            }
        } catch (Exception e) {
            logger.error("测试数据库连接失败", e);
            return ServiceResp.getInstance().error("数据库连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据源是否被助理引用
     * 包括直接引用和通过数据模型间接引用
     *
     * @param connectionId 数据源ID
     * @return 如果被引用返回true，否则返回false
     */
    @Override
    public boolean checkConnectionReferenceByAssistant(String connectionId) {
        if (StringUtils.isBlank(connectionId)) {
            return false;
        }

        try {
            // 1. 检查助理是否直接引用该数据源
            LambdaQueryWrapper<AiAssistantD> assistantQuery = new LambdaQueryWrapper<>();
            assistantQuery.eq(AiAssistantD::getConnectionId, Long.valueOf(connectionId));

            if (aiAssistantMapper.selectCount(assistantQuery) > 0) {
                logger.info("数据源{}被助理直接引用", connectionId);
                return true;
            }

            // 2. 检查是否有数据模型引用该数据源，且该数据模型被助理引用
            LambdaQueryWrapper<AiDataModelD> dataModelQuery = new LambdaQueryWrapper<>();
            dataModelQuery.eq(AiDataModelD::getConnectionId, Long.valueOf(connectionId));
            List<AiDataModelD> dataModels = aiDataModelMapper.selectList(dataModelQuery);

            for (AiDataModelD dataModel : dataModels) {
                // 检查该数据模型是否被助理引用
                LambdaQueryWrapper<AiAssistantD> assistantByModelQuery = new LambdaQueryWrapper<>();
                assistantByModelQuery.eq(AiAssistantD::getDataModelId, dataModel.getDataModelId());

                if (aiAssistantMapper.selectCount(assistantByModelQuery) > 0) {
                    logger.info("数据源{}通过数据模型{}被助理间接引用", connectionId, dataModel.getDataModelId());
                    return true;
                }
            }

            return false;
        } catch (NumberFormatException e) {
            logger.error("数据源ID格式错误: {}", connectionId, e);
            return false;
        } catch (Exception e) {
            logger.error("检查数据源引用时发生错误", e);
            return false;
        }
    }
}