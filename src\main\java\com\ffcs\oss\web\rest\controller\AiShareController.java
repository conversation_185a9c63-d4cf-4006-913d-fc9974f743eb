package com.ffcs.oss.web.rest.controller;

import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.security.SecurityUtils;
import com.ffcs.oss.service.AiShareService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.evt.share.ShareEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 分享控制器
 */
@RestController
@RequestMapping(value = "/api/share")
@ApiModel("分享管理")
public class AiShareController {

    @Autowired
    private AiShareService aiShareService;

    /**
     * 创建分享
     */
    @PostMapping("/createShare")
    @ApiOperation("创建分享")
    public ServiceResp createShare(@RequestBody ShareEvt evt) {
        if (StringUtils.isBlank(evt.getQuestionId())) {
            return ServiceResp.getInstance().error("问题ID不能为空");
        }
        if (StringUtils.isBlank(evt.getAnswerId())) {
            return ServiceResp.getInstance().error("回答ID不能为空");
        }
        
        // 获取当前用户
        String currentUserName = StringUtils.isNotBlank(PtSecurityUtils.getUsername()) ? 
                PtSecurityUtils.getUsername() : 
                StringUtils.isNotBlank(SecurityUtils.getCurrentUserLogin().get()) ? 
                        SecurityUtils.getCurrentUserLogin().get() : "";
        
        // 创建分享
        String shareLink = aiShareService.createShare(evt.getQuestionId(), evt.getAnswerId(), currentUserName);
        
        if (StringUtils.isBlank(shareLink)) {
            return ServiceResp.getInstance().error("创建分享失败");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("shareLink", shareLink);
        
        return ServiceResp.getInstance().success(result, "已复制分享链接");
    }
    
    /**
     * 获取分享详情
     */
    @GetMapping("/getShareDetail/{shareId}")
    @ApiOperation("获取分享详情")
    public ServiceResp getShareDetail(@PathVariable("shareId") String shareId) {
        if (StringUtils.isBlank(shareId)) {
            return ServiceResp.getInstance().error("分享ID不能为空");
        }
        
        // 增加访问次数
        aiShareService.increaseVisitCount(shareId);
        
        // 获取分享详情
        Map<String, Object> detail = aiShareService.getShareDetail(shareId);
        
        if (detail.isEmpty()) {
            return ServiceResp.getInstance().error("分享不存在");
        }
        
        if (detail.containsKey("expired") && (Boolean) detail.get("expired")) {
            return ServiceResp.getInstance().error("分享已过期");
        }
        
        return ServiceResp.getInstance().success(detail, LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }
} 