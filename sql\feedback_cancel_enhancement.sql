-- 点赞点踩取消功能增强SQL脚本
-- 确保相关表结构支持取消功能

-- 1. 确保ai_answer_d表存在并包含必要字段
-- 检查like_or_no_like_result字段是否存在
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'ai_answer_d' 
        AND column_name = 'like_or_no_like_result'
    ) THEN
        ALTER TABLE ai_answer_d ADD COLUMN like_or_no_like_result VARCHAR(2) DEFAULT '0';
        COMMENT ON COLUMN ai_answer_d.like_or_no_like_result IS '1点赞|2点踩|0无';
    END IF;
END $$;

-- 2. 确保ai_audit_list_d表存在并包含必要字段
-- 检查audit_status字段是否存在
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'ai_audit_list_d' 
        AND column_name = 'audit_status'
    ) THEN
        ALTER TABLE ai_audit_list_d ADD COLUMN audit_status VARCHAR(2) DEFAULT '0';
        COMMENT ON COLUMN ai_audit_list_d.audit_status IS '审核状态 0待审核|1已审核';
    END IF;
END $$;

-- 检查answer_id字段是否存在
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'ai_audit_list_d' 
        AND column_name = 'answer_id'
    ) THEN
        ALTER TABLE ai_audit_list_d ADD COLUMN answer_id VARCHAR(32);
        COMMENT ON COLUMN ai_audit_list_d.answer_id IS '答案ID';
    END IF;
END $$;

-- 检查feed_back_user字段是否存在
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'ai_audit_list_d' 
        AND column_name = 'feed_back_user'
    ) THEN
        ALTER TABLE ai_audit_list_d ADD COLUMN feed_back_user VARCHAR(128);
        COMMENT ON COLUMN ai_audit_list_d.feed_back_user IS '反馈用户';
    END IF;
END $$;

-- 检查like_or_no_like_result字段是否存在
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'ai_audit_list_d' 
        AND column_name = 'like_or_no_like_result'
    ) THEN
        ALTER TABLE ai_audit_list_d ADD COLUMN like_or_no_like_result VARCHAR(2);
        COMMENT ON COLUMN ai_audit_list_d.like_or_no_like_result IS '1点赞|2点踩|0无';
    END IF;
END $$;

-- 3. 创建索引以提高查询性能
-- 为取消功能相关的查询创建索引
CREATE INDEX IF NOT EXISTS idx_audit_answer_user ON ai_audit_list_d (answer_id, feed_back_user);
CREATE INDEX IF NOT EXISTS idx_audit_status ON ai_audit_list_d (audit_status);
CREATE INDEX IF NOT EXISTS idx_answer_like_status ON ai_answer_d (like_or_no_like_result);

-- 4. 添加约束确保数据一致性
-- 确保like_or_no_like_result字段只能是0、1、2
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_answer_like_status'
    ) THEN
        ALTER TABLE ai_answer_d ADD CONSTRAINT chk_answer_like_status 
        CHECK (like_or_no_like_result IN ('0', '1', '2'));
    END IF;
END $$;

-- 确保audit_status字段只能是0、1
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_audit_status'
    ) THEN
        ALTER TABLE ai_audit_list_d ADD CONSTRAINT chk_audit_status 
        CHECK (audit_status IN ('0', '1'));
    END IF;
END $$;

-- 确保审核表的like_or_no_like_result字段只能是0、1、2
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_audit_like_status'
    ) THEN
        ALTER TABLE ai_audit_list_d ADD CONSTRAINT chk_audit_like_status 
        CHECK (like_or_no_like_result IN ('0', '1', '2'));
    END IF;
END $$;

-- 5. 创建用于查询的视图（可选）
-- 创建一个视图来方便查询用户的反馈状态
CREATE OR REPLACE VIEW v_user_feedback_status AS
SELECT 
    a.answer_id,
    a.question_id,
    a.session_id,
    a.like_or_no_like_result,
    a.create_user_name as answer_creator,
    audit.feed_back_user,
    audit.audit_status,
    audit.feed_back_time,
    CASE 
        WHEN audit.audit_status = '1' THEN false
        WHEN audit.audit_status = '0' THEN true
        ELSE true
    END as can_cancel
FROM ai_answer_d a
LEFT JOIN ai_audit_list_d audit ON a.answer_id = audit.answer_id
WHERE a.like_or_no_like_result != '0';

COMMENT ON VIEW v_user_feedback_status IS '用户反馈状态视图，包含是否可以取消的信息';

-- 6. 创建函数来检查是否可以取消反馈（可选）
CREATE OR REPLACE FUNCTION can_cancel_feedback(
    p_answer_id VARCHAR(32),
    p_user_id VARCHAR(128)
) RETURNS BOOLEAN AS $$
DECLARE
    v_audit_status VARCHAR(2);
BEGIN
    -- 查询审核状态
    SELECT audit_status INTO v_audit_status
    FROM ai_audit_list_d
    WHERE answer_id = p_answer_id 
    AND feed_back_user = p_user_id;
    
    -- 如果没有审核记录，可以取消
    IF v_audit_status IS NULL THEN
        RETURN TRUE;
    END IF;
    
    -- 如果审核状态为待审核(0)，可以取消；如果已审核(1)，不能取消
    RETURN v_audit_status = '0';
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION can_cancel_feedback(VARCHAR, VARCHAR) IS '检查用户是否可以取消对指定回答的反馈';

-- 7. 数据迁移（如果需要）
-- 如果有历史数据需要处理，可以在这里添加数据迁移脚本
-- 例如：将空的like_or_no_like_result设置为'0'
UPDATE ai_answer_d SET like_or_no_like_result = '0' 
WHERE like_or_no_like_result IS NULL OR like_or_no_like_result = '';

UPDATE ai_audit_list_d SET audit_status = '0' 
WHERE audit_status IS NULL OR audit_status = '';

-- 8. 创建触发器确保数据一致性（可选）
-- 当删除审核记录时，确保对应的回答状态也被重置
CREATE OR REPLACE FUNCTION trigger_reset_answer_feedback()
RETURNS TRIGGER AS $$
BEGIN
    -- 当删除审核记录时，将对应回答的反馈状态重置为0
    IF TG_OP = 'DELETE' THEN
        UPDATE ai_answer_d 
        SET like_or_no_like_result = '0',
            update_time = CURRENT_TIMESTAMP
        WHERE answer_id = OLD.answer_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS tr_reset_answer_feedback ON ai_audit_list_d;
CREATE TRIGGER tr_reset_answer_feedback
    AFTER DELETE ON ai_audit_list_d
    FOR EACH ROW
    EXECUTE FUNCTION trigger_reset_answer_feedback();

COMMENT ON TRIGGER tr_reset_answer_feedback ON ai_audit_list_d IS '删除审核记录时自动重置回答的反馈状态';
