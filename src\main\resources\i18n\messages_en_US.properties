test=\u672C\u57302-message-2
match.words.cannot.repeated= entry phrases cannot be repeated
params.cannot.null=param cannot null
export.serial.number=test the source
rule.title.cannot.repeated=rule title cannot repeated

#\u679A\u4E3E\u503C
enum.yesno.no=N
enum.yesno.yes=Y
#\u679A\u4E3E\u7C7B\u4E2D\u663E\u793A\u5B57\u6BB5
enum.status.enabled=enabled
enum.status.disabled=disabled
#log info \u64CD\u4F5C\u5F02\u5E38,\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
log.operate.fail=Abnormal operation, please contact the administrator
#\u51FD\u6570\u540D\u3010{}\u3011\u5165\u53C2\u4FE1\u606F\u3010{}\u3011
log.output.param=Function name [{}] Input parameter information [{}]
#\u51FD\u6570\u540D\u3010{}\u3011\u5165\u53C2\u4FE1\u606F\u3010{}\u3011\u5F02\u5E38\u4FE1\u606F:
log.output.excep=Function name [{}] Input parameter information [{}] Exception information:
#common
operate.success=Successful operation
operate.exception=Abnormal operation
operate.fail=Abnormal failed
query.success=Query successful
query.exception=Query exception
common.param.error=input error
common.param.empty={0} cannot be empty
#cs-sm-qry-srv
menu.name.exist=menu name already exists
menu.code.exist=menu code already exists
menu.permission.code.exist=The function authorization code already exists
menu.check.pass=Verified
ctgCache.init.fail=Failed to obtain CTG-CACH instance
user.not.exist=User does not exist
user.userIdAndUsername.not.both.blank=User id and username cannot be empty at the same time
#portal-service
pwd.oldPwd.empty=old password is empty
pwd.newPwd.empty=new password is empty
pwd.oldNewPwd.same=old and new passwords are the same
pwd.oldPwd.error=wrong old password
pwd.used.repeat=This password has been used within {} times
login.usernameOrPwd.empty=Username or password cannot be empty
login.usernameOrPwdOrCode.empty=Username, password and verification code cannot be empty
login.phoneOrCode.empty=Mobile number verification code cannot be empty
login.usernameOrPwd.error=User deprecated or wrong username and password
login.phone.error=wrong mobile number
login.code.empty=verification code cannot be empty
login.code.error=Verification code error
login.msg.lock=User is locked
login.msg.loginTimeout=The account is locked if the user has not logged in for a long time
login.msg.pwdTimeout=The account is locked if the user changes the password for a long time
login.msg.accountEffective=The account validity period has expired The account is locked
login.msg.loginErrorLimi=The number of login errors reaches the limit and the account is locked
login.msg.cancel=Account cancellation
login.msg.userDisabeld=Account not available
login.msg.userChecked=Account pending
login.msg.updPwd=update password
login.username.empty=Username is empty
login.get.phone.first=Please get the verification code first
login.code.valid=The verification code is still valid
login.code.invalid=The verification code has expired, please get the verification code again
login.code.sms.send=Sent to your mobile phone, please check
login.code.sms.error=The SMS is sent abnormally, please use the account password to log in
registry.loginName.repeat=Username already exists
registry.loginName.legal=Username can only contain numbers, letters, and underscores
registry.phone.illegal=Incorrect phone number
registry.phone.null=phone number is empty
registry.phone.exist=Phone number already exists
registry.email.illegal=Email is incorrect
registry.pid.illegal=ID card format is incorrect
registry.code.sms.illegal=Incorrect username or mobile number
registry.unlock.status.error=The account is not locked
registry.forget.pwd.status.error=The account does not exist or is unavailable
#oss-setting-server
catalog.delete.to.belong=Please go to the directory to delete the article
log.for.login=Login log
log.for.system=System log
log.for.operate=Operation log
export.all.icon.info=All icon data
export.select.icon.info=selected icon data
online.user.info=Online User Information
role.delError.bind.userOrGroup=Deletion failed, the role has bound users or user groups
role.dirName.exist=directory name already exists
role.name.exist=role name already exists
role.roleIdEqualsParentId=The directory you belong to cannot select the currently edited directory itself, please reselect the directory tree
group.name.exist=User group name already exists
scene-config-info.not.search=The scene configuration information is found

#smart-workbench-web
no.shift.leader.shift=There is no shift leader for this shift, unable to evaluate
more.than.one.shif=This shift has more than one shift leader, unable to evaluate
not.the.monitor.not.be.evaluated=only the shift leader can evaluate it!
shift.leader.unable.evaluate.himself=The shift leader was unable to evaluate himself
evaluate.success=Evaluate successful
evaluate.exception=Evaluate exception


primarykey.id.cannot.be.empty=primarykey id cannot be empty
information.pushed.cannot.modified=the information has been pushed and cannot be modified
information.pushed.all=the information has been pushed by all staff
information.pushed.cannot.pushed.further=the information has been pushed and cannot be pushed further
not.creator.administrator.without.permission=Not the creator or super administrator, without permission
please.upload.the.necessary.parameters=Please upload the necessary parameters
no.such.plan=No such plan
mobile.number.added.cannot.added.repeatedly=The mobile number has been added and cannot be added repeatedly
previously.added=previously added
operate.zero.records=operate zero records
name.cannot.repeated=name.cannot.repeated
check.exception=check exception
no.permission=no permission