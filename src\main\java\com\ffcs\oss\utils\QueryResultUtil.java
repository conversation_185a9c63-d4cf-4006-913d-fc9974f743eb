package com.ffcs.oss.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * SQL查询结果处理工具类
 */
@Slf4j
public class QueryResultUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON格式的查询结果转换为List<Map<String, Object>>格式
     * 
     * @param jsonResult JSON格式的查询结果
     * @return List<Map<String, Object>> 格式的查询结果
     */
    public static List<Map<String, Object>> parseQueryResult(String jsonResult) {
        if (StringUtils.isBlank(jsonResult)) {
            return Collections.emptyList();
        }
        
        try {
            return objectMapper.readValue(jsonResult, new TypeReference<List<Map<String, Object>>>() {});
        } catch (JsonProcessingException e) {
            log.error("解析查询结果失败", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 将查询结果转换为二维数组格式（用于表格展示）
     *
     * @param queryResult 查询结果
     * @return 二维数组格式的数据
     */
    public static String[][] convertToTableData(List<Map<String, Object>> queryResult) {
        if (queryResult == null || queryResult.isEmpty()) {
            return new String[0][0];
        }
        
        // 获取列名
        Set<String> columnNames = queryResult.get(0).keySet();
        String[] columns = columnNames.toArray(new String[0]);
        
        // 创建表格数据
        String[][] tableData = new String[queryResult.size() + 1][columns.length];
        
        // 添加表头
        for (int i = 0; i < columns.length; i++) {
            tableData[0][i] = columns[i];
        }
        
        // 添加数据行
        for (int i = 0; i < queryResult.size(); i++) {
            Map<String, Object> row = queryResult.get(i);
            for (int j = 0; j < columns.length; j++) {
                Object value = row.get(columns[j]);
                tableData[i + 1][j] = value != null ? value.toString() : "";
            }
        }
        
        return tableData;
    }
    
    /**
     * 从查询结果中提取特定列的数据
     *
     * @param queryResult 查询结果
     * @param columnName 列名
     * @return 列数据
     */
    public static List<Object> extractColumnData(List<Map<String, Object>> queryResult, String columnName) {
        if (queryResult == null || queryResult.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<Object> columnData = new ArrayList<>();
        for (Map<String, Object> row : queryResult) {
            columnData.add(row.get(columnName));
        }
        
        return columnData;
    }
} 