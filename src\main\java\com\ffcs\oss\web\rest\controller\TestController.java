package com.ffcs.oss.web.rest.controller;

import com.ffcs.oss.param.out.ServiceResp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2023/8/2 13:44
 */
@RestController
@RequestMapping(value = "/api/test")
public class TestController {
    @PostMapping("/testController")
    public ServiceResp testController(){
        return ServiceResp.getInstance().success("调用成功");
    }
}
