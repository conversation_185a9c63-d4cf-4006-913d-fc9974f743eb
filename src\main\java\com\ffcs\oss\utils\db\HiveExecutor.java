package com.ffcs.oss.utils.db;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.UUID;

/**
 * Hive SQL执行器实现
 */
public class HiveExecutor extends AbstractSqlExecutor {
    
    private static final Logger logger = LoggerFactory.getLogger(HiveExecutor.class);
    
    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private final String password;
    private final String params;
    
    // Kerberos配置
    private final boolean kerberosEnabled;
    private final String kerberosPrincipal;
    private final String kerberosKeytabPath;
    private final byte[] keytabFileContent;
    private final byte[] krb5ConfFileContent;
    
    // 临时文件路径
    private String tempKeytabPath;
    private String tempKrb5ConfPath;
    
    public HiveExecutor(String host, int port, String database, String username, String password, String params) {
        this(host, port, database, username, password, params, false, null, null);
    }
    
    public HiveExecutor(String host, int port, String database, String username, String password, String params,
                        boolean kerberosEnabled, String kerberosPrincipal, String kerberosKeytabPath) {
        this(host, port, database, username, password, params, kerberosEnabled, kerberosPrincipal, kerberosKeytabPath, null, null);
    }
    
    public HiveExecutor(String host, int port, String database, String username, String password, String params,
                        boolean kerberosEnabled, String kerberosPrincipal, String kerberosKeytabPath,
                        byte[] keytabFileContent, byte[] krb5ConfFileContent) {
        this.host = host;
        this.port = port;
        this.database = database;
        this.username = username;
        this.password = password;
        this.params = params;
        this.kerberosEnabled = kerberosEnabled;
        this.kerberosPrincipal = kerberosPrincipal;
        this.kerberosKeytabPath = kerberosKeytabPath;
        this.keytabFileContent = keytabFileContent;
        this.krb5ConfFileContent = krb5ConfFileContent;
    }
    
    @Override
    protected Connection createConnection() throws SQLException {
        try {
            // 加载Hive驱动
            Class.forName("org.apache.hive.jdbc.HiveDriver");
            
            // 设置Kerberos认证
            if (kerberosEnabled) {
                try {
                    setupKerberosAuthentication();
                } catch (IOException e) {
                    throw new SQLException("Kerberos认证设置失败", e);
                }
            }
            
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append("jdbc:hive2://").append(host);
            
            // 添加数据库名
            if (database != null && !database.isEmpty()) {
                urlBuilder.append("/").append(database);
            }
            
            // 如果启用了Kerberos，添加Kerberos认证参数
            if (kerberosEnabled) {
                if (StringUtils.hasText(kerberosPrincipal)) {
                    urlBuilder.append(";principal=").append(kerberosPrincipal);
                }
                urlBuilder.append(";auth=kerberos");
            } 
            // 否则使用普通的用户名密码认证
            else if (username != null && !username.isEmpty()) {
                urlBuilder.append(";user=").append(username);
                if (password != null && !password.isEmpty()) {
                    urlBuilder.append(";password=").append(password);
                }
            }
            
            // 添加其他参数
            if (params != null && !params.isEmpty()) {
                if (!params.startsWith(";")) {
                    urlBuilder.append(";");
                }
                urlBuilder.append(params);
            }
            
            // 创建连接并返回
            String url = urlBuilder.toString();
            logger.debug("Hive连接URL: {}", url);
            return DriverManager.getConnection(url);
        } catch (ClassNotFoundException e) {
            throw new SQLException("Hive JDBC驱动未找到", e);
        }
    }
    
    /**
     * 设置Kerberos认证
     */
    private void setupKerberosAuthentication() throws IOException {
        // 如果提供了文件内容，则创建临时文件
        if (keytabFileContent != null && keytabFileContent.length > 0) {
            tempKeytabPath = createTempFile("keytab", keytabFileContent);
        }
        
        if (krb5ConfFileContent != null && krb5ConfFileContent.length > 0) {
            tempKrb5ConfPath = createTempFile("krb5.conf", krb5ConfFileContent);
            System.setProperty("java.security.krb5.conf", tempKrb5ConfPath);
        } else {
            // 使用默认的krb5.conf路径
            System.setProperty("java.security.krb5.conf", "/etc/krb5.conf");
        }
        
        // 验证Kerberos参数
        String principal = kerberosPrincipal;
        String keytabPath = tempKeytabPath != null ? tempKeytabPath : kerberosKeytabPath;
        
        if (!StringUtils.hasText(principal) || !StringUtils.hasText(keytabPath)) {
            throw new IllegalArgumentException("Kerberos认证参数不完整");
        }
        
        // 设置Kerberos认证
        Configuration conf = new Configuration();
        conf.set("hadoop.security.authentication", "kerberos");
        UserGroupInformation.setConfiguration(conf);
        
        // 使用keytab认证
        UserGroupInformation.loginUserFromKeytab(principal, keytabPath);
        logger.info("Kerberos认证成功，principal={}", principal);
    }
    
    /**
     * 创建临时文件并写入内容
     * 
     * @param prefix 文件前缀
     * @param content 文件内容
     * @return 临时文件路径
     * @throws IOException IO异常
     */
    private String createTempFile(String prefix, byte[] content) throws IOException {
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("hive-kerberos-");
        
        // 创建临时文件
        String fileName = prefix + "-" + UUID.randomUUID().toString();
        File tempFile = new File(tempDir.toFile(), fileName);
        
        // 写入文件内容
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(content);
        }
        
        // 设置文件权限
        tempFile.setReadable(true, true);
        tempFile.setWritable(true, true);
        
        // 返回文件路径
        return tempFile.getAbsolutePath();
    }
    
    @Override
    public void close() {
        super.close();
        
        // 清理临时文件
        cleanupTempFiles();
    }
    
    /**
     * 清理临时文件
     */
    private void cleanupTempFiles() {
        try {
            if (tempKeytabPath != null) {
                Files.deleteIfExists(Paths.get(tempKeytabPath));
            }
            
            if (tempKrb5ConfPath != null) {
                Files.deleteIfExists(Paths.get(tempKrb5ConfPath));
            }
        } catch (IOException e) {
            logger.warn("清理临时文件失败", e);
        }
    }
} 