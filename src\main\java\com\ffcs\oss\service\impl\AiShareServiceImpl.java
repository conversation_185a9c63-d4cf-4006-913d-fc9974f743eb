package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.domain.AiAnswerD;
import com.ffcs.oss.domain.AiQuestionD;
import com.ffcs.oss.domain.AiShareD;
import com.ffcs.oss.mapper.AiShareMapper;
import com.ffcs.oss.service.AiAnswerService;
import com.ffcs.oss.service.AiQuestionService;
import com.ffcs.oss.service.AiShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 分享Service实现类
 */
@Service
public class AiShareServiceImpl extends ServiceImpl<AiShareMapper, AiShareD> implements AiShareService {

    @Autowired
    private AiQuestionService aiQuestionService;

    @Autowired
    private AiAnswerService aiAnswerService;
    
    @Value("${server.servlet.context-path:/}")
    private String contextPath;
    
    @Value("${share.base-url:http://localhost:8080}")
    private String baseUrl;
    
    @Value("${share.expire-days:30}")
    private Integer expireDays;

    /**
     * 创建分享
     * @param questionId 问题ID
     * @param answerId 回答ID
     * @param username 用户名
     * @return 分享链接
     */
    @Override
    public String createShare(String questionId, String answerId, String username) {
        // 创建分享记录
        AiShareD share = new AiShareD();
        share.setShareId(UUID.randomUUID().toString().replace("-", ""));
        share.setQuestionId(questionId);
        share.setAnswerId(answerId);
        share.setCreateUserName(username);
        share.setUpdateUserName(username);
        share.setCreateTime(LocalDateTime.now());
        share.setUpdateTime(LocalDateTime.now());
        share.setVisitCount(0);
        // 设置过期时间
        share.setExpireTime(LocalDateTime.now().plusDays(expireDays));
        
        // 生成分享链接
        String shareLink = baseUrl;
        if (!contextPath.equals("/")) {
            shareLink += contextPath;
        }
        if (!shareLink.endsWith("/")) {
            shareLink += "/";
        }
        shareLink += "share/" + share.getShareId();
        
        share.setShareLink(shareLink);
        
        // 保存分享记录
        this.save(share);
        
        return shareLink;
    }
    
    /**
     * 获取分享详情
     * @param shareId 分享ID
     * @return 分享详情（包含问题和回答）
     */
    @Override
    public Map<String, Object> getShareDetail(String shareId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取分享记录
        AiShareD share = this.getById(shareId);
        if (share == null) {
            return result;
        }
        
        // 检查是否过期
        if (share.getExpireTime() != null && share.getExpireTime().isBefore(LocalDateTime.now())) {
            result.put("expired", true);
            return result;
        }
        
        result.put("share", share);
        
        // 获取问题
        AiQuestionD question = aiQuestionService.getById(share.getQuestionId());
        if (question != null) {
            result.put("question", question);
        }
        
        // 获取回答
        AiAnswerD answer = aiAnswerService.getById(share.getAnswerId());
        if (answer != null) {
            result.put("answer", answer);
        }
        
        return result;
    }
    
    /**
     * 增加访问次数
     * @param shareId 分享ID
     * @return 是否成功
     */
    @Override
    public boolean increaseVisitCount(String shareId) {
        AiShareD share = this.getById(shareId);
        if (share == null) {
            return false;
        }
        
        share.setVisitCount(share.getVisitCount() + 1);
        share.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(share);
    }
} 