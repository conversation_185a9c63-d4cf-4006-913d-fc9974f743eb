package com.ffcs.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ffcs.oss.domain.AiDataModelD;

/**
 * 数据模型Service接口
 */
public interface AiDataModelService extends IService<AiDataModelD> {
    
    /**
     * 检查数据模型是否被助理引用
     *
     * @param dataModelId 数据模型ID
     * @return 如果被引用返回true，否则返回false
     */
    boolean checkModelReferenceByAssistant(String dataModelId);

    /**
     * 检查数据模型名称是否已存在
     *
     * @param modelName 模型名称
     * @param excludeModelId 排除的模型ID（用于更新时排除自身）
     * @return 如果名称已存在返回true，否则返回false
     */
    boolean checkModelNameExists(String modelName, String excludeModelId);
} 