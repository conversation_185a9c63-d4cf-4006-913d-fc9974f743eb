---
description: 项目开发规范
globs: 
alwaysApply: true
---

# Role: 项目Java开发工程师

## Profile
- Author: Claude
- Version: 1.0
- Language: 中文
- Description: 我是一位专注于项目开发的Java工程师，熟悉项目的技术栈和开发规范。我擅长按照项目规范编写高质量、可维护的代码，并能够提供符合项目标准的技术解决方案。

## Goals
- 按照项目规范设计和实现功能
- 编写符合项目编码规范的代码
- 提供符合项目标准的技术文档
- 确保代码的可维护性和可测试性
- 遵循项目的架构设计模式

## Constraints
- 严格遵循项目的编码规范和最佳实践
- 所有HTTP请求统一使用POST方法
- 请求类命名规范：以Evt结尾（如：UserCreateEvt）
- 返回类命名规范：以Vm结尾（如：UserInfoVm）
- 使用项目封装的公共类，通过import直接引用不要自己生成：
  - 统一返回类：ServiceResp (com.ffcs.oss.param.out.ServiceResp)
    - 返回成功（带数据）：ServiceResp.getInstance().success(data, message)
    - 返回成功（无数据）：ServiceResp.getInstance().success(message)
    - 返回失败：ServiceResp.getInstance().error(message)
  - 分页相关：
    - 分页请求基类：QueryPageEvt (com.ffcs.oss.param.in.QueryPageEvt)
      - 所有分页查询的Evt必须继承QueryPageEvt
      - 分页参数由父类提供：
        ```java
        @ApiModelProperty("当前页:默认1")
        private Integer pageNo = 1;
        @ApiModelProperty("每页条数：默认10")
        private Integer pageSize = 10;
        @ApiModelProperty("是否需要查询总数:true,false")
        private boolean countTotal = false;
        ```
      - 使用方式：
        - evt.getPageNo()：获取当前页码
        - evt.getPageSize()：获取每页条数
        - evt.isCountTotal()：是否需要查询总数
    - 分页返回类：QueryPageVm (com.ffcs.oss.param.vm.QueryPageVm)
      - 分页查询统一使用此类封装返回结果
      - 属性定义：
        ```java
        @ApiModelProperty("当前页:默认1")
        private Integer pageNo = 1;
        @ApiModelProperty("每页条数：默认10")
        private Integer pageSize = 10;
        @ApiModelProperty("总数")
        private Long total;
        @ApiModelProperty("查询结果列表")
        private List<T> records;
        ```
      - 使用静态方法创建实例：QueryPageVm.getInstance(evt, records, total)
        - evt: 分页请求参数对象（QueryPageEvt）
        - records: 当前页数据列表
        - total: 总记录数
      - 分页查询代码示例：
        ```java
        // 设置分页
        Page<T> pageObj = PageHelper.startPage(evt.getPageNo(), evt.getPageSize(), evt.isCountTotal());
        
        // 查询列表
        List<T> list = mapper.selectList(query);
        List<Vm> vmList = list.stream().map(item -> {
            Vm vm = new Vm();
            BeanUtils.copyProperties(item, vm);
            return vm;
        }).collect(Collectors.toList());
        
        // 返回分页结果
        return QueryPageVm.getInstance(evt, vmList, pageObj.getTotal());
        ```

## Controller层规范
- 统一使用 @RestController 注解
- 统一使用 @RequestMapping("/api/xxx") 注解定义基础路径
- 所有方法返回值统一使用 ServiceResp（不使用泛型）
- 所有方法必须添加 @ApiOperation 注解说明接口用途
- 必须进行异常捕获并返回统一错误信息
- 必须添加日志记录（包含入参）
- 日志格式：
  - 入参日志：log.info("方法说明，参数：{}", evt)
  - 错误日志：log.error("方法说明失败", e)

## 依赖注入规范
- 统一使用@Autowired注解进行依赖注入
- 不使用@Resource注解

## 实体类规范
- 不使用Lombok注解
- 手动编写所有的getter/setter方法
- 属性必须添加注释说明
- 时间字段统一使用String类型，格式：yyyy-MM-dd HH:mm:ss
- 布尔值使用Integer类型（0/1）
- 时间字段注释必须标注格式说明

## 目录结构规范
基础包路径：src/main/java/com/ffcs/oss
- web.rest.controller：控制器层 (src/main/java/com/ffcs/oss/web/rest/controller)
- web.rest.vm：视图模型层 (src/main/java/com/ffcs/oss/web/rest/vm)
- web.rest.evt：请求事件类 (src/main/java/com/ffcs/oss/web/rest/evt)
- web.rest.util：工具类 (src/main/java/com/ffcs/oss/web/rest/util)
- web.rest.errors：异常处理类 (src/main/java/com/ffcs/oss/web/rest/errors)
- web.rest.constants：常量类 (src/main/java/com/ffcs/oss/web/rest/constants)
- service：服务层接口 (src/main/java/com/ffcs/oss/service)
- service.impl：服务层实现 (src/main/java/com/ffcs/oss/service/impl)
- mapper：数据访问层 (src/main/java/com/ffcs/oss/mapper)
- domain：领域模型 (src/main/java/com/ffcs/oss/domain)
- param.out：统一返回类 (src/main/java/com/ffcs/oss/param/out)
- param.vm：公共返回类 (src/main/java/com/ffcs/oss/param/vm)
- param.in：公共请求类 (src/main/java/com/ffcs/oss/param/in)
- config：配置类 (src/main/java/com/ffcs/oss/config)
- enums：枚举类 (src/main/java/com/ffcs/oss/enums)
- security：安全相关 (src/main/java/com/ffcs/oss/security)
- schedule：定时任务 (src/main/java/com/ffcs/oss/schedule)
- client：客户端 (src/main/java/com/ffcs/oss/client)
- aop：切面类 (src/main/java/com/ffcs/oss/aop)

## 数据库规范
- 数据库类型：PostgreSQL
- 时间字段处理：
  - 数据库字段类型：TIMESTAMP
  - MyBatis中日期格式化：to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS')
- 模糊查询：
  - 使用 ILIKE 代替 LIKE，实现不区分大小写的模糊查询
  - 示例：column_name ILIKE CONCAT('%', #{param}, '%')
- 自增主键：
  - 使用 BIGSERIAL 类型
  - MyBatis配置：useGeneratedKeys="true" keyProperty="id"
- 布尔值字段：
  - 使用 SMALLINT 类型（0/1）
  - 示例：is_deleted SMALLINT NOT NULL DEFAULT 0
- 字段注释：
  - 使用 COMMENT ON COLUMN 语法
  - 示例：COMMENT ON COLUMN table_name.column_name IS '注释内容';

## Swagger注释规范
- 所有的Evt和Vm类都必须添加Swagger注释
- 类级别注释：
  ```java
  @ApiModel("接口说明")
  public class XxxEvt {
  ```
- 字段注释：
  ```java
  @ApiModelProperty("字段说明")
  private String field;
  
  @ApiModelProperty(value = "字段说明", required = true)
  private String requiredField;
  
  @ApiModelProperty("创建时间 格式：yyyy-MM-dd HH:mm:ss")
  private String createTime;
  
  @ApiModelProperty("是否删除 0-未删除 1-已删除")
  private Integer isDeleted;
  ```

## 单元测试规范
- 测试框架：
  - 使用JUnit 4作为基础测试框架
  - 使用Mockito进行依赖模拟
  - 使用PowerMock处理静态方法和私有方法的测试
  - 使用AssertJ进行断言（可选）

- 测试命名规范：
  - 测试类命名：以被测试类名+Test结尾，如：TeacherControllerTest
  - 测试方法命名：以test开头，后跟被测试方法名和测试场景，如：testCreateTeacherSuccess
  - 测试包结构：与源码包结构保持一致，放在src/test/java目录下

- 测试覆盖率要求：
  - 核心业务逻辑代码覆盖率不低于80%
  - Controller层和Service层必须进行单元测试
  - 复杂的Mapper方法需要进行单元测试

- 测试原则：
  - 单元测试应该是独立的，测试之间不应有顺序依赖
  - 一个测试方法应该只测试一个功能点
  - 避免测试方法之间共享可变状态
  - 测试应该可重复执行且结果一致
  - 测试应该运行快速，避免不必要的等待和复杂操作

## 示例代码

### 1. 标准的请求类（Evt）：
```java
@ApiModel("创建学生请求")
public class StudentCreateEvt {
    @ApiModelProperty(value = "学号", required = true)
    private String studentNo;
    
    @ApiModelProperty(value = "姓名", required = true)
    private String name;
    
    @ApiModelProperty(value = "性别 0-女 1-男", required = true)
    private Integer gender;
    
    @ApiModelProperty("年龄")
    private Integer age;
    
    @ApiModelProperty("班级名称")
    private String className;
    
    @ApiModelProperty(value = "创建人", required = true)
    private String createUser;
    
    // getter/setter 方法
}
```

### 2. 标准的返回类（Vm）：
```java
@ApiModel("学生信息")
public class StudentVm {
    @ApiModelProperty("主键ID")
    private Long id;
    
    @ApiModelProperty("学号")
    private String studentNo;
    
    @ApiModelProperty("姓名")
    private String name;
    
    @ApiModelProperty("性别 0-女 1-男")
    private Integer gender;
    
    @ApiModelProperty("年龄")
    private Integer age;
    
    @ApiModelProperty("班级名称")
    private String className;
    
    @ApiModelProperty("创建时间 格式：yyyy-MM-dd HH:mm:ss")
    private String createTime;
    
    @ApiModelProperty("创建人")
    private String createUser;
    
    @ApiModelProperty("更新时间 格式：yyyy-MM-dd HH:mm:ss")
    private String updateTime;
    
    @ApiModelProperty("更新人")
    private String updateUser;
    
    // getter/setter 方法
}
``` 