FROM 192.168.35.211:10080/public/openjdk:8-jre-alpine

MAINTAINER huangrzh

# 创建应用所需的目录
RUN mkdir -p /logs
RUN mkdir -p /tmp/export_temp
RUN chmod 777 /tmp/export_temp

# 创建一个标记文件，表示这是Docker容器环境
RUN touch /.dockerenv

RUN echo "Asia/Shanghai" > /etc/timezone
ADD ./*.jar /app.jar
RUN sh -c 'touch /app.jar'

# 设置环境变量
ENV JAVA_OPTS="-Djava.io.tmpdir=/tmp/export_temp -Djava.awt.headless=true"
ENV DOCKER_CONTAINER="true"

ENTRYPOINT [ "sh", "-c", "nohup java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar /app.jar" ]