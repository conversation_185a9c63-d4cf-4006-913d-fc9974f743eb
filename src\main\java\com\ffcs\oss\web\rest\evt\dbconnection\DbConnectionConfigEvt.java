package com.ffcs.oss.web.rest.evt.dbconnection;


import com.ffcs.oss.param.in.QueryPageEvt;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * 数据库连接配置Event
 */
public class DbConnectionConfigEvt extends QueryPageEvt implements Serializable {
    
    /**
     * 连接ID
     */
    private BigInteger connectionId;
    
    /**
     * 连接名称
     */
    private String connectionName;
    
    /**
     * 数据库类型
     */
    private String dbType;
    
    /**
     * 数据库版本
     */
    private String dbVersion;
    
    /**
     * 认证类型
     */
    private String authType;
    
    /**
     * 主机地址
     */
    private String host;
    
    /**
     * 端口号
     */
    private Integer port;
    
    /**
     * 数据库名
     */
    private String databaseName;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * Kerberos principal
     */
    private String principal;
    
    /**
     * Kerberos keytab文件
     */
    private MultipartFile keytabFile;
    
    /**
     * krb5.conf文件
     */
    private MultipartFile krb5ConfFile;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 其他连接参数
     */
    private String params;
    
    /**
     * 关键字搜索
     */
    private String keyword;
    
    /**
     * 当前操作用户名
     */
    private String creatorName;
    
    /**
     * 数据库连接URL
     */
    private String url;

    public BigInteger getConnectionId() {
        return connectionId;
    }

    public void setConnectionId(BigInteger connectionId) {
        this.connectionId = connectionId;
    }

    public String getConnectionName() {
        return connectionName;
    }

    public void setConnectionName(String connectionName) {
        this.connectionName = connectionName;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }
    
    public String getDbVersion() {
        return dbVersion;
    }

    public void setDbVersion(String dbVersion) {
        this.dbVersion = dbVersion;
    }
    
    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getDatabaseName() {
        return databaseName;
    }

    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public MultipartFile getKeytabFile() {
        return keytabFile;
    }

    public void setKeytabFile(MultipartFile keytabFile) {
        this.keytabFile = keytabFile;
    }

    public MultipartFile getKrb5ConfFile() {
        return krb5ConfFile;
    }

    public void setKrb5ConfFile(MultipartFile krb5ConfFile) {
        this.krb5ConfFile = krb5ConfFile;
    }
    
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public String getCreatorName() {
        return creatorName;
    }
    
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
} 